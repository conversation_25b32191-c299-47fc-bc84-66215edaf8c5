{"name": "vscode-api-tests", "description": "API tests for JoyCode", "version": "0.0.1", "publisher": "vscode", "license": "MIT", "enabledApiProposals": ["activeComment", "authSession", "chatParticipantPrivate", "chat<PERSON>rovider", "contribStatusBarItems", "contribViewsRemote", "customEditorMove", "defaultChatParticipant", "diffCommand", "documentFiltersExclusive", "editorInsets", "embeddings", "extensionRuntime", "extensionsAny", "externalUriOpener", "fileSearchProvider", "findFiles2", "findTextInFiles", "fsChunks", "interactive", "languageStatusText", "nativeWindowHandle", "notebookCellExecutionState", "notebookDeprecated", "notebookLiveShare", "notebookMessaging", "notebookMime", "portsAttributes", "quickPickSortByLabel", "resolvers", "scmActionButton", "scmSelectedProvider", "scmTextDocument", "scmValidation", "taskPresentationGroup", "telemetry", "terminalDataWriteEvent", "terminalDimensions", "testObserver", "textDocumentEncoding", "textSearchProvider", "timeline", "tokenInformation", "treeViewActiveItem", "treeViewReveal", "tunnels", "workspaceTrust"], "private": true, "activationEvents": [], "main": "./out/extension", "engines": {"vscode": "^1.55.0"}, "icon": "media/icon.png", "contributes": {"languageModels": [{"vendor": "test-lm-vendor"}], "chatParticipants": [{"id": "api-test.participant", "name": "participant", "description": "test", "isDefault": true, "commands": [{"name": "hello", "description": "Hello"}]}, {"id": "api-test.participant2", "name": "participant2", "description": "test", "commands": []}], "configuration": {"type": "object", "title": "Test Config", "properties": {"farboo.config0": {"type": "boolean", "default": true}, "farboo.nested.config1": {"type": "number", "default": 42}, "farboo.nested.config2": {"type": "string", "default": "<PERSON> Pferd frisst kein <PERSON>."}, "farboo.config4": {"type": "string"}, "farboo.get": {"type": "string", "default": "get-prop"}, "integration-test.http.proxy": {"type": "string"}, "integration-test.http.proxyAuth": {"type": "string", "default": "get-prop"}}}, "views": {"remote": [{"id": "test.treeId", "name": "test-tree", "when": "never"}]}, "configurationDefaults": {"[abcLang]": {"editor.lineNumbers": "off", "editor.tabSize": 2}}, "taskDefinitions": [{"type": "custombuildscript", "required": ["flavor"], "properties": {"flavor": {"type": "string", "description": "The build flavor. Should be either '32' or '64'."}, "flags": {"type": "array", "description": "Additional build flags."}}}], "breakpoints": [{"language": "markdown"}], "debuggers": [{"type": "mock", "label": "<PERSON><PERSON>", "languages": ["markdown"], "configurationAttributes": {"launch": {"required": ["program"], "properties": {"program": {"type": "string", "description": "Absolute path to a text file.", "default": "${workspaceFolder}/file.md"}, "stopOnEntry": {"type": "boolean", "description": "Automatically stop after launch.", "default": true}, "trace": {"type": "boolean", "description": "Enable logging of the Debug Adapter Protocol.", "default": true}}}}, "initialConfigurations": [{"type": "mock", "request": "launch", "name": "Debug file.md", "program": "${workspaceFolder}/file.md"}]}], "interactiveSession": [{"id": "provider", "label": "Provider"}], "notebooks": [{"type": "notebookCoreTest", "displayName": "Notebook Core Test", "selector": [{"filenamePattern": "*.vsctestnb", "excludeFileNamePattern": ""}]}, {"type": "notebook.nbdtest", "displayName": "notebook.nbdtest", "selector": [{"filenamePattern": "**/*.nbdtest"}]}, {"type": "notebook.nbdserializer", "displayName": "notebook.nbdserializer", "selector": [{"filenamePattern": "**/*.nbdserializer"}]}], "statusBarItems": {"id": "myStaticItem", "alignment": "right", "priority": 17, "name": "My Static Item", "text": "Hello $(globe)", "tooltip": "Hover World", "accessibilityInformation": {"label": "Hello World", "role": "button"}}}, "scripts": {"compile": "node ./node_modules/vscode/bin/compile -watch -p ./", "vscode:prepublish": "node ../../node_modules/gulp/bin/gulp.js --gulpfile ../../build/gulpfile.extensions.js compile-extension:vscode-api-tests ./tsconfig.json"}, "devDependencies": {"@types/mocha": "^9.1.1", "@types/node": "20.x", "@types/node-forge": "^1.3.11", "node-forge": "^1.3.1", "straightforward": "^4.2.2"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}