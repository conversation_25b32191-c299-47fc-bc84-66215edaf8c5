{
  // 指定使用的颜色主题JSON模式
  "$schema": "vscode://schemas/color-theme",
  // 主题名称
  "name": "GitHub Light",
  // 主题类型：light（浅色）、dark（深色）或high-contrast（高对比度）
  "type": "light",
  "colors": {
    // ======= 活动栏（左侧边栏图标区域）=======
    // 活动栏背景色
    "activityBar.background": "#F6F8FA",
    // 活动栏前景色（图标颜色）
    "activityBar.foreground": "#24292E",
    // 活动栏徽章背景色（通知数字背景）
    "activityBarBadge.background": "#0366D6",
    // 活动栏徽章前景色（通知数字颜色）
    "activityBarBadge.foreground": "#FFFFFF",

    // ======= 侧边栏（文件浏览器等）=======
    // 侧边栏背景色
    "sideBar.background": "#F6F8FA",
    // 侧边栏前景色（文本颜色）
    "sideBar.foreground": "#24292E",
    // 侧边栏标题前景色
    "sideBarTitle.foreground": "#24292E",
    // 侧边栏分区标题背景色
    "sideBarSectionHeader.background": "#F6F8FA",
    // 侧边栏分区标题前景色
    "sideBarSectionHeader.foreground": "#24292E",

    // ======= 状态栏（底部信息栏）=======
    // 状态栏背景色
    "statusBar.background": "#F6F8FA",
    // 状态栏前景色（文本颜色）
    "statusBar.foreground": "#24292E",
    // 无文件夹打开时状态栏背景色
    "statusBar.noFolderBackground": "#F6F8FA",
    // 远程连接状态项背景色
    "statusBarItem.remoteBackground": "#0366D6",
    // 远程连接状态项前景色
    "statusBarItem.remoteForeground": "#FFFFFF",

    // ======= 标题栏（窗口顶部）=======
    // 活动窗口的标题栏背景色
    "titleBar.activeBackground": "#F6F8FA",
    // 活动窗口的标题栏前景色
    "titleBar.activeForeground": "#24292E",
    // 非活动窗口的标题栏背景色
    "titleBar.inactiveBackground": "#F6F8FA",
    // 非活动窗口的标题栏前景色
    "titleBar.inactiveForeground": "#6A737D",

    // ======= 编辑器 =======
    // 编辑器背景色
    "editor.background": "#FFFFFF",
    // 编辑器前景色（文本颜色）
    "editor.foreground": "#24292E",
    // 行号颜色
    "editorLineNumber.foreground": "#6A737D",
    // 活动行的行号颜色
    "editorLineNumber.activeForeground": "#24292E",
    // 选中文本的背景色
    "editor.selectionBackground": "#0366D640",
    // 选中文本相同内容的高亮背景色
    "editor.selectionHighlightBackground": "#34D05840",
    // 相同单词高亮背景色
    "editor.wordHighlightBackground": "#34D05820",
    // 写入访问的单词高亮背景色
    "editor.wordHighlightStrongBackground": "#34D05840",
    // 光标颜色
    "editorCursor.foreground": "#044289",
    // 空白字符颜色（空格、制表符等）
    "editorWhitespace.foreground": "#E1E4E8",
    // 缩进参考线颜色
    "editorIndentGuide.background1": "#E1E4E8",
    // 活动缩进参考线颜色
    "editorIndentGuide.activeBackground1": "#6A737D",

    // ======= 列表和树视图 =======
    // 列表/树活动选择项背景色
    "list.activeSelectionBackground": "#0366D6",
    // 列表/树活动选择项前景色
    "list.activeSelectionForeground": "#FFFFFF",
    // 鼠标悬停项的背景色
    "list.hoverBackground": "#E1E4E8",
    // 非活动选择项的背景色
    "list.inactiveSelectionBackground": "#E1E4E8",

    // ======= 标签页 =======
    // 活动标签页背景色
    "tab.activeBackground": "#FFFFFF",
    // 活动标签页前景色
    "tab.activeForeground": "#24292E",
    // 非活动标签页背景色
    "tab.inactiveBackground": "#F6F8FA",
    // 非活动标签页前景色
    "tab.inactiveForeground": "#6A737D",
    // 标签页边框颜色
    "tab.border": "#E1E4E8",
    // 活动标签页底部边框颜色
    "tab.activeBorder": "#0366D6",

    // ======= 按钮 =======
    // 按钮背景色
    "button.background": "#0366D6",
    // 按钮前景色
    "button.foreground": "#FFFFFF",
    // 鼠标悬停时按钮背景色
    "button.hoverBackground": "#0356B3",

    // ======= 下拉菜单 =======
    // 下拉菜单背景色
    "dropdown.background": "#FFFFFF",
    // 下拉菜单前景色
    "dropdown.foreground": "#24292E",

    // ======= 输入框 =======
    // 输入框背景色
    "input.background": "#FFFFFF",
    // 输入框前景色
    "input.foreground": "#24292E",
    // 输入框边框颜色
    "input.border": "#E1E4E8",
    // 输入框占位符文本颜色
    "input.placeholderForeground": "#6A737D"
  },
  // 代码语法高亮颜色设置
  "tokenColors": [
    {
      // 嵌入式代码块的颜色
      "scope": [
        "meta.embedded",
        "source.groovy.embedded",
        "string meta.image.inline.markdown"
      ],
      "settings": {
        "foreground": "#24292E" // 深灰色，默认文本颜色
      }
    },
    {
      // 注释的颜色
      "scope": "comment",
      "settings": {
        "foreground": "#6A737D", // 中灰色
        "fontStyle": "italic"    // 斜体样式
      }
    },
    {
      // 字符串的颜色
      "scope": "string",
      "settings": {
        "foreground": "#032F62" // 深蓝色
      }
    },
    {
      // 数字常量的颜色
      "scope": "constant.numeric",
      "settings": {
        "foreground": "#005CC5" // 蓝色
      }
    },
    {
      // 语言常量、字符常量和其他常量的颜色
      "scope": [
        "constant.language",    // 如 true, false, null
        "constant.character",   // 字符常量
        "constant.other"        // 其他常量
      ],
      "settings": {
        "foreground": "#005CC5" // 蓝色
      }
    },
    {
      // 变量的颜色
      "scope": "variable",
      "settings": {
        "foreground": "#24292E" // 深灰色，默认文本颜色
      }
    },
    {
      // 关键字的颜色
      "scope": "keyword",
      "settings": {
        "foreground": "#D73A49" // 红色
      }
    },
    {
      // 存储类型和修饰符的颜色
      "scope": "storage",
      "settings": {
        "foreground": "#D73A49" // 红色
      }
    },
    {
      // 函数名的颜色
      "scope": "entity.name.function",
      "settings": {
        "foreground": "#6F42C1" // 紫色
      }
    },
    {
      // 类型名的颜色
      "scope": "entity.name.type",
      "settings": {
        "foreground": "#22863A" // 绿色
      }
    },
    {
      // 类名的颜色
      "scope": "entity.name.class",
      "settings": {
        "foreground": "#22863A" // 绿色
      }
    },
    {
      // HTML/XML标签名的颜色
      "scope": "entity.name.tag",
      "settings": {
        "foreground": "#22863A" // 绿色
      }
    },
    {
      // HTML/XML属性名的颜色
      "scope": "entity.other.attribute-name",
      "settings": {
        "foreground": "#6F42C1" // 紫色
      }
    },
    {
      // 支持函数的颜色（内置函数）
      "scope": "support.function",
      "settings": {
        "foreground": "#6F42C1" // 紫色
      }
    },
    {
      // 支持类的颜色（内置类）
      "scope": "support.class",
      "settings": {
        "foreground": "#22863A" // 绿色
      }
    },
    {
      // 支持类型的颜色（内置类型）
      "scope": "support.type",
      "settings": {
        "foreground": "#22863A" // 绿色
      }
    },
    {
      // 支持常量的颜色（内置常量）
      "scope": "support.constant",
      "settings": {
        "foreground": "#005CC5" // 蓝色
      }
    },
    {
      // 支持变量的颜色（内置变量）
      "scope": "support.variable",
      "settings": {
        "foreground": "#24292E" // 深灰色，默认文本颜色
      }
    },
    {
      // 标点符号的颜色
      "scope": "punctuation",
      "settings": {
        "foreground": "#24292E" // 深灰色，默认文本颜色
      }
    }
  ],
  // 启用语义高亮
  "semanticHighlighting": true,
  // 语义标记颜色设置
  "semanticTokenColors": {
    // 新建操作符的颜色（如 new）
    "newOperator": "#D73A49",    // 红色
    // 字符串字面量的颜色
    "stringLiteral": "#032F62",  // 深蓝色
    // 自定义字面量的颜色
    "customLiteral": "#6F42C1",  // 紫色
    // 数字字面量的颜色
    "numberLiteral": "#005CC5"   // 蓝色
  }
}
