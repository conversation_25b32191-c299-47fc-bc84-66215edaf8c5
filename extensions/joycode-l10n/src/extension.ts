/*!--------------------------------------------------------
 * Copyright (c) JoyCode. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 *-------------------------------------------------------*/

import * as vscode from 'vscode';
import * as l10n from '@vscode/l10n';

// 全局变量存储当前翻译
let currentTranslations: any = {};

export async function activate(context: vscode.ExtensionContext) {
	console.log('JoyCode L10n extension is now active');

	try {
		// 初始化l10n
		await l10n.config({
			fsPath: context.extensionPath
		});

		// 注册命令以更新文本
		const updateTextsCommand = vscode.commands.registerCommand('joycode-l10n.updateTexts', () => {
			console.log('joycode-l10n.updateTexts command called');
			return updateTexts();
		});

		// 注册获取文本的命令
		const getTextsCommand = vscode.commands.registerCommand('joycode-l10n.getTexts', () => {
			console.log('joycode-l10n.getTexts command called, returning:', currentTranslations);
			return currentTranslations;
		});

		context.subscriptions.push(updateTextsCommand, getTextsCommand);

		// 监听配置变化
		context.subscriptions.push(
			vscode.workspace.onDidChangeConfiguration(e => {
				if (e.affectsConfiguration('joycode.language')) {
					console.log('Language configuration changed');
					const config = vscode.workspace.getConfiguration('joycode');
					const language = config.get<string>('language');
					if (language) {
						// 更新文本
						updateTexts();
						// 提示用户需要重启以应用语言更改
						vscode.window.showInformationMessage(
							l10n.t('joycode.settings.language.confirm', language),
							l10n.t('joycode.settings.language.yes')
						).then(selection => {
							if (selection === l10n.t('joycode.settings.language.yes')) {
								vscode.commands.executeCommand('workbench.action.reloadWindow');
							}
						});
					}
				}
			})
		);

		// 延迟初始更新文本，确保系统完全启动
		setTimeout(() => {
			updateTexts();
		}, 1000);

		console.log('JoyCode L10n extension activated successfully');
	} catch (error) {
		console.error('Failed to activate JoyCode L10n extension:', error);
	}
}

function updateTexts() {
	try {
		// 使用l10n.t()获取翻译文本
		const translations = {
			login: l10n.t('joycode.settings.menu.login'),
			checkUpdate: l10n.t('joycode.settings.menu.checkUpdate'),
			projectSettings: l10n.t('joycode.settings.menu.projectSettings'),
			settings: l10n.t('joycode.settings.menu.settings'),
			joycodeSettings: l10n.t('joycode.settings.menu.joycodeSettings'),
			theme: l10n.t('joycode.settings.menu.theme'),
			language: l10n.t('joycode.settings.menu.language'),
			keybindings: l10n.t('joycode.settings.menu.keybindings'),
			extensions: l10n.t('joycode.settings.menu.extensions'),
			about: l10n.t('joycode.settings.menu.about'),
			releaseNote: l10n.t('joycode.settings.menu.releaseNote'),
			logout: l10n.t('joycode.settings.menu.logout'),
			error: l10n.t('joycode.settings.menu.error') || 'Error'
		};

		// 更新全局变量
		currentTranslations = translations;

		console.log('Updating l10n texts:', translations);

		// 使用多种方式设置 context key
		vscode.commands.executeCommand('setContext', 'joycode.l10n.texts', translations);
		vscode.commands.executeCommand('setContext', 'menuTexts', translations);

		// 触发自定义事件通知主应用
		vscode.commands.executeCommand('joycode.l10n.textsUpdated', translations);

		return translations;
	} catch (error) {
		console.error('Failed to update l10n texts:', error);
		return null;
	}
}

export function deactivate() { }
