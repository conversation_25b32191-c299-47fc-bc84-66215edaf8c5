{"name": "joycode-l10n", "version": "0.0.1", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "joycode-l10n", "version": "0.0.1", "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.99.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vscode/l10n": "^0.0.18", "eslint": "^8.45.0", "typescript": "^5.8.3"}, "engines": {"vscode": "^1.80.0"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "resolved": "http://registry.m.jd.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz", "integrity": "sha1-YHCEYwxsAzmSoILebm+8GotSF1o=", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "resolved": "http://registry.m.jd.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz", "integrity": "sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "resolved": "http://registry.m.jd.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz", "integrity": "sha1-OIomnw8lwbatwxe1osVXFIlMcK0=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/js": {"version": "8.57.1", "resolved": "http://registry.m.jd.com/@eslint/js/download/@eslint/js-8.57.1.tgz", "integrity": "sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "resolved": "http://registry.m.jd.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz", "integrity": "sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=", "deprecated": "Use @eslint/config-array instead", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/config-array/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@humanwhocodes/config-array/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz", "integrity": "sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz", "integrity": "sha1-Siho111taWPkI7z5C3/RvjQ0CdM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "resolved": "http://registry.m.jd.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz", "integrity": "sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "resolved": "http://registry.m.jd.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz", "integrity": "sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "resolved": "http://registry.m.jd.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz", "integrity": "sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "resolved": "http://registry.m.jd.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz", "integrity": "sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "16.18.126", "resolved": "http://registry.m.jd.com/@types/node/download/@types/node-16.18.126.tgz", "integrity": "sha1-J4dfqikmwPR1s5qLseVGwBdvjUs=", "dev": true, "license": "MIT"}, "node_modules/@types/semver": {"version": "7.7.0", "resolved": "http://registry.m.jd.com/@types/semver/download/@types/semver-7.7.0.tgz", "integrity": "sha1-ZMRBva4DOzeLbu99DD13wym5N44=", "dev": true, "license": "MIT"}, "node_modules/@types/vscode": {"version": "1.99.1", "resolved": "http://registry.m.jd.com/@types/vscode/download/@types/vscode-1.99.1.tgz", "integrity": "sha1-vebi2cy+BJP97ZitY5vyZxuOye4=", "dev": true, "license": "MIT"}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-6.21.0.tgz", "integrity": "sha1-MIMMHKgf1fPCcU5STEMD4BlPnNM=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.5.1", "@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/type-utils": "6.21.0", "@typescript-eslint/utils": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4", "graphemer": "^1.4.0", "ignore": "^5.2.4", "natural-compare": "^1.4.0", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^6.0.0 || ^6.0.0-alpha", "eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/parser/download/@typescript-eslint/parser-6.21.0.tgz", "integrity": "sha1-r4/PZv7uLtyGvF0c9F4zsGML81s=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/types": "6.21.0", "@typescript-eslint/typescript-estree": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-6.21.0.tgz", "integrity": "sha1-6oqb/I8VBKasXVmm3zCNOgYworE=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-6.21.0.tgz", "integrity": "sha1-ZHMoHP7U2sq+gAToUhzuC9nUwB4=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "6.21.0", "@typescript-eslint/utils": "6.21.0", "debug": "^4.3.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/types/download/@typescript-eslint/types-6.21.0.tgz", "integrity": "sha1-IFckxRI6j+9+zRlQdfpuhbrDQ20=", "dev": true, "license": "MIT", "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-6.21.0.tgz", "integrity": "sha1-xHrnkB2zuL3cPs1z2v8tCJVojEY=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "minimatch": "9.0.3", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/utils": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/utils/download/@typescript-eslint/utils-6.21.0.tgz", "integrity": "sha1-RxTnprOedzwcjpfsWH9SCEDNgTQ=", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "@types/json-schema": "^7.0.12", "@types/semver": "^7.5.0", "@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/types": "6.21.0", "@typescript-eslint/typescript-estree": "6.21.0", "semver": "^7.5.4"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "6.21.0", "resolved": "http://registry.m.jd.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-6.21.0.tgz", "integrity": "sha1-h6mdB3qlB+IOI4sR1WzCat5F/kc=", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "6.21.0", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@ungap/structured-clone": {"version": "1.3.0", "resolved": "http://registry.m.jd.com/@ungap/structured-clone/download/@ungap/structured-clone-1.3.0.tgz", "integrity": "sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=", "dev": true, "license": "ISC"}, "node_modules/@vscode/l10n": {"version": "0.0.18", "resolved": "http://registry.m.jd.com/@vscode/l10n/download/@vscode/l10n-0.0.18.tgz", "integrity": "sha1-kW06XpYNurR8HFb1iny1CHsTXJU=", "dev": true, "license": "MIT"}, "node_modules/acorn": {"version": "8.15.0", "resolved": "http://registry.m.jd.com/acorn/download/acorn-8.15.0.tgz", "integrity": "sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "resolved": "http://registry.m.jd.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "http://registry.m.jd.com/ajv/download/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/ansi-regex/download/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "http://registry.m.jd.com/ansi-styles/download/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/argparse": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/argparse/download/argparse-2.0.1.tgz", "integrity": "sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=", "dev": true, "license": "Python-2.0"}, "node_modules/array-union": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/array-union/download/array-union-2.1.0.tgz", "integrity": "sha1-t5hCCtvrHego2ErNii4j0+/oXo0=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/balanced-match/download/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true, "license": "MIT"}, "node_modules/brace-expansion": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-2.0.1.tgz", "integrity": "sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "http://registry.m.jd.com/braces/download/braces-3.0.3.tgz", "integrity": "sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=", "dev": true, "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/callsites": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/callsites/download/callsites-3.1.0.tgz", "integrity": "sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "http://registry.m.jd.com/chalk/download/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "http://registry.m.jd.com/color-convert/download/color-convert-2.0.1.tgz", "integrity": "sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=", "dev": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "http://registry.m.jd.com/color-name/download/color-name-1.1.4.tgz", "integrity": "sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=", "dev": true, "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://registry.m.jd.com/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "http://registry.m.jd.com/cross-spawn/download/cross-spawn-7.0.6.tgz", "integrity": "sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "http://registry.m.jd.com/debug/download/debug-4.4.1.tgz", "integrity": "sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/deep-is": {"version": "0.1.4", "resolved": "http://registry.m.jd.com/deep-is/download/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=", "dev": true, "license": "MIT"}, "node_modules/dir-glob": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/dir-glob/download/dir-glob-3.0.1.tgz", "integrity": "sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/doctrine": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/doctrine/download/doctrine-3.0.0.tgz", "integrity": "sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/escape-string-regexp": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz", "integrity": "sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint": {"version": "8.57.1", "resolved": "http://registry.m.jd.com/eslint/download/eslint-8.57.1.tgz", "integrity": "sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=", "deprecated": "This version is no longer supported. Please see https://eslint.org/version-support for other options.", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-scope": {"version": "7.2.2", "resolved": "http://registry.m.jd.com/eslint-scope/download/eslint-scope-7.2.2.tgz", "integrity": "sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "resolved": "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz", "integrity": "sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/espree": {"version": "9.6.1", "resolved": "http://registry.m.jd.com/espree/download/espree-9.6.1.tgz", "integrity": "sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esquery": {"version": "1.6.0", "resolved": "http://registry.m.jd.com/esquery/download/esquery-1.6.0.tgz", "integrity": "sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "resolved": "http://registry.m.jd.com/esrecurse/download/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "resolved": "http://registry.m.jd.com/estraverse/download/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "resolved": "http://registry.m.jd.com/esutils/download/esutils-2.0.3.tgz", "integrity": "sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "http://registry.m.jd.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.3", "resolved": "http://registry.m.jd.com/fast-glob/download/fast-glob-3.3.3.tgz", "integrity": "sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-glob/node_modules/glob-parent": {"version": "5.1.2", "resolved": "http://registry.m.jd.com/glob-parent/download/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://registry.m.jd.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "resolved": "http://registry.m.jd.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=", "dev": true, "license": "MIT"}, "node_modules/fastq": {"version": "1.19.1", "resolved": "http://registry.m.jd.com/fastq/download/fastq-1.19.1.tgz", "integrity": "sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "resolved": "http://registry.m.jd.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz", "integrity": "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "http://registry.m.jd.com/fill-range/download/fill-range-7.1.1.tgz", "integrity": "sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=", "dev": true, "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/find-up/download/find-up-5.0.0.tgz", "integrity": "sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/flat-cache": {"version": "3.2.0", "resolved": "http://registry.m.jd.com/flat-cache/download/flat-cache-3.2.0.tgz", "integrity": "sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.3", "resolved": "http://registry.m.jd.com/flatted/download/flatted-3.3.3.tgz", "integrity": "sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=", "dev": true, "license": "ISC"}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "http://registry.m.jd.com/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true, "license": "ISC"}, "node_modules/glob": {"version": "7.2.3", "resolved": "http://registry.m.jd.com/glob/download/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "6.0.2", "resolved": "http://registry.m.jd.com/glob-parent/download/glob-parent-6.0.2.tgz", "integrity": "sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/glob/node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/glob/node_modules/minimatch": {"version": "3.1.2", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/globals": {"version": "13.24.0", "resolved": "http://registry.m.jd.com/globals/download/globals-13.24.0.tgz", "integrity": "sha1-hDKhnXjODB6DOUnDats0VAC7EXE=", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globby": {"version": "11.1.0", "resolved": "http://registry.m.jd.com/globby/download/globby-11.1.0.tgz", "integrity": "sha1-vUvpi7BC+D15b344EZkfvoKg00s=", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/graphemer": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/graphemer/download/graphemer-1.4.0.tgz", "integrity": "sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/has-flag/download/has-flag-4.0.0.tgz", "integrity": "sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ignore": {"version": "5.3.2", "resolved": "http://registry.m.jd.com/ignore/download/ignore-5.3.2.tgz", "integrity": "sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/import-fresh": {"version": "3.3.1", "resolved": "http://registry.m.jd.com/import-fresh/download/import-fresh-3.3.1.tgz", "integrity": "sha1-nOy1ZQPAraHydB271lRuSxO1fM8=", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://registry.m.jd.com/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "http://registry.m.jd.com/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "http://registry.m.jd.com/inherits/download/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true, "license": "ISC"}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://registry.m.jd.com/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-glob": {"version": "4.0.3", "resolved": "http://registry.m.jd.com/is-glob/download/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "http://registry.m.jd.com/is-number/download/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "resolved": "http://registry.m.jd.com/is-path-inside/download/is-path-inside-3.0.3.tgz", "integrity": "sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/js-yaml": {"version": "4.1.0", "resolved": "http://registry.m.jd.com/js-yaml/download/js-yaml-4.1.0.tgz", "integrity": "sha1-wftl+PUBeQHN0slRhkuhhFihBgI=", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/json-buffer": {"version": "3.0.1", "resolved": "http://registry.m.jd.com/json-buffer/download/json-buffer-3.0.1.tgz", "integrity": "sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "http://registry.m.jd.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=", "dev": true, "license": "MIT"}, "node_modules/keyv": {"version": "4.5.4", "resolved": "http://registry.m.jd.com/keyv/download/keyv-4.5.4.tgz", "integrity": "sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/levn": {"version": "0.4.1", "resolved": "http://registry.m.jd.com/levn/download/levn-0.4.1.tgz", "integrity": "sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/locate-path": {"version": "6.0.0", "resolved": "http://registry.m.jd.com/locate-path/download/locate-path-6.0.0.tgz", "integrity": "sha1-VTIeswn+u8WcSAHZMackUqaB0oY=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lodash.merge": {"version": "4.6.2", "resolved": "http://registry.m.jd.com/lodash.merge/download/lodash.merge-4.6.2.tgz", "integrity": "sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "resolved": "http://registry.m.jd.com/merge2/download/merge2-1.4.1.tgz", "integrity": "sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "http://registry.m.jd.com/micromatch/download/micromatch-4.0.8.tgz", "integrity": "sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=", "dev": true, "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/minimatch": {"version": "9.0.3", "resolved": "http://registry.m.jd.com/minimatch/download/minimatch-9.0.3.tgz", "integrity": "sha1-puAMPeRMOlQr+q5wq/wiQgptqCU=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "http://registry.m.jd.com/ms/download/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "dev": true, "license": "MIT"}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/natural-compare/download/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=", "dev": true, "license": "MIT"}, "node_modules/once": {"version": "1.4.0", "resolved": "http://registry.m.jd.com/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/optionator": {"version": "0.9.4", "resolved": "http://registry.m.jd.com/optionator/download/optionator-0.9.4.tgz", "integrity": "sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "http://registry.m.jd.com/p-limit/download/p-limit-3.1.0.tgz", "integrity": "sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "5.0.0", "resolved": "http://registry.m.jd.com/p-locate/download/p-locate-5.0.0.tgz", "integrity": "sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parent-module": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/parent-module/download/parent-module-1.0.1.tgz", "integrity": "sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/path-exists/download/path-exists-4.0.0.tgz", "integrity": "sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "http://registry.m.jd.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/path-key/download/path-key-3.1.1.tgz", "integrity": "sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-type": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/path-type/download/path-type-4.0.0.tgz", "integrity": "sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "http://registry.m.jd.com/picomatch/download/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/prelude-ls": {"version": "1.2.1", "resolved": "http://registry.m.jd.com/prelude-ls/download/prelude-ls-1.2.1.tgz", "integrity": "sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/punycode": {"version": "2.3.1", "resolved": "http://registry.m.jd.com/punycode/download/punycode-2.3.1.tgz", "integrity": "sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/queue-microtask": {"version": "1.2.3", "resolved": "http://registry.m.jd.com/queue-microtask/download/queue-microtask-1.2.3.tgz", "integrity": "sha1-SSkii7xyTfrEPg77BYyve2z7YkM=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/resolve-from": {"version": "4.0.0", "resolved": "http://registry.m.jd.com/resolve-from/download/resolve-from-4.0.0.tgz", "integrity": "sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/reusify": {"version": "1.1.0", "resolved": "http://registry.m.jd.com/reusify/download/reusify-1.1.0.tgz", "integrity": "sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rimraf": {"version": "3.0.2", "resolved": "http://registry.m.jd.com/rimraf/download/rimraf-3.0.2.tgz", "integrity": "sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-parallel": {"version": "1.2.0", "resolved": "http://registry.m.jd.com/run-parallel/download/run-parallel-1.2.0.tgz", "integrity": "sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/semver": {"version": "7.7.2", "resolved": "http://registry.m.jd.com/semver/download/semver-7.7.2.tgz", "integrity": "sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "http://registry.m.jd.com/shebang-command/download/shebang-command-2.0.0.tgz", "integrity": "sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/shebang-regex/download/shebang-regex-3.0.0.tgz", "integrity": "sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slash": {"version": "3.0.0", "resolved": "http://registry.m.jd.com/slash/download/slash-3.0.0.tgz", "integrity": "sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "http://registry.m.jd.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz", "integrity": "sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "http://registry.m.jd.com/supports-color/download/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/text-table": {"version": "0.2.0", "resolved": "http://registry.m.jd.com/text-table/download/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=", "dev": true, "license": "MIT"}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "http://registry.m.jd.com/to-regex-range/download/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/ts-api-utils": {"version": "1.4.3", "resolved": "http://registry.m.jd.com/ts-api-utils/download/ts-api-utils-1.4.3.tgz", "integrity": "sha1-v8IhX+ZSj+yrKw+6VwouikJjsGQ=", "dev": true, "license": "MIT", "engines": {"node": ">=16"}, "peerDependencies": {"typescript": ">=4.2.0"}}, "node_modules/type-check": {"version": "0.4.0", "resolved": "http://registry.m.jd.com/type-check/download/type-check-0.4.0.tgz", "integrity": "sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.20.2", "resolved": "http://registry.m.jd.com/type-fest/download/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typescript": {"version": "5.8.3", "resolved": "http://registry.m.jd.com/typescript/download/typescript-5.8.3.tgz", "integrity": "sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=", "dev": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "http://registry.m.jd.com/uri-js/download/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/which": {"version": "2.0.2", "resolved": "http://registry.m.jd.com/which/download/which-2.0.2.tgz", "integrity": "sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/word-wrap": {"version": "1.2.5", "resolved": "http://registry.m.jd.com/word-wrap/download/word-wrap-1.2.5.tgz", "integrity": "sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "http://registry.m.jd.com/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true, "license": "ISC"}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "http://registry.m.jd.com/yocto-queue/download/yocto-queue-0.1.0.tgz", "integrity": "sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}