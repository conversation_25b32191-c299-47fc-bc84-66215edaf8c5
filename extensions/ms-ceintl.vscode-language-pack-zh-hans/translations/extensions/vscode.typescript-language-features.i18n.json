{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"(loading...)/Prefix displayed for hover entries while the server is still loading": "(正在加载...)", "...1 additional file not shown": "...1 个其他文件未显示", "...{0} additional files not shown": "...{0} 个其他文件未显示", "1 implementation": "1 个实现", "1 reference": "1 个引用", "Acquiring typings definitions for IntelliSense./Typings refers to the *.d.ts typings files that power our IntelliSense. It should not be localized": "获取 IntelliSense 的 typings 定义。", "Acquiring typings.../Typings refers to the *.d.ts typings files that power our IntelliSense. It should not be localized": "正在获取 typings…", "Add all missing imports": "添加所有缺少的导入", "Allow": "允许", "Always": "始终", "An error occurred while renaming file": "重命名文件时出错", "Analyzing '{0}' and its dependencies": "正在分析“{0}”及其依赖项", "Checking for update of JS/TS imports": "正在检查 JS/TS import 语句的更新", "Configure Excludes": "配置排除", "Configure JSConfig": "配置 JSConfig", "Configure TSConfig": "配置 TSConfig", "Configure jsconfig.json": "配置 jsconfig.json", "Configure tsconfig.json": "配置 tsconfig.json", "Could not apply refactoring": "无法应用重构", "Could not detect a Node installation to run TS Server.": "无法检测节点安装以运行 TS 服务器。", "Could not determine TypeScript or JavaScript project": "无法确定 TypeScript 或 JavaScript 项目", "Could not determine TypeScript or JavaScript project. Unsupported file type": "无法确定 TypeScript 或 JavaScript 项目。不受支持的文件类型", "Could not determine references": "无法确定引用", "Could not install typings files for JavaScript language features. Please ensure that NPM is installed, or configure 'typescript.npm' in your user settings. Alternatively, check the [documentation]({0}) to learn more.": "无法为 JavaScript 语言功能安装 typings 文件。请确认 NPM 已安装，或在用户设置中配置 “typescript.npm”。或者检查 [文档]({0})以了解详细信息。", "Could not load the TypeScript version at this path": "无法获取此目录 TypeScript 的版本", "Could not open TS Server log file": "无法打开 TS 服务器日志文件", "Disable logging": "禁用事件日志", "Disables semantic checking in a JavaScript file. Must be at the top of a file.": "在 JavaScript 文件中禁用语义检查。必须在文件顶部。", "Dismiss": "关闭", "Don't Show Again": "不再显示", "Don't show again": "不再显示", "Enable logging and restart TS server": "启用日志记录并重启 TS 服务器", "Enables semantic checking in a JavaScript file. Must be at the top of a file.": "在 JavaScript 文件中启用语义检查。必须在文件顶部。", "Enter file path": "输入文件路径", "Enter new file path...": "输入新文件路径...", "Extract to constant": "提取到常量", "Extract to function": "提取到函数", "Failed to resolve {0} as module": "无法将 {0} 解析为模块", "Fetching data for better TypeScript IntelliSense": "提取数据以实现更好的 TypeScript IntelliSense", "File is not part of a JavaScript project. View the [jsconfig.json documentation]({0}) to learn more.": "文件不是 JavaScript 项目的一部分。请查看 [jsconfig.json 文档]({0})以了解详细信息。", "File is not part of a TypeScript project. View the [tsconfig.json documentation]({0}) to learn more.": "文件不是 TypeScript 项目的一部分。请查看 [tsconfig.json 文档]({0})以了解详细信息。", "File is not part opened folders": "文件不是已打开文件夹的一部分", "Find file references failed. No resource provided.": "查找文件引用失败。未提供资源。", "Find file references failed. Requires TypeScript 4.2+.": "查找文件引用失败。需要 TypeScript 4.2+。", "Find file references failed. Unknown file type.": "查找文件引用失败。未知的文件类型。", "Find file references failed. Unsupported file type.": "查找文件引用失败。不支持的文件类型。", "Finding file references": "正在查找文件引用", "Finding source definitions": "正在查找源定义", "Fix all fixable JS/TS issues": "修复所有可修复的 JS/TS 问题", "Follow link": "跟踪链接", "Go to Source Definition failed. No resource provided.": "转到源定义失败。未提供资源。", "Go to Source Definition failed. Requires TypeScript 4.7+.": "转到源定义失败。需要 TypeScript 4.7 以上版本。", "Go to Source Definition failed. Unknown file type.": "转到源定义失败。未知文件类型。", "Go to Source Definition failed. Unsupported file type.": "转到源定义失败。不支持的文件类型。", "Initializing '{0}'": "正在初始化“{0}”", "JS/TS IntelliSense Status": "JS/TS IntelliSense 状态", "JSDoc comment": "JSDoc 注释", "Learn More": "了解详细信息", "Learn more about JS/TS refactorings": "了解有关 JS/TS 重构功能的更多详细信息", "Learn more about managing TypeScript versions": "了解有关管理 TypeScript 版本的更多信息", "Loading IntelliSense status": "正在加载 IntelliSense 状态", "Move to File": "移动到文件", "Never": "从不", "Never in this Workspace": "绝不在此工作区中", "No": "否", "No jsconfig": "无 jsconfig", "No opened folders": "没有打开的文件夹", "No source definitions found.": "未找到定义。", "No tsconfig": "无 tsconfig", "Not now": "以后再说", "Open Config File": "打开配置文件", "Open on GitHub": "在 GitHub 上打开", "Organize Imports": "整理导入", "Partial mode": "部分模式", "Paste with imports": "粘贴并导入", "Please open a folder in VS Code to use a TypeScript or JavaScript project": "请在 VS Code 中打开一个文件夹，以使用 TypeScript 或 JavaScript 项目", "Please report an issue against Yarn PnP": "请针对 Yarn PnP 报告问题", "Please update your TypeScript version": "请更新 TypeScript 版本", "Project wide IntelliSense not available": "项目范围的 IntelliSense 不可用", "Remove Unused Imports": "删除未使用的导入", "Remove all unused code": "删除所有未使用的代码", "Report Issue": "报告问题", "Report issue against Yarn PnP": "针对 Yarn PnP 报告问题", "Select Version": "选择版本", "Select code action to apply": "选择要应用的代码操作", "Select existing file...": "选择现有文件...", "Select move destination": "选择移动目标", "Select the TypeScript version used for JavaScript and TypeScript language features": "选择用于 JavaScript 和 TypeScript 语言功能的 TypeScript 版本", "Sort Imports": "对导入进行排序", "Suppresses @ts-check errors on the next line of a file, expecting at least one to exist.": "禁止在文件的下一行显示 @ts-check 错误，预计至少存在一个错误。", "Suppresses @ts-check errors on the next line of a file.": "取消文件下一行的 @ts-check 错误提示。", "TS Server has not started logging.": "TS 服务器尚未启动日志记录。", "TS Server logging is currently enabled which may impact performance.": "TS Server 事件日志当前已启用，这可能会影响性能。", "TS Server logging is off. Please set 'typescript.tsserver.log' and restart the TS server to enable logging": "TS 服务器日志已关闭。请设置 “typescript.tsserver.log” 并重启 TS 服务器以启用事件日志", "The JS/TS language service crashed 5 times in the last 5 Minutes.": "在过去 5 分钟内，JS/TS 语言服务崩溃了 5 次。", "The JS/TS language service crashed 5 times in the last 5 Minutes.\nThis may be caused by a plugin contributed by one of these extensions: {0}\nPlease try disabling these extensions before filing an issue against VS Code.": "在过去 5 分钟内，JS/TS 语言服务崩溃了 5 次。\n这可能是由以下其中一个扩展提供的插件引起的: {0}\n请在针对 VS Code 提交问题之前尝试禁用这些扩展。", "The JS/TS language service crashed.": "JS/TS 语言服务已崩溃。", "The JS/TS language service crashed.\nThis may be caused by a plugin contributed by one of these extensions: {0}.\nPlease try disabling these extensions before filing an issue against VS Code.": "JS/TS 语言服务已崩溃。\n这可能是由以下其中一个扩展提供的插件引起的: {0}\n请在针对 VS Code 提交问题之前尝试禁用这些扩展。", "The JS/TS language service immediately crashed 5 times. The service will not be restarted.": "JS/TS 语言服务已立即崩溃 5 次。将不会重启该服务。", "The JS/TS language service immediately crashed 5 times. The service will not be restarted.\nThis may be caused by a plugin contributed by one of these extensions: {0}.\nPlease try disabling these extensions before filing an issue against VS Code.": "JS/TS 语言服务已立即崩溃 5 次。不会重新启动该服务。\n这可能是由以下其中一个扩展提供的插件引起的: {0}\n请在针对 VS Code 提交问题之前尝试禁用这些扩展。", "The TypeScript Go extension is not installed.": "未安装 TypeScript Go 扩展。", "The current selection cannot be extracted": "无法提取当前所选内容", "The path {0} doesn't point to a valid Node installation to run TS Server. Falling back to bundled Node.": "路径 {0} 未指向运行 TS 服务器的有效节点安装。回退到捆绑节点。", "The path {0} doesn't point to a valid tsserver install. Falling back to bundled TypeScript version.": "路径 {0} 未指向有效的 tsserver 安装。请回退到捆绑的 TypeScript 版本。", "The workspace is using a version of the TypeScript Server that has been patched by Yarn PnP. This patching is a common source of bugs.": "工作区使用的是已经过 Yarn PnP 修补的 TypeScript 服务器版本。此修补是 bug 的常见来源。", "The workspace is using an old version of TypeScript ({0}).\n\nBefore reporting an issue, please update the workspace to use TypeScript {1} or newer to make sure the bug has not already been fixed.": "工作区正在使用旧版本的 TypeScript（{0}）。\n\n在报告问题之前，请更新工作区以使用 TypeScript {1} 或更高版本，以确认 bug 尚未修复。", "This workspace contains a TypeScript version. Would you like to use the workspace TypeScript version for TypeScript and JavaScript language features?": "此工作区包含一个 TypeScript 版本。是否要对 TypeScript 和 JavaScript 语言功能使用工作区 TypeScript 版本?", "This workspace wants to use the Node installation at '{0}' to run TS Server. Would you like to use it?": "此工作区想要使用 '{0}' 处的节点安装来运行 TS 服务器。是否要使用?", "To enable project-wide JavaScript/TypeScript language features, exclude folders with many files, like: {0}": "若要启用项目范围内的 JavaScript/TypeScript 语言功能，请排除包含多个文件的文件夹，例如: {0}", "To enable project-wide JavaScript/TypeScript language features, exclude large folders with source files that you do not work on.": "若要启用项目范围内的 JavaScript/TypeScript 语言功能，请排除包含不需要处理的源文件的大型文件夹。", "TypeScript Server Log": "TypeScript 服务器日志", "TypeScript Task in tasks.json contains \"\\\\\". TypeScript tasks tsconfig must use \"/\"": "tasks.json 中的 TypeScript 任务包含 \"\\\\\"。TypeScript 任务的 tsconfig 必须使用 \"/\"", "TypeScript Version": "TypeScript 版本", "TypeScript language server exited with error. Error message is: {0}": "TypeScript 语言服务器因错误而退出。错误消息: {0}", "TypeScript version": "TypeScript 版本", "TypeScript: Configure Excludes": "TypeScript: 配置排除", "Update imports for '{0}'?": "是否更新“{0}”的导入?", "Update imports for the following {0} files?": "是否更新以下 {0} 个文件的导入?", "Use VS Code's Version": "使用 VS Code 的版本", "Use Workspace Version": "使用工作区版本", "VS Code's tsserver was deleted by another application such as a misbehaving virus detection tool. Please reinstall VS Code.": "VS Code 的 tsserver 已被其他应用程序(例如运行异常的病毒检测工具)删除。请重新安装 VS Code。", "Yes": "是", "build - {0}": "构建 - {0}", "destination files": "目标文件", "invalid version": "无效版本", "watch - {0}": "监视 - {0}", "{0} (Fix all in file)": "{0} (修复文件中所有)", "{0} implementations": "{0} 个实现", "{0} references": "{0} 个引用"}, "package": {"configuration.expandableHover": "启用展开/收缩悬停以显示来自 TS 服务器的更多/更少信息。需要 TypeScript 5.9+。", "configuration.format": "格式设置", "configuration.implicitProjectConfig.checkJs": "启用或禁用 JavaScript 文件的语义检查。现有 `jsconfig.json` 或 `tsconfig.json` 文件将覆盖此设置。", "configuration.implicitProjectConfig.experimentalDecorators": "在不属于任何工程的 JavaScript 文件中启用或禁用 `experimentalDecorators`。现有 `jsconfig.json` 或 `tsconfig.json` 文件将覆盖此设置。", "configuration.implicitProjectConfig.module": "设置程序的模块系统。查看详细信息: https://www.typescriptlang.org/tsconfig#module。", "configuration.implicitProjectConfig.strictFunctionTypes": "在不属于项目的 JavaScript 和 TypeScript 文件中启用/禁用[严格函数类型](https://www.typescriptlang.org/tsconfig#strictFunctionTypes)。现有 `jsconfig.json` 或 `tsconfig.json` 文件将替代此设置。", "configuration.implicitProjectConfig.strictNullChecks": "在不属于项目的 JavaScript 和 TypeScript 文件中启用/禁用[严格 null 检查](https://www.typescriptlang.org/tsconfig#strictNullChecks)。现有 `jsconfig.json` 或 `tsconfig.json` 文件将替代此设置。", "configuration.implicitProjectConfig.target": "为发出的 JavaScript 设置目标 JavaScript 语言版本并包含库声明。查看详细信息: https://www.typescriptlang.org/tsconfig#target。", "configuration.inlayHints": "内嵌提示", "configuration.inlayHints.enumMemberValues.enabled": "启用/禁用枚举声明中成员值的嵌入提示:\r\n```typescript\r\n\r\nenum MyValue {\r\n\tA /* = 0 */;\r\n\tB /* = 1 */;\r\n}\r\n \r\n```", "configuration.inlayHints.functionLikeReturnTypes.enabled": "启用/禁用函数签名上隐式返回类型的嵌入提示:\r\n```typescript\r\n\r\nfunction foo() /* :number */ {\r\n\treturn Date.now();\r\n} \r\n \r\n```", "configuration.inlayHints.parameterNames.enabled": "启用/禁用参数名称的叠加提示:\r\n```typescript\r\n\r\nparseInt(/* str: */ '123', /* radix: */ 8)\r\n \r\n```", "configuration.inlayHints.parameterNames.suppressWhenArgumentMatchesName": "对于文本与参数名称完全相同的参数，抑制其参数名称提示。", "configuration.inlayHints.parameterTypes.enabled": "启用/禁用隐式参数类型的嵌入提示:\r\n```typescript\r\n\r\nel.addEventListener('click', e /* :MouseEvent */ => ...)\r\n \r\n```", "configuration.inlayHints.propertyDeclarationTypes.enabled": "启用/禁用属性声明中隐式类型的嵌入提示:\r\n```typescript\r\n\r\nclass Foo {\r\n\tprop /* :number */ = Date.now();\r\n}\r\n \r\n```", "configuration.inlayHints.variableTypes.enabled": "启用/禁用隐式变量类型的嵌入提示:\r\n```typescript\r\n\r\nconst foo /* :number */ = Date.now();\r\n \r\n```", "configuration.inlayHints.variableTypes.suppressWhenTypeMatchesName": "抑制关于名称与类型名称相同的变量的类型提示。", "configuration.javascript.checkJs.checkJs.deprecation": "为支持 `js/ts.implicitProjectConfig.checkJs`，已弃用此设置。", "configuration.javascript.checkJs.experimentalDecorators.deprecation": "为支持 `js/ts.implicitProjectConfig.experimentalDecorators`，已弃用此设置。", "configuration.preferGoToSourceDefinition": "通过改为触发“转到源定义”，使“转到定义”尽可能避免类型声明文件。这样就可使用鼠标手势触发“转到源定义”。", "configuration.preferences": "首选项", "configuration.server": "TS 服务器", "configuration.suggest": "建议", "configuration.suggest.autoImports": "启用/禁用自动导入建议。", "configuration.suggest.classMemberSnippets.enabled": "启用/禁用类成员的代码段完成。", "configuration.suggest.completeFunctionCalls": "完成函数的参数签名。", "configuration.suggest.completeJSDocs": "启用/禁用对完成 JSDoc 注释的建议。", "configuration.suggest.includeAutomaticOptionalChainCompletions": "启用/禁用显示可能未定义的值的完成情况，这些值会插入可选的链式调用。需要启用严格的 Null 检查。", "configuration.suggest.includeCompletionsForImportStatements": "对部分键入的导入语句启用/禁用自动导入样式完成。", "configuration.suggest.jsdoc.generateReturns": "启用/禁用为 JSDoc 模板生成 `@returns` 批注。", "configuration.suggest.names": "启用/禁用在 JavaScript 建议中包含文件中的唯一名称。请注意，在使用`@ts-check`或`checkJs`进行语义检查的 JavaScript 代码中，名称建议始终处于禁用状态。", "configuration.suggest.objectLiteralMethodSnippets.enabled": "启用/禁用对象文本中的方法的代码片段补全。", "configuration.suggest.paths": "在 import 语句和 require 调用中，启用或禁用路径建议。", "configuration.tsserver.experimental.enableProjectDiagnostics": "启用项目范围的错误报告。", "configuration.tsserver.maxTsServerMemory": "要分配给 TypeScript 服务器进程) 以 MB 为 (的最大内存量。如果要使用大于 4 GB 的内存限制，请使用 `#typescript.tsserver.nodePath#` 运行具有自定义节点安装的 TS Server。", "configuration.tsserver.nodePath": "在自定义节点安装上运行 TS 服务器。如果希望 VS Code 检测节点安装，则可以是节点可执行文件的路径，也可以是 “node”。", "configuration.tsserver.useSeparateSyntaxServer": "启用/禁用生成可更快地响应语法相关操作的单独 TypeScript 服务器，例如计算折叠或计算文档符号。", "configuration.tsserver.useSeparateSyntaxServer.deprecation": "此设置已弃用，取而代之的是“typescript.tsserver.useSyntaxServer”。", "configuration.tsserver.useSyntaxServer": "控制 TypeScript 是否启动专用服务器，以便更快地处理与语法相关的运算，如计算代码折叠。", "configuration.tsserver.useSyntaxServer.always": "使用更加轻量级的语法服务器来处理所有 IntelliSense 运算。此语法服务器只能为打开的文件提供 IntelliSense。", "configuration.tsserver.useSyntaxServer.auto": "生成一个完整的服务器和一个专用于语法运算的轻量级服务器。语法服务器用于加快语法运算并在加载项目时提供 IntelliSense。", "configuration.tsserver.useSyntaxServer.never": "请不要使用专用的语法服务器。使用单个服务器来处理所有 IntelliSense 运算。", "configuration.tsserver.useVsCodeWatcher": "使用 VS Code 而不是 TypeScript 的文件观察程序。要求在工作区中使用 TypeScript 5.4+。", "configuration.tsserver.useVsCodeWatcher.deprecation": "请改用 `#typescript.tsserver.watchOptions#` 设置。", "configuration.tsserver.watchOptions": "配置应使用哪些监视策略来跟踪文件和目录。", "configuration.tsserver.watchOptions.fallbackPolling": "使用文件系统事件时，此选项指定当系统用完本机文件观察程序和/或不支持本机文件观察程序时使用的轮询策略。", "configuration.tsserver.watchOptions.fallbackPolling.dynamicPriorityPolling ": "使用动态队列，在该队列中，较少检查不经常修改的文件。", "configuration.tsserver.watchOptions.fallbackPolling.fixedPollingInterval": "以固定间隔每秒多次检查每个文件的更改。", "configuration.tsserver.watchOptions.fallbackPolling.priorityPollingInterval": "每秒检查每个文件有无多次更改，但使用启发式方法检查某些类型的文件的频率低于其他文件类型。", "configuration.tsserver.watchOptions.synchronousWatchDirectory": "禁用目录上的延迟监视。当可能同时发生大量文件更改(例如，运行 npm install 导致的 node_modules 更改)时，延迟监视非常有用，但是对于一些不太常见的设置，可能需要使用此标志将其禁用。", "configuration.tsserver.watchOptions.vscode": "使用 VS Code 而不是 TypeScript 的文件观察程序。要求在工作区中使用 TypeScript 5.4+。", "configuration.tsserver.watchOptions.watchDirectory": "在缺乏递归文件监视功能的系统中监视整个目录树的策略。", "configuration.tsserver.watchOptions.watchDirectory.dynamicPriorityPolling": "使用动态队列，其中较少修改的目录将较少检查。", "configuration.tsserver.watchOptions.watchDirectory.fixedChunkSizePolling": "定期轮询区块中的目录。", "configuration.tsserver.watchOptions.watchDirectory.fixedPollingInterval": "以固定间隔每秒多次检查每个目录的更改。", "configuration.tsserver.watchOptions.watchDirectory.useFsEvents": "尝试使用操作系统/文件系统的本机事件进行目录更改。", "configuration.tsserver.watchOptions.watchFile": "如何监视单个文件的策略。", "configuration.tsserver.watchOptions.watchFile.dynamicPriorityPolling": "使用动态队列，在该队列中，较少检查不经常修改的文件。", "configuration.tsserver.watchOptions.watchFile.fixedChunkSizePolling": "定期轮询区块中的文件。", "configuration.tsserver.watchOptions.watchFile.fixedPollingInterval": "以固定间隔每秒多次检查每个文件的更改。", "configuration.tsserver.watchOptions.watchFile.priorityPollingInterval": "每秒多次检查每个文件的更改，但使用启发方法按不同频率检查不同类型的文件。", "configuration.tsserver.watchOptions.watchFile.useFsEvents": "尝试使用操作系统/文件系统的本机事件进行文件更改。", "configuration.tsserver.watchOptions.watchFile.useFsEventsOnParentDirectory": "尝试使用操作系统/文件系统的本机事件来侦听文件包含目录的更改。此操作可减少使用的文件观察程序数量，但准确度可能较低。", "configuration.tsserver.web.projectWideIntellisense.enabled": "在 Web 上启用/禁用项目范围内的 IntelliSense。要求 VS Code 在受信任的上下文中运行。", "configuration.tsserver.web.projectWideIntellisense.suppressSemanticErrors": "即使启用了项目范围的 IntelliSense，仍会抑制 Web 上出现语义错误。当项目范围的 IntelliSense 未启用或不可用时，此功能将始终可用。请参阅 `#typescript.tsserver.web.projectWideIntellisense.enabled#`", "configuration.tsserver.web.typeAcquisition.enabled": "在 Web 上启用/禁用包获取。这会为导入的包启用 IntelliSense。需要 `#typescript.tsserver.web.projectWideIntellisense.enabled#`。目前不支持 Safari。", "configuration.typescript": "TypeScript", "configuration.updateImportsOnPaste": "粘贴代码时自动更新导入项。需要 TypeScript 5.6+。", "description": "为 JavaScript 和 TypeScript 提供丰富的语言支持。", "displayName": "JavaScript 和 TypeScript 的语言功能", "format.indentSwitchCase": "switch 语句中的缩进 case 子句。需要在工作区中使用 TypeScript 5.1+。", "format.insertSpaceAfterCommaDelimiter": "定义逗号分隔符后面的空格处理。", "format.insertSpaceAfterConstructor": "定义构造函数关键字后面的空格处理方式。", "format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": "定义匿名函数的函数关键字后面的空格处理。", "format.insertSpaceAfterKeywordsInControlFlowStatements": "定义控制流语句中关键字后面的空格处理。", "format.insertSpaceAfterOpeningAndBeforeClosingEmptyBraces": "定义空大括号中左括号后和右括号前的空格处理方式。", "format.insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces": "定义 JSX 表达式括号中左括号后和右括号前的空格处理方式。", "format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": "定义非空大括号中左括号后和右括号前的空格处理方式。", "format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": "定义非空中括号的左括号后和右括号前的空格处理方式。", "format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": "定义非空小括号的左括号后和右括号前的空格处理方式。", "format.insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces": "定义模板字符串括号中左括号后和右括号前的空格处理方式。", "format.insertSpaceAfterSemicolonInForStatements": "定义 for 语句中分号之后的空格处理方式。", "format.insertSpaceAfterTypeAssertion": "定义 TypeScript 中类型断言后的空格处理方式。", "format.insertSpaceBeforeAndAfterBinaryOperators": "定义二进制运算符后面的空格处理", "format.insertSpaceBeforeFunctionParenthesis": "定义函数参数括号前的空格处理方式。", "format.placeOpenBraceOnNewLineForControlBlocks": "定义控制块的左括号是否放置在新的一行。", "format.placeOpenBraceOnNewLineForFunctions": "定义函数的左大括号是否放置在新的一行。", "format.semicolons": "定义如何处理可选分号。", "format.semicolons.ignore": "不要插入或删除任何分号。", "format.semicolons.insert": "在语句末尾插入分号。", "format.semicolons.remove": "删除不必要的分号。", "inlayHints.parameterNames.all": "启用文本和非文本参数的参数名称提示。", "inlayHints.parameterNames.literals": "仅启用文本参数的参数名称提示。", "inlayHints.parameterNames.none": "禁用参数名称提示。", "javascript.format.enable": "启用/禁用 JavaScript 格式化程序。", "javascript.goToProjectConfig.title": "转到项目配置(jsconfig/tsconfig)", "javascript.preferences.jsxAttributeCompletionStyle.auto": "根据属性类型在属性名称后插入 `={}` 或 `=\"\"`。请参见 `#javascript.preferences.quoteStyle#`，控制用于字符串属性的引号样式。", "javascript.preferences.organizeImports": "控制导入顺序的高级首选项。", "javascript.referencesCodeLens.enabled": "启用/禁用在 JavaScript 文件中引用 CodeLens。", "javascript.referencesCodeLens.showOnAllFunctions": "启用/禁用在 JavaScript 文件中对所有函数的 CodeLens 引用。", "javascript.suggestionActions.enabled": "启用或禁用编辑器中 JavaScript 文件的建议诊断。", "javascript.validate.enable": "启用/禁用 JavaScript 验证。", "reloadProjects.title": "重载项目", "taskDefinition.tsconfig.description": "定义 ts 生成的 tsconfig 文件。", "typescript.autoClosingTags": "启用/禁用 JSX 标记的自动关闭。", "typescript.check.npmIsInstalled": "检查是否为 [自动类型获取](https://code.visualstudio.com/docs/nodejs/working-with-javascript#_typings-and-automatic-type-acquisition) 安装了 npm 。", "typescript.disableAutomaticTypeAcquisition": "禁用 [自动类型获取](https://code.visualstudio.com/docs/nodejs/working-with-javascript#_typings-and-automatic-type-acquisition) 。自动类型获取可以从 npm 提取 `@types` 包来改进外部库的 IntelliSense。", "typescript.enablePromptUseWorkspaceTsdk": "允许提示用户对 Intellisense 使用在工作区中配置的 TypeScript 版本。", "typescript.findAllFileReferences": "查找文件引用", "typescript.format.enable": "启用/禁用默认 TypeScript 格式化程序。", "typescript.goToProjectConfig.title": "转到项目配置(tsconfig)", "typescript.goToSourceDefinition": "转到源定义", "typescript.implementationsCodeLens.enabled": "启用或禁用实现 CodeLens。此 CodeLens 显示接口的实现。", "typescript.implementationsCodeLens.showOnInterfaceMethods": "在接口方法上启用/禁用 CodeLens 实现。", "typescript.locale": "设置在报告 JavaScript 和 TypeScript 错误时使用的区域设置。默认使用 VS Code 的区域设置。", "typescript.locale.auto": "使用 VS Code 配置的显示语言。", "typescript.npm": "指定为 [自动类型获取](https://code.visualstudio.com/docs/nodejs/working-with-javascript#_typings-and-automatic-type-acquisition) 使用的 npm 可执行文件的路径。", "typescript.openTsServerLog.title": "打开 TS 服务器日志", "typescript.preferences.autoImportFileExcludePatterns": "从自动导入中排除指定文件的 glob 模式。相对路径是相对于工作区进行解析的。使用 tsconfig.json ['exclude'](https://www.typescriptlang.org/tsconfig#exclude)语义计算模式。", "typescript.preferences.autoImportSpecifierExcludeRegexes": "指定正则表达式以排除包含匹配导入说明符的自动导入。示例:\r\n\r\n- `^node:`\r\n- `lib/internal` (无需对斜杠进行转义...)\r\n- `/lib\\/internal/i` (...除非包括 `i` 或 `u` 标志周围的斜杠)\r\n- `^lodash$` (仅允许从 lodash 进行子路径导入)", "typescript.preferences.importModuleSpecifier": "自动 import 语句中路径的首选样式。", "typescript.preferences.importModuleSpecifier.nonRelative": "根据在 `jsconfig.json` / `tsconfig.json` 中配置的 `baseUrl` 或 `paths` 首选不相关导入。", "typescript.preferences.importModuleSpecifier.projectRelative": "仅当相对导入路径从包或项目目录中移除后，才首选使用非相对导入。", "typescript.preferences.importModuleSpecifier.relative": "首选导入文件位置的相对路径。", "typescript.preferences.importModuleSpecifier.shortest": "仅当有路径段少于相关导入路径段的不相关导入时，才首选不相关导入。", "typescript.preferences.importModuleSpecifierEnding": "自动导入的首选路径结尾。", "typescript.preferences.importModuleSpecifierEnding.auto": "使用项目设置选择默认值。", "typescript.preferences.importModuleSpecifierEnding.index": "将 \"./component/index.js\" 缩短为 \"./component/index\"。", "typescript.preferences.importModuleSpecifierEnding.js": "不要缩短路径结尾；包括 \".js\" 或 \".ts\" 扩展。", "typescript.preferences.importModuleSpecifierEnding.label.js": ".js / .ts", "typescript.preferences.importModuleSpecifierEnding.minimal": "将 \"./component/index.js\" 缩短为 \"./component\"。", "typescript.preferences.includePackageJsonAutoImports": "允许/禁止在 \"package.json\" 依赖项中搜索可用的自动导入。", "typescript.preferences.includePackageJsonAutoImports.auto": "根据预估的性能影响搜索依赖项。", "typescript.preferences.includePackageJsonAutoImports.off": "从不搜索依赖项。", "typescript.preferences.includePackageJsonAutoImports.on": "始终搜索依赖项。", "typescript.preferences.jsxAttributeCompletionStyle": "JSX 属性完成的首选样式。", "typescript.preferences.jsxAttributeCompletionStyle.auto": "根据属性类型在属性名称后插入 `={}` 或 `=\"\"`。请参见 `#typescript.preferences.quoteStyle#`，控制用于字符串属性的引号样式。", "typescript.preferences.jsxAttributeCompletionStyle.braces": "在属性名称后插入 `={}`。", "typescript.preferences.jsxAttributeCompletionStyle.none": "仅插入属性名称。", "typescript.preferences.organizeImports": "控制导入顺序的高级首选项。", "typescript.preferences.organizeImports.accentCollation": "需要 `organizeImports.unicodeCollation: 'unicode'`。将带变音符号的字符与基本字符比较，将二者视为不相等。", "typescript.preferences.organizeImports.caseFirst": "需要 `organizeImports.unicodeCollation: 'unicode'`，而 `organizeImports.caseSensitivity` 不是 `caseInsensitive`。指示大写是否排列在小写前面。", "typescript.preferences.organizeImports.caseFirst.default": "`locale` 指定的默认顺序。", "typescript.preferences.organizeImports.caseFirst.lower": "小写放在大写前面。例如 ` a, A, z, Z`。", "typescript.preferences.organizeImports.caseFirst.upper": "大写放在小写前面。例如 ` A, a, B, b`。", "typescript.preferences.organizeImports.caseSensitivity": "指定如何根据大小写区分对导入进行排序。如果 `auto` 或未指定，我们将根据每个文件检测大小写区分", "typescript.preferences.organizeImports.caseSensitivity.auto": "检测导入排序是否区分大小写。", "typescript.preferences.organizeImports.caseSensitivity.insensitive": "按区分大小写的方式对导入进行排序。", "typescript.preferences.organizeImports.caseSensitivity.sensitive": "按区分大小写的方式对导数进行排序。", "typescript.preferences.organizeImports.locale": "需要 `organizeImports.unicodeCollation: 'unicode'`。替代用于排序规则的区域设置。指定 `auto` 以使用 UI 区域设置。", "typescript.preferences.organizeImports.numericCollation": "需要 `organizeImports.unicodeCollation: 'unicode'`。按整数值对数值字符串进行排序。", "typescript.preferences.organizeImports.typeOrder": "指定应如何对仅类型命名的导入进行排序。", "typescript.preferences.organizeImports.typeOrder.auto": "检测应在何处对仅类型命名的导入进行排序。", "typescript.preferences.organizeImports.typeOrder.first": "将仅类型命名导入排序到导入列表开头。例如，`import { type A, type Y, B, Z } from 'module';`", "typescript.preferences.organizeImports.typeOrder.inline": "命名导入仅按名称排序。例如，`import { type A, B, type Y, Z } from 'module';`", "typescript.preferences.organizeImports.typeOrder.last": "将仅类型命名导入排序到导入列表末尾。例如，`import { B, Z, type A, type Y } from 'module';`", "typescript.preferences.organizeImports.unicodeCollation": "指定是使用 Unicode 还是序号排序规则对导入进行排序。", "typescript.preferences.organizeImports.unicodeCollation.ordinal": "使用每个码位元素的数值对导入进行排序。", "typescript.preferences.organizeImports.unicodeCollation.unicode": "使用 Unicode 代码排序规则对导入进行排序。", "typescript.preferences.preferTypeOnlyAutoImports": "尽可能在自动导入中包含 `type` 关键字。要求在工作区中使用 TypeScript 5.3+。", "typescript.preferences.quoteStyle": "用于快速修复的首选引文样式。", "typescript.preferences.quoteStyle.auto": "从现有代码推断引号类型", "typescript.preferences.quoteStyle.double": "始终使用双引号: `\"`", "typescript.preferences.quoteStyle.single": "始终使用单引号: `'`", "typescript.preferences.renameMatchingJsxTags": "在 JSX 标记上时，请尝试重命名匹配的标记，而不是重命名符号。要求在工作区中使用 TypeScript 5.1+。", "typescript.preferences.renameShorthandProperties.deprecationMessage": "设置 \"typescript.preferences.renameShorthandProperties\" 已被弃用，取而代之的是 \"typescript.preferences.useAliasesForRenames\"", "typescript.preferences.useAliasesForRenames": "启用/禁用在重命名期间为对象速记属性引入别名。", "typescript.problemMatchers.tsc.label": "TypeScript 问题", "typescript.problemMatchers.tscWatch.label": "TypeScript 问题(观看模式)", "typescript.referencesCodeLens.enabled": "在 TypeScript 文件中启用或禁用引用 CodeLens。", "typescript.referencesCodeLens.showOnAllFunctions": "启用/禁用在 TypeScript 文件中的所有函数上引用 CodeLens。", "typescript.removeUnusedImports": "删除未使用的导入", "typescript.reportStyleChecksAsWarnings": "将风格检查的问题报告为警告。", "typescript.restartTsServer": "重启 TS 服务器", "typescript.selectTypeScriptVersion.title": "选择 TypeScript 版本...", "typescript.sortImports": "对导入进行排序", "typescript.suggest.enabled": "启用/禁用自动完成建议。", "typescript.suggestionActions.enabled": "启用或禁用编辑器中 TypeScript 文件的建议诊断。", "typescript.tsc.autoDetect": "控制对 tsc 任务的自动检测。", "typescript.tsc.autoDetect.build": "仅创建单次运行编译任务。", "typescript.tsc.autoDetect.off": "禁用此功能。", "typescript.tsc.autoDetect.on": "同时创建生成和监视任务。", "typescript.tsc.autoDetect.watch": "仅创建编译和监视任务。", "typescript.tsdk.desc": "指定 TypeScript 安装下用于 IntelliSense 的 tsserver 和 `lib*.d.ts` 文件的文件夹路径，例如: `./node_modules/typescript/lib`。\r\n\r\n- 当指定为用户设置时，`typescript.tsdk` 中的 TypeScript 版本会自动替换内置的 TypeScript 版本。\r\n- 当指定为工作区设置时，`typescript.tsdk` 允许通过 `TypeScript: Select TypeScript version` 命令切换为对 IntelliSense 使用 TypeScript 的该工作区版本。\r\n\r\n有关管理 TypeScript 版本的更多详细信息，请参阅 [TypeScript文档](https://code.visualstudio.com/docs/typescript/typescript-compiling#_using-newer-typescript-versions)。", "typescript.tsserver.enableRegionDiagnostics": "使用 TypeScript 启用基于区域的诊断。要求在工作区中使用 TypeScript 5.6+。", "typescript.tsserver.enableTracing": "允许将 TS 服务器性能跟踪保持到目录。这些跟踪文件可用于诊断 TS 服务器性能问题。日志可能包含你的项目中的文件路径、源代码和其他可能敏感的信息。", "typescript.tsserver.log": "将 TS 服务器的日志保存到一个文件。此日志可用于诊断 TS 服务器问题。日志可能包含你的项目中的文件路径、源代码和其他可能敏感的信息。", "typescript.tsserver.pluginPaths": "其他用于搜索 TypeScript 语言服务插件的路径。", "typescript.tsserver.pluginPaths.item": "相对或绝对路径。相对路径将根据工作区文件夹进行解析。", "typescript.tsserver.trace": "对发送到 TS 服务器的消息启用跟踪。此跟踪信息可用于诊断 TS 服务器问题。 跟踪信息可能包含你的项目中的文件路径、源代码和其他可能敏感的信息。", "typescript.updateImportsOnFileMove.enabled": "启用或禁用在 VS Code 中重命名或移动文件时自动更新导入路径的功能。", "typescript.updateImportsOnFileMove.enabled.always": "始终自动更新路径。", "typescript.updateImportsOnFileMove.enabled.never": "一律不要重命名路径，也不要提示。", "typescript.updateImportsOnFileMove.enabled.prompt": "在每次重命名时进行提示。", "typescript.useTsgo": "禁用 TypeScript 和 JavaScript 语言功能以允许使用 TypeScript Go 实验性扩展。需要安装和配置 TypeScript Go。更改此设置后需要重新加载扩展。", "typescript.validate.enable": "启用/禁用 TypeScript 验证。", "typescript.workspaceSymbols.excludeLibrarySymbols": "排除“转到工作区中的符号”结果中库文件中的符号。要求在工作区中使用 TypeScript 5.3+。", "typescript.workspaceSymbols.scope": "通过[转到工作区中的符号](https://code.visualstudio.com/docs/editor/editingevolved#_open-symbol-by-name)来控制搜索的具体文件。", "typescript.workspaceSymbols.scope.allOpenProjects": "在所有打开的 JavaScript 或 TypeScript 项目中搜索符号。", "typescript.workspaceSymbols.scope.currentProject": "仅在当前 JavaScript 或 TypeScript 项目中搜索符号。", "virtualWorkspaces": "在虚拟工作区中，不支持解析和查找跨文件的引用。", "walkthroughs.nodejsWelcome.debugJsFile.altText": "使用 Visual Studio Code 在 Node.js 中调试并运行 JavaScript 代码。", "walkthroughs.nodejsWelcome.debugJsFile.description": "安装 Node.js 后，可以在终端输入 ``node your-file-name.js`` 来运行 Node.js 程序\r\n另一个简单的方法是使用 VS Code 调试器，它可以运行 Node.js 程序，在不同点暂停，并帮助你逐步了解执行的操作。\r\n[开始调试](命令: javascript-walkthrough.commands.debugJsFile)", "walkthroughs.nodejsWelcome.debugJsFile.title": "运行和调试 JavaScript", "walkthroughs.nodejsWelcome.description": "充分利用 Visual Studio Code 一流的 JavaScript 体验。", "walkthroughs.nodejsWelcome.downloadNode.forLinux.description": "Node.js 是运行 JavaScript 代码的简单方法。可以使用它快速生成命令行应用和服务器。它还附带 npm，一个包管理器，可用来轻松重用和共享 JavaScript 代码。\r\n[安装 Node.js](https://nodejs.org/en/download/package-manager/)", "walkthroughs.nodejsWelcome.downloadNode.forLinux.title": "安装 Node.js", "walkthroughs.nodejsWelcome.downloadNode.forMacOrWindows.description": "Node.js 是运行 JavaScript 代码的简单方法。可以使用它快速生成命令行应用和服务器。它还附带 npm，一个包管理器，可用来轻松重用和共享 JavaScript 代码。\r\n[安装 Node.js](https://nodejs.org/en/download/)", "walkthroughs.nodejsWelcome.downloadNode.forMacOrWindows.title": "安装 Node.js", "walkthroughs.nodejsWelcome.learnMoreAboutJs.altText": "详细了解 Visual Studio Code 中的 JavaScript 和 Node.js。", "walkthroughs.nodejsWelcome.learnMoreAboutJs.description": "想要更舒适地使用 JavaScript、Node.js 和 VS Code? 请务必看看我们的文档!\r\n我们有很多 [JavaScript](https://code.visualstudio.com/docs/nodejs/working-with-javascript) 和 [Node.js](https://code.visualstudio.com/docs/nodejs/nodejs-tutorial)的学习资源。\r\n\r\n[详细了解](https://code.visualstudio.com/docs/nodejs/nodejs-tutorial)", "walkthroughs.nodejsWelcome.learnMoreAboutJs.title": "浏览更多", "walkthroughs.nodejsWelcome.makeJsFile.description": "让我们来编写第一个 JavaScript 文件。我们必须创建一个新文件，并在文件名末尾使用 \".js\" 扩展名保存该文件。\r\n[创建 JavaScript 文件](命令: javascript-walkthrough.commands.createJsFile)", "walkthroughs.nodejsWelcome.makeJsFile.title": "创建 JavaScript 文件", "walkthroughs.nodejsWelcome.title": "开始使用 JavaScript 和 Node.js", "workspaceTrust": "使用工作区版本时，扩展需要工作区信任，因为它会执行工作区指定的代码。"}}}