{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Insert Image as Attachment": "将图像插入为附件"}, "package": {"addCellOutputToChat.title": "将单元格输出添加到聊天", "cleanInvalidImageAttachment.title": "清理无效图像附件引用", "copyCellOutput.title": "复制单元格输出", "description": "为打开和读取 Jupyter 的 .ipynb 笔记本文件提供基本支持", "displayName": ".ipynb 支持", "ipynb.experimental.serialization": "用于在工作线程中序列化 Jupyter 笔记本的试验性功能。", "ipynb.pasteImagesAsAttachments.enabled": "启用/禁用将图像粘贴到 ipynb 笔记本文件中的 Markdown 单元格中。粘贴的图像会作为附件插入到单元格。", "markdownAttachmentRenderer.displayName": "Markdown-It ipynb 单元格附件呈现器", "newUntitledIpynb.shortTitle": "Jupyter Notebook", "newUntitledIpynb.title": "新 Jupyter Notebook", "openCellOutput.title": "在文本编辑器中打开单元输出", "openIpynbInNotebookEditor.title": "在笔记本编辑器中打开 IPYNB 文件"}}}