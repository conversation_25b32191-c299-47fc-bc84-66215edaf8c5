{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Callers Of": "以下项调用方", "Calls From": "调用来自", "No results.": "无结果。", "No results. Try running a previous search again:": "无结果。再次尝试运行上一个搜索:", "Open Call": "打开调用", "Open Reference": "打开引用", "Open Type": "打开类型", "References": "引用", "Rerun": "重新运行", "Select previous reference search": "选择以前的引用搜索", "Subtypes Of": "以下项的子类型", "Supertypes Of": "以下项的超类型", "{0} result in {1} file": "{0} 个结果，包含于 {1} 个文件中", "{0} result in {1} files": "{1} 文件中有 {0} 个结果", "{0} results in {1} file": "{1} 文件中有 {0} 个结果", "{0} results in {1} files": "{1} 文件中有 {0} 个结果"}, "package": {"cmd.category.references": "引用", "cmd.references-view.clear": "清除", "cmd.references-view.clearHistory": "清除历史记录", "cmd.references-view.copy": "复制", "cmd.references-view.copyAll": "全部复制", "cmd.references-view.copyPath": "复制路径", "cmd.references-view.findImplementations": "查找所有实现", "cmd.references-view.findReferences": "查找所有引用", "cmd.references-view.next": "转到下一个引用", "cmd.references-view.pickFromHistory": "显示历史记录", "cmd.references-view.prev": "转到上一个引用", "cmd.references-view.refind": "重新运行", "cmd.references-view.refresh": "刷新", "cmd.references-view.removeCallItem": "取消", "cmd.references-view.removeReferenceItem": "关闭", "cmd.references-view.removeTypeItem": "关闭", "cmd.references-view.showCallHierarchy": "显示调用层次结构", "cmd.references-view.showIncomingCalls": "显示来电", "cmd.references-view.showOutgoingCalls": "显示去电", "cmd.references-view.showSubtypes": "显示子类型", "cmd.references-view.showSupertypes": "显示超类型", "cmd.references-view.showTypeHierarchy": "显示类型层次结构", "config.references.preferredLocation": "控制在选择代码信息指示器引用时是否调用“速览引用”或“查找引用”。", "config.references.preferredLocation.peek": "在速览编辑器中显示引用。", "config.references.preferredLocation.view": "在单独的视图中显示引用。", "container.title": "引用", "description": "在边栏中以独立稳定的视图引用搜索结果", "displayName": "引用搜索视图", "view.title": "引用搜索结果"}}}