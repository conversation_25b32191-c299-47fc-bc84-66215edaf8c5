{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Cannot validate since a PHP installation could not be found. Use the setting 'php.validate.executablePath' to configure the PHP executable.": "无法验证，因为找不到 PHP 安装。使用设置 \"php.validate.executablePath\" 来配置 PHP 可执行文件。", "Cannot validate since no PHP executable is set. Use the setting 'php.validate.executablePath' to configure the PHP executable.": "无法验证，因为未设置任何 PHP 可执行文件。请使用设置 \"php.validate.executablePath\" 配置 PHP 可执行文件。", "Cannot validate since {0} is not a valid php executable. Use the setting 'php.validate.executablePath' to configure the PHP executable.": "无法验证，因为 {0} 不是有效的 PHP 可执行文件。请使用设置 \"php.validate.executablePath\" 配置 PHP 可执行文件。", "Failed to run php using path: {0}. Reason is unknown.": "使用路径运行 php 失败: {0}。原因未知。", "Open Settings": "打开设置"}, "package": {"command.untrustValidationExecutable": "禁止 PHP 验证程序(定义为工作区设置)", "commands.categroy.php": "PHP", "configuration.suggest.basic": "控制是否启用内置 PHP 语言建议。支持对 PHP 全局变量和变量进行建议。", "configuration.title": "PHP", "configuration.validate.enable": "启用/禁用内置的 PHP 验证。", "configuration.validate.executablePath": "指向 PHP 可执行文件。", "configuration.validate.run": "不管 linter 是在 save 还是在 type 上运行。", "description": "为 PHP 文件提供丰富的语言支持。", "displayName": "php 语言功能", "workspaceTrust": "当 \"php.validate.executablePath\" 设置将在工作区中加载 PHP 版本时，扩展需要工作区信任。"}}}