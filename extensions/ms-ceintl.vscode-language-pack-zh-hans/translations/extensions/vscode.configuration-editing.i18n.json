{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Example": "示例", "Files by Extension": "按扩展名的文件", "Files with Extension": "带扩展名的文件", "Files with Multiple Extensions": "具有多个扩展名的文件", "Files with Path": "带路径的文件", "Files with Siblings by Name": "具有同级文件的文件(按名称)", "Folder by Name (Any Location)": "按名称的文件夹(任意位置)", "Folder by Name (Top Level)": "按名称的文件夹(顶级)", "Folders with Multiple Names (Top Level)": "使用多个名称的文件夹(顶级)", "GitHub": "GitHub", "Map all files matching the absolute path glob pattern in their path to the language with the given identifier.": "将所有匹配其路径内绝对路径 glob 模式的文件映射到具有给定标识符的语言。", "Map all files matching the glob pattern in their filename to the language with the given identifier.": "将所有匹配其文件名内的 glob 模式的文件映射到具有给定标识符的语言。", "Match a folder with a specific name in any location.": "与任意位置具有特定名称的文件夹匹配。", "Match a top level folder with a specific name.": "与具有特定名称的顶级文件夹匹配。", "Match all files of a specific file extension.": "与具有特定文件扩展名的所有文件匹配。", "Match all files with any of the file extensions.": "与具有任意文件扩展名的所有文件匹配。", "Match files that have siblings with the same name but a different extension.": "与具有名称相同但扩展名不同的同级文件的文件匹配。", "Match multiple top level folders.": "与多个顶级文件夹匹配。", "The character used by the operating system to separate components in file paths. Is also aliased to '/'.": "操作系统用于在文件路径中分隔组件的字符。也别名于“/”。", "The current opened file": "当前打开的文件", "The current opened file relative to ${workspaceFolder}": "相对于 ${workspaceFolder}，当前打开的文件路径", "The current opened file workspace folder name without any slashes (/)": "当前打开的文件工作区文件夹名称(不带任何斜杠(/))", "The current opened file's basename": "当前打开的文件的文件名", "The current opened file's basename with no file extension": "当前打开的文件的文件名 (不包含文件扩展名)", "The current opened file's dirname": "当前打开的文件的完整目录名", "The current opened file's dirname relative to ${workspaceFolder}": "当前打开的文件与 ${workspaceFolder} 相对的目录名", "The current opened file's extension": "当前打开文件的扩展名", "The current opened file's folder name": "当前打开的文件的文件夹名称", "The current selected line number in the active file": "活动文件中当前选定行的行号", "The current selected text in the active file": "当前在活动文件中选定的文本", "The file extension of the editor (e.g. txt)": "编辑器的文件扩展名(例如 txt)", "The file name of the editor without its directory or extension (e.g. myFile)": "没有目录或扩展名的编辑器的文件名(例如 myFile)", "The name of the default build task. If there is not a single default build task then a quick pick is shown to choose the build task.": "默认生成任务的名称。如果没有单个默认生成任务，则将显示快速选取以选择生成任务。", "The name of the folder opened in VS Code without any slashes (/)": "在 VS Code 中打开的文件夹的名称 (不包含任何斜杠 \"/\" )", "The nth parent folder name of the editor": "编辑器的第 n 个父文件夹名称", "The parent folder name of the editor (e.g. myFileFolder)": "编辑器的父文件夹名称(例如 myFileFolder)", "The path of the folder opened in VS Code": "在 VS Code 中打开的文件夹的路径", "The path where an extension is installed.": "安装扩展的路径。", "The task runner's current working directory on startup": "启动时任务运行程序的当前工作目录", "Use the language of the currently active text editor if any": "使用当前活动的文本编辑器的语言(如果有)", "a conditional separator (' - ') that only shows when surrounded by variables with values": "一个条件分隔符(\"-\")，仅在左右是具有值的变量时才显示", "an indicator for when the active editor has unsaved changes": "表明活动编辑器具有未保存更改的时间的指示器", "e.g. SSH": "例如 SSH", "e.g. VS Code": "例如 VS Code", "file path of the workspace (e.g. /Users/<USER>/myWorkspace)": "工作区路径 (例如 /Users/<USER>/myWorkspace)", "file path of the workspace folder the file is contained in (e.g. /Users/<USER>/myFolder)": "文件所在工作区文件夹的路径 (例如 /Users/<USER>/myFolder)", "gist": "Gist", "name of the workspace folder the file is contained in (e.g. myFolder)": "文件所在工作区文件夹的名称 (例如 myFolder)", "name of the workspace with optional remote name and workspace indicator if applicable (e.g. myFolder, myRemoteFolder [SSH] or myWorkspace (Workspace))": "具有可选远程名称和工作区指示器的工作区的名称(如果适用)(例如 myFolder、myRemoteFolder [SSH] 或 myWorkspace [工作区])", "shortened name of the workspace without suffixes (e.g. myFolder or myWorkspace)": "不带后缀的工作区缩短名称(例如 myFolder 或 myWorkspace)", "the file name (e.g. myFile.txt)": "文件名 (例如 myFile.txt)", "the full path of the file (e.g. /Users/<USER>/myFolder/myFileFolder/myFile.txt)": "文件的完整路径(例如，/Users/<USER>/myFolder/myFileFolder/myFile.txt)", "the full path of the folder the file is contained in (e.g. /Users/<USER>/myFolder/myFileFolder)": "包含文件的文件夹的完整路径(例如，/Users/<USER>/myFolder/myFileFolder)", "the name of the active branch in the active repository (e.g. main)": "活动存储库中活动分支的名称(例如 main)", "the name of the active repository (e.g. vscode)": "活动存储库的名称(例如 vscode)", "the name of the folder the file is contained in (e.g. myFileFolder)": "包含文件的文件夹的名称(例如 myFileFolder)。", "the path of the file relative to the workspace folder (e.g. myFolder/myFileFolder/myFile.txt)": "文件相对于工作区文件夹的路径(例如 myFolder/myFileFolder/myFile.txt)", "the path of the folder the file is contained in, relative to the workspace folder (e.g. myFolder/myFileFolder)": "文件所在的文件夹的路径，相对于工作区文件夹(例如 myFolder/myFileFolder)", "the state of the active editor (e.g. modified).": "活动编辑器的状态(例如已修改)。"}, "package": {"description": "在配置文件 (如设置、启动和扩展推荐文件) 中提供高级 IntelliSense、自动修复等功能", "displayName": "配置编辑"}}}