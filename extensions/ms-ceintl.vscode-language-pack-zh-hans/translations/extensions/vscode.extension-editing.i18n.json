{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Data URLs are not a valid image source.": "数据 URL 不是有效的图像源。", "Embedded SVGs are not a valid image source.": "嵌入的 SVG 不是有效的图像源。", "Error parsing the when-clause:": "分析 when 子句时出错:", "Images must use the HTTPS protocol.": "图像必须使用 HTTPS 协议。", "Language specific editor settings": "特定语言编辑器设置", "Override editor settings for language": "替代语言编辑器设置", "Relative badge URLs require a repository with HTTPS protocol to be specified in this package.json.": "相对徽章 URL 要求在 package.json 中指定使用 HTTPS 协议的仓库。", "Relative image URLs require a repository with HTTPS protocol to be specified in the package.json.": "相对映像 URL 要求在 package.json 中指定使用 HTTPS 协议的仓库。", "Remove activation event": "删除激活事件", "SVGs are not a valid image source.": "SVG 不是有效的图像源。", "This activation event can be removed as VS Code generates these automatically from your package.json contribution declarations.": "可以删除此激活事件，因为 VS Code 从 package.json 贡献声明中自动生成此类事件。", "This activation event can be removed for extensions targeting engine version ^1.75 as VS Code will generate these automatically from your package.json contribution declarations.": "对于面向引擎版本 ^1.75 的扩展，可以删除此激活事件，因为 VS Code将从 package.json 贡献声明自动生成这些事件。", "This activation event cannot be explicitly listed by your extension.": "扩展无法显式列出此激活事件。", "This proposal cannot be used because for this extension the product defines a fixed set of API proposals. You can test your extension but before publishing you MUST reach out to the VS Code team.": "无法使用此建议，因为对于此扩展，产品定义了一组固定的 API 建议。你可以测试扩展，但在发布之前，必须联系 VS Code 团队。", "Using '*' activation is usually a bad idea as it impacts performance.": "使用“*”激活通常是一个错误的想法，因为它会影响性能。"}, "package": {"description": "在创建扩展时提供 linting 功能。", "displayName": "扩展创建"}}}