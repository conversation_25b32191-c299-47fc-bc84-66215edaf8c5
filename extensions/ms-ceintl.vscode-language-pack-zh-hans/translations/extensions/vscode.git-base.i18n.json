{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Branch name": "分支名称", "Choose a URL to clone from.": "选择要从中进行克隆的 URL。", "No remote repositories found.": "未找到远程存储库。", "Provide repository URL": "提供仓库 URL", "Provide repository URL or pick a repository source.": "提供仓库 URL 或选择仓库源。", "Repository name": "仓库名称", "Repository name (type to search)": "仓库名称(键入内容进行搜索)", "URL": "URL", "recently opened": "最近打开", "remote sources": "远程源", "{0} Error: {1}": "{0} 错误: {1}"}, "package": {"command.api.getRemoteSources": "获取远程源", "description": "Git 静态贡献和选取器。", "displayName": "Git 基础"}}}