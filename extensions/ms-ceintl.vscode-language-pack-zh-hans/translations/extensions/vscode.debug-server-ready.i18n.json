{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Format uri ('{0}') must contain exactly one substitution placeholder.": "格式 uri (\"{0}\") 只能包含一个替换占位符。", "Format uri ('{0}') uses a substitution placeholder but pattern did not capture anything.": "格式 uri (\"{0}\") 使用替换占位符, 但模式没有捕获任何内容。"}, "package": {"debug.server.ready.action.debugWithChrome.description": "开始使用 \"Debugger for Chrome\" 进行调试。", "debug.server.ready.action.description": "当服务器准备就绪时，如何处理 URI。", "debug.server.ready.action.openExternally.description": "使用默认应用程序在外部打开 URI。", "debug.server.ready.action.startDebugging.description": "运行另一启动配置。", "debug.server.ready.debugConfig.description": "要运行的调试配置。", "debug.server.ready.debugConfigName.description": "要运行的启动配置的名称。", "debug.server.ready.killOnServerStop.description": "在父会话停止时停止子会话。", "debug.server.ready.pattern.description": "此模式出现在调试控制台上表示服务器已准备就绪。首个捕获组必须包含一个 URI 或端口号。", "debug.server.ready.serverReadyAction.description": "当正在调试的服务器程序准备就绪时，执行URI (通过 \"listening on port 3000\" 或 \"Now listening on: https://localhost:5001\" 的形式发送至调试控制台 )。", "debug.server.ready.uriFormat.description": "从端口号构造 URI 时使用的格式字符串。第一个 \"%s\" 将替换为端口号。", "debug.server.ready.webRoot.description": "传递给 \"Debugger for Chrome\" 调试配置的值。", "description": "如果正在调试的服务器已准备就绪，在浏览器中打开 URI。", "displayName": "服务器就绪操作"}}}