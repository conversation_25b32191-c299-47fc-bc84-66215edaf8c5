{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"\"store\" a boolean test for later evaluation in a guard or if().": "\"store\" 一个布尔测试，以便以后在 guard 或 if() 中评估。", "'from' expected": "应为“From”", "'in' expected": "应为 \"in\"", "'through' or 'to' expected": "应为 \"through\" 或 \"to\"", "'{0}'": "\"{0}\"", "( expected": "应为 (", ") expected": "应为 )", "<undefined>": "<未定义>", "@font-face": "@font-face", "@font-face rule must define 'src' and 'font-family' properties": "@font-face 规则必须定义 \"src\" 和 \"font-family\" 属性。", "@keyframes {0}": "@keyframes {0}", "A list of properties that are not validated against the `unknownProperties` rule.": "不根据 \"unknownProperties\" 规则进行验证的属性列表。", "Adds quotes to a string.": "将引号添加到字符串。", "Also define the standard property '{0}' for compatibility": "还定义标准属性“{0}”以实现兼容性", "Always define standard rule '@keyframes' when defining keyframes.": "定义关键帧时，始终定义标准规则 \"@keyframes\"。", "Always include all vendor specific properties: Missing: {0}": "始终包括所有供应商专属规则: 缺少: {0}", "Always include all vendor specific rules: Missing: {0}": "始终包括所有供应商专属规则: 缺少: {0}", "Appends a single value onto the end of a list.": "将单个值追加到列表末尾。", "Appends selectors to one another without spaces in between.": "将选择器追加到彼此，中间没有空格。", "Avoid using !important. It is an indication that the specificity of the entire CSS has gotten out of control and needs to be refactored.": "避免使用 !important。它表明整个 CSS 的优先级已经失去控制且需要进行重构。", "Avoid using 'float'. Floats lead to fragile CSS that is easy to break if one aspect of the layout changes.": "避免使用 \"float\"。float 会使 CSS 变得脆弱。即使只更改了一部分布局，也很容易造成破坏。", "CSS Language Server": "CSS 语言服务器", "CSS fix is outdated and can't be applied to the document.": "CSS 修复已过时，无法应用于文档。", "Causes one or more rules to be emitted at the root of the document.": "导致在文档的根目录发出一个或多个规则。", "Changes one or more properties of a color.": "更改颜色的一个或多个属性。", "Changes the alpha component for a color.": "更改颜色的 alpha 分量。", "Changes the hue of a color.": "更改颜色的色调。", "Combines several lists into a single multidimensional list.": "将多个列表合并到单个多维列表中。", "Converts a color into the format understood by IE filters.": "将颜色转换为 IE 筛选器理解的格式。", "Converts a color to grayscale.": "将颜色转换为灰度。", "Converts a string to lower case.": "将字符串转换为小写。", "Converts a string to upper case.": "将字符串转换为大写。", "Converts a unitless number to a percentage.": "将无单位数转换为百分比。", "Creates a Color from hue, saturation, and lightness values.": "根据色调、饱和度和亮度值创建颜色。", "Creates a Color from hue, saturation, lightness, and alpha values.": "根据色调、饱和度、亮度和 alpha 值创建颜色。", "Creates a Color from hue, white, and black values.": "根据色调、白色和黑色值创建颜色。", "Creates a Color from lightness, a, and b values.": "根据亮度、a 和 b 值创建颜色。", "Creates a Color from lightness, chroma, and hue values.": "根据亮度、色度和色调值创建颜色。", "Creates a Color from red, green, and blue values.": "从红色、绿色、蓝色值创建颜色。", "Creates a Color from red, green, blue, and alpha values.": "根据红色、绿色、蓝色和 alpha 值创建颜色。", "Creates a Color from the hue, saturation, and lightness values of another Color.": "根据另一种颜色的色调、饱和度和亮度值创建颜色。", "Creates a Color from the hue, white, and black values of another Color.": "根据另一种颜色的色调、白色和黑色值创建颜色。", "Creates a Color from the lightness, a, and b values of another Color.": "根据另一种颜色的亮度、a 和 b 值创建颜色。", "Creates a Color from the lightness, chroma, and hue values of another Color.": "根据另一种颜色的亮度、色度和色调值创建颜色。", "Creates a Color from the red, green, and blue values of another Color.": "根据另一个颜色的红色、绿色和蓝色值创建颜色。", "Creates a Color in a specific color space from red, green, and blue values.": "根据红色、绿色和蓝色值在特定颜色空间中创建颜色。", "Creates a Color in a specific color space from the red, green, and blue values of another Color.": "根据另一种颜色的红色、绿色和蓝色值在特定颜色空间中创建颜色。", "Defines complex operations that can be re-used throughout stylesheets.": "定义可在整个样式表中重复使用的复杂运算。", "Defines styles that can be re-used throughout the stylesheet with `@include`.": "定义了可以使用 @include 在整个样式表中重用的样式。", "Do not use duplicate style definitions": "不要使用重复的样式定义", "Do not use empty rulesets": "请勿使用空规则集", "Do not use width or height when using padding or border": "在使用 padding 或 border 时，不使用 width 或 height", "Dynamically calls a Sass function.": "动态调用 Sass 函数。", "Each loop that sets `$var` to each item in the list or map, then outputs the styles it contains using that value of `$var`.": "将 `$var` 设置为列表或映射中每个项的每个循环，然后使用该`$var` 的值输出它包含的样式。", "Exposes the details of Sass’s inner workings.": "公开 Sass 内部工作的详细信息。", "Extends $extendee with $extender within $selector.": "使用 $selector 中的 $extender 扩展 $extendee。", "Extracts a substring from $string.": "从 $string 中提取子字符串。", "Failed to apply CSS fix to the document. Please consider opening an issue with steps to reproduce.": "未能将 CSS 修补程序应用于文档。请考虑执行重现步骤以打开问题。", "Finds the maximum of several numbers.": "查找多个数字的最大值。", "Finds the minimum of several numbers.": "查找几个数字的最小值。", "Fluidly scales one or more properties of a color.": "流畅缩放颜色的一个或多个属性。", "Folding Region End": "折叠区域结束", "Folding Region Start": "折叠区域开始", "For loop that repeatedly outputs a set of styles for each `$var` in the `from/through` or `from/to` clause.": "For 循环，将为 `from/through` 或 `from/to` 子句中的每个 `$var` 重复输出一组样式。", "Generates new colors based on existing ones, making it easy to build color themes.": "基于现有颜色生成新颜色，从而轻松生成颜色主题。", "Gets the blue component of a color.": "获取颜色的蓝色分量。", "Gets the green component of a color.": "获取颜色的绿色分量。", "Gets the hue component of a color.": "获取颜色的色调分量。", "Gets the lightness component of a color.": "获取颜色的亮度分量。", "Gets the opacity component of a color.": "获取颜色的不透明度分量。", "Gets the red component of a color.": "获取颜色的红色分量。", "Gets the saturation component of a color.": "获取颜色的饱和度分量。", "Hex colors must consist of three, four, six or eight hex numbers": "十六进制颜色必须包含三个、四个、六个或八个十六进制数字", "IE hacks are only necessary when supporting IE7 and older": "仅在支持 IE7 及更低版本时，才需要 IE hack", "Import statements do not load in parallel": "import 语句没有并行加载", "Includes the body if the expression does not evaluate to `false` or `null`.": "如果表达式的计算结果不为 `false` 或 `null`，则包括正文。", "Includes the styles defined by another mixin into the current rule.": "包括其他 mixin 在当前规则中定义的样式。", "Increases or decreases one or more components of a color.": "提高或降低颜色的一个或多个分量。", "Inherits the styles of another selector.": "继承另一个选择器的样式。", "Insert url() Function": "插入 url() 函数", "Insert url() Functions": "插入 url() 函数", "Inserts $insert into $string at $index.": "在 $index 处将 $insert 插入到 $string 中。", "Invalid number of parameters": "参数数目无效", "Joins together two lists into one.": "将两个列表联接成一个。", "Lets you access and modify values in lists.": "让你访问和修改列表中的值。", "Loads a Sass stylesheet and makes its mixins, functions, and variables available when this stylesheet is loaded with the @use rule.": "加载 Sass 样式表，并在使用 @use 规则加载此样式表时使其 mixin、函数、变量可用。", "Loads mixins, functions, and variables from other Sass stylesheets as 'modules', and combines CSS from multiple stylesheets together.": "从其他 Sass 样式表加载 mixin、函数和变量作为“模块”，并将来自多个样式表的 CSS 组合在一起。", "Makes a color darker.": "使颜色变深。", "Makes a color less saturated.": "降低颜色饱和度。", "Makes a color lighter.": "使颜色变浅。", "Makes a color more opaque.": "使颜色更加不透明。", "Makes a color more saturated.": "使颜色更饱和。", "Makes a color more transparent.": "使颜色更透明。", "Makes it easy to combine, search, or split apart strings.": "使组合、搜索或拆分字符串变得容易。", "Makes it possible to look up the value associated with a key in a map, and much more.": "支持在映射中查找与键关联的值等功能。", "Merges two maps together into a new map.": "将两个地图合并成新地图。", "Mix two colors together in a polar color space.": "在极色空间中将两种颜色混合在一起。", "Mix two colors together in a rectangular color space.": "在矩形颜色空间中将两种颜色混合在一起。", "Mixes two colors together.": "将两种颜色混合在一起。", "Nests selector beneath one another like they would be nested in the stylesheet.": "将一个选择器嵌套在另一个选择器之下，就像它们嵌套在样式表中一样。", "No unit for zero needed": "零不需要单位", "Parses a selector into the format returned by &.": "将选择器分析为由 & 返回的格式。", "Prints the value of an expression to the standard error output stream. Useful for debugging complicated Sass files.": "将表达式的值输出到标准错误输出流。可用于调试复杂的 Sass 文件。", "Prints the value of an expression to the standard error output stream. Useful for libraries that need to warn users of deprecations or recovering from minor mixin usage mistakes. Warnings can be turned off with the `--quiet` command-line option or the `:quiet` Sass option.": "将表达式的值输出到标准错误输出流。适用于需要警告用户弃用或从次要 mixin 使用错误中恢复的库。可以使用 `--quiet` 命令行选项或 `:quiet` Sass 选项关闭警告。", "Property is ignored due to the display.": "由于显示，属性被忽略。", "Property is ignored due to the display. With 'display: block', vertical-align should not be used.": "由于显示，属性被忽略。对于 \"display: block\"，不应使用垂直对齐。", "Provides access to Sass’s powerful selector engine.": "提供对 Sass 功能强大的选择器引擎的访问权限。", "Provides functions that operate on numbers.": "提供对数字进行运算的函数。", "Removes quotes from a string.": "从字符串中删除引号。", "Rename to '{0}'": "重命名为“{0}”", "Replaces $original with $replacement within $selector.": "在 $selector 内将 $original 替换为 $replacement。", "Replaces the nth item in a list.": "替换列表中的第 n 项。", "Returns a list of all keys in a map.": "返回映射中所有键的列表。", "Returns a list of all values in a map.": "返回映射中所有值的列表。", "Returns a new map with keys removed.": "返回已删除键的新映射。", "Returns a random number.": "返回随机数。", "Returns a specific item in a list.": "返回列表中的特定项。", "Returns the absolute value of a number.": "返回数值的绝对值。", "Returns the complement of a color.": "返回颜色的补色。", "Returns the index of the first occurance of $substring in $string.": "返回 $string 中第一次出现 $substring 的索引。", "Returns the inverse of a color.": "返回颜色的反色。", "Returns the keywords passed to a function that takes variable arguments.": "返回传递给采用变量参数的函数的关键字。", "Returns the length of a list.": "返回列表的长度。", "Returns the number of characters in a string.": "返回字符串中的字符数。", "Returns the position of a value within a list.": "返回某个值在列表中的位置。", "Returns the separator of a list.": "返回列表的分隔符。", "Returns the simple selectors that comprise a compound selector.": "返回构成复合选择器的简单选择器。", "Returns the string representation of a value as it would be represented in Sass.": "返回值的字符串表示形式，因为它将在 Sass 中表示。", "Returns the type of a value.": "返回值的类型。", "Returns the unit(s) associated with a number.": "返回与数字关联的单位。", "Returns the value in a map associated with a given key.": "返回与给定键关联的映射中的值。", "Returns whether $super matches all the elements $sub does, and possibly more.": "返回 $super 是否与 $sub 匹配的所有元素匹配，以及它的匹配数目是否更多。", "Returns whether a feature exists in the current Sass runtime.": "返回当前 Sass 运行时中是否存在某个功能。", "Returns whether a function with the given name exists.": "返回具有给定名称的函数是否存在。", "Returns whether a map has a value associated with a given key.": "返回映射是否具有与给定键关联的值。", "Returns whether a mixin with the given name exists.": "返回是否存在具有给定名称的 mixin。", "Returns whether a number has units.": "返回数字是否具有单位。", "Returns whether a variable with the given name exists in the current scope.": "返回当前作用域中是否存在具有给定名称的变量。", "Returns whether a variable with the given name exists in the global scope.": "返回全局范围内是否存在具有给定名称的变量。", "Returns whether two numbers can be added, subtracted, or compared.": "返回两个数字是否可以相加、相减或比较。", "Rounds a number down to the previous whole number.": "将此数值向下舍入到上一个整数。", "Rounds a number to the nearest whole number.": "将数字舍入到最接近的整数。", "Rounds a number up to the next whole number.": "将数字向上舍入到下一个整数。", "Sass documentation": "Sass 文档", "Selector Specificity": "选择器特定性", "Selectors should not contain IDs because these rules are too tightly coupled with the HTML.": "选择器不应包含 ID，因为这些规则与 HTML 的耦合过于紧密。", "The universal selector (*) is known to be slow": "通配选择符 (*) 的运行效率低", "Throws the value of an expression as a fatal error with stack trace. Useful for validating arguments to mixins and functions.": "将表达式的值作为带堆栈跟踪的灾难性错误引发。对于验证 mixin 和函数的参数非常有用。", "URI expected": "应为 URI", "URL encodes a string": "对字符串进行 URL 编码", "Unifies two selectors to produce a selector that matches elements matched by both.": "统一两个选择器，生成与两者匹配的元素匹配的选择器。", "Unknown at-rule.": "未知的 @ 规则。", "Unknown property.": "未知的属性。", "Unknown property: '{0}'": "未知属性:“{0}”", "Unknown vendor specific property.": "未知的供应商特定属性。", "When using a vendor-specific prefix also include the standard property": "使用厂商特定的前缀时，同时添加标准属性", "When using a vendor-specific prefix make sure to also include all other vendor-specific properties": "使用厂商特定的前缀时，同时添加所有其他厂商特定的属性", "While loop that takes an expression and repeatedly outputs the nested styles until the statement evaluates to `false`.": "While 循环，采用表达式并重复输出嵌套样式，直到语句的计算结果为 `false`。", "[ expected": "应为 [", "] expected": "应有 ]", "absolute value of a number": "一个数的绝对值", "arccosine - inverse of cosine function": "反余弦函数–余弦函数的反函数", "arcsine - inverse of sine function": "反正弦函数 - 正弦函数的反函数", "arctangent - inverse of tangent function": "反正切函数 - 正切函数的反函数", "argument from '{0}'": "来自“{0}”的参数", "at-rule or selector expected": "应为 @ 规则或选择器", "at-rule unknown": "@ 规则未知", "bind the evaluation of a ruleset to each member of a list.": "将规则集的计算结果绑定到列表的每个成员。", "calculates square root of a number": "计算数字的平方根", "colon expected": "预期为冒号", "comma expected": "需要逗号", "condition expected": "应有条件", "converts numbers from one type into another": "将数字从一种类型转换为另一种类型", "converts to a %, e.g. 0.5 > 50%": "转换为 %，例如 0.5 > 50%", "cosine function": "余弦函数", "creates a #AARRGGBB": "创建一个 #AARRGGBB", "creates a color": "创建颜色", "css.builtin.lab": "css.builtin.lab", "dot expected": "应为点", "escape string content": "转义字符串内容", "expression expected": "应为表达式。", "first argument modulus second argument": "第一个参数对第二个参数取模", "first argument raised to the power of the second argument": "第一个参数增加到第二个参数的幂", "generate a list spanning a range of values": "生成跨一系列值的列表", "identifier expected": "预期有标识符", "identifier or variable expected": "应为标识符或变量", "identifier or wildcard expected": "预期有标识符或通配符", "inline-block is ignored due to the float. If 'float' has a value other than 'none', the box is floated and 'display' is treated as 'block'": "由于浮点值，将忽略 inline-block。如果 \"float\" 的值不是 \"none\"，则会浮动该框，并将 \"display\" 视为 \"block\"", "inlines a resource and falls back to `url()`": "内联资源并回退到 `url()`", "media query expected": "应为媒体查询", "number expected": "应为数字", "operator expected": "应有运算符", "page directive or declaraton expected": "应为页面指令或声明", "parses a string to a color": "将字符串分析为颜色", "percentage expected": "预期为百分比", "property value expected": "应为属性值", "remove or change the unit of a dimension": "删除或更改维度的单位", "return `@color` 10% points darker": "返回 `@color` 深色 10%", "return `@color` 10% points less saturated": "返回 `@color` 饱和度低 10%", "return `@color` 10% points less transparent": "返回透明度降低 10% 的 `@color`", "return `@color` 10% points lighter": "返回颜色浅 10% 的 `@color`", "return `@color` 10% points more saturated": "返回饱和度低 10% 的 `@color`", "return `@color` 10% points more transparent": "返回透明度高 10% 的 `@color`", "return `@color` with 50% transparency": "返回透明度为 50% 的 `@color`", "return `@color` with a 10 degree larger in hue": "返回色调提高 10度 的 `@color`", "return `@darkcolor` if `@color1 is> 43% luma` otherwise return `@lightcolor`, see notes": "return `@darkcolor` if `@color1 is> 43% luma` otherwise return `@lightcolor`, see notes", "return a mix of `@color1` and `@color2`": "返回 `@color1` 和 `@color2` 的组合", "returns a grey, 100% desaturated color": "返回灰色，100% 反饱和颜色", "returns a value at the specified position in the list": "返回列表中指定位置处的值", "returns one of two values depending on a condition.": "根据条件返回两个值之一。", "returns pi": "返回 pi", "returns the `alpha` channel of `@color`": "返回 `@color` 的 `alpha` 通道", "returns the `blue` channel of `@color`": "返回 `@color`的 `blue` 通道", "returns the `green` channel of `@color`": "返回 `@color` 的 `green` 通道", "returns the `hue` channel of `@color` in the HSL space": "返回 HSL 空间中 `@color` 的 `hue` 通道", "returns the `hue` channel of `@color` in the HSV space": "返回 HSV 空间中 `@color` 的 `hue` 通道", "returns the `lightness` channel of `@color` in the HSL space": "返回 HSL 空间中 `@color` 的 `lightness` 通道", "returns the `luma` value (perceptual brightness) of `@color`": "返回 `@color` 的 `luma` 值(感知亮度)", "returns the `red` channel of `@color`": "返回 `@color` 的 `red` 通道", "returns the `saturation` channel of `@color` in the HSL space": "返回 HSL 空间中 `@color` 的 `saturation` 通道", "returns the `saturation` channel of `@color` in the HSV space": "返回 HSV 空间中 `@color` 的 `saturation` 通道", "returns the `value` channel of `@color` in the HSV space": "返回 HSV 空间中 `@color` 的 `value` 通道", "returns the lowest of one or more values": "返回一个或多个值中的最低值", "returns the number of elements in a value list": "返回值列表中的元素数", "rounds a number to a number of places": "将数字舍入到多个位置", "rounds down to an integer": "向下舍入到整数", "rounds up to an integer": "向上舍入到整数", "selector expected": "应为选择器", "semi-colon expected": "需要冒号", "sine function": "正弦函数", "string literal expected": "预期有字符串字面量", "string replace": "字符串替换", "tangent function": "正切函数", "term expected": "应为术语", "unknown keyword": "未知关键字", "uri or string expected": "应为 URI 或字符串", "variable name expected": "应有变量名", "variable value expected": "预期有变量值", "whitespace expected": "应为空格", "wildcard expected": "应为通配符", "{ expected": "应为 {", "{0}, '{1}'": "{0}，“{1}”", "} expected": "应输入 }"}, "package": {"css.colorDecorators.enable.deprecationMessage": "已弃用设置 \"css.colorDecorators.enable\"，请改用 \"editor.colorDecorators\"。", "css.completion.completePropertyWithSemicolon.desc": "补全 CSS 属性时在行尾插入分号。", "css.completion.triggerPropertyValueCompletion.desc": "默认情况下，VS Code 在选择 CSS 属性后触发属性值完成。使用此设置可禁用此行为。", "css.customData.desc": "一个相对文件路径列表，这些路径指向采用[自定义数据格式](https://github.com/microsoft/vscode-css-languageservice/blob/master/docs/customData.md)的 JSON 文件。\r\n\r\nVS Code 在启动时会加载自定义数据，从而增强它对你在 JSON 文件中指定的自定义 CSS 属性（变量）、at 规则、伪类和伪元素的 CSS 支持。\r\n\r\n这些文件路径与工作区相对，且只考虑工作区文件夹设置。", "css.format.braceStyle.desc": "将大括号放在规则的同一行(`折叠`)或将大括号放在自己所在行上(`展开`)。", "css.format.enable.desc": "启用/禁用默认的 CSS 格式化程序。", "css.format.maxPreserveNewLines.desc": "启用 `#css.format.preserveNewLines#` 后要在一个区块中保留的最大换行符数。", "css.format.newlineBetweenRules.desc": "用空白行分隔规则集。", "css.format.newlineBetweenSelectors.desc": "用新行分隔选择器。", "css.format.preserveNewLines.desc": "是否应保留规则和声明之前的现有换行符。", "css.format.spaceAroundSelectorSeparator.desc": "确保选择器分隔符 '>'、'+'、'~' (例如 `a > b`)周围有空格字符。", "css.hover.documentation": "在 CSS 悬停中显示属性和值文档。", "css.hover.references": "在 CSS 悬停时显示 MDN 的引用。", "css.lint.argumentsInColorFunction.desc": "参数数目无效。", "css.lint.boxModel.desc": "在使用 `padding` 或 `border` 时，不要使用 `width` 或 `height`。", "css.lint.compatibleVendorPrefixes.desc": "使用厂商特定的前缀时，同时添加所有其他厂商特定的属性。", "css.lint.duplicateProperties.desc": "不要使用重复的样式定义。", "css.lint.emptyRules.desc": "不要使用空规则集。", "css.lint.float.desc": "避免使用 `float`。浮动会使 CSS 变得脆弱。即使只更改了一部分布局，也很容易造成破坏。", "css.lint.fontFaceProperties.desc": "`@font-face` 规则必须定义 `src` 和 `font-family` 属性。", "css.lint.hexColorLength.desc": "十六进制颜色必须包含 3、4、6 或 8 个十六进制数字。", "css.lint.idSelector.desc": "选择器不应包含 ID，因为这些规则与 HTML 的耦合过于紧密。", "css.lint.ieHack.desc": "仅在需要支持 IE7 及更低版本时，才需要 IE hack。", "css.lint.importStatement.desc": "import 语句没有并行加载。", "css.lint.important.desc": "避免使用 `!important`。它表明整个 CSS 的优先级已经失去控制且需要进行重构。", "css.lint.propertyIgnoredDueToDisplay.desc": "由于 `display` 属性值，属性被忽略。例如，使用 `display: inline` 时，`width`、`height`、`margin-top`、`margin-bottom` 和 `float` 属性将不起作用。", "css.lint.universalSelector.desc": "通配选择符 (`*`) 的运行效率低。", "css.lint.unknownAtRules.desc": "未知的 @ 规则。", "css.lint.unknownProperties.desc": "未知的属性。", "css.lint.unknownVendorSpecificProperties.desc": "未知的供应商特定属性。", "css.lint.validProperties.desc": "不根据 \"unknownProperties\" 规则进行验证的属性列表。", "css.lint.vendorPrefix.desc": "使用厂商特定的前缀时，同时添加标准属性。", "css.lint.zeroUnits.desc": "零不需要单位。", "css.title": "CSS", "css.trace.server.desc": "跟踪 VS Code 与 CSS 语言服务器之间的通信。", "css.validate.desc": "启用或禁用所有验证。", "css.validate.title": "控制 CSS 验证和问题严重性。", "description": "为 CSS、LESS 和 SCSS 文件提供丰富的语言支持。", "displayName": "CSS 语言功能", "less.colorDecorators.enable.deprecationMessage": "已弃用设置 \"less.colorDecorators.enable\"，请改用 \"editor.colorDecorators\"。", "less.completion.completePropertyWithSemicolon.desc": "补全 CSS 属性时在行尾插入分号。", "less.completion.triggerPropertyValueCompletion.desc": "默认情况下，VS Code 在选择 CSS 属性后触发属性值完成。使用此设置可禁用此行为。", "less.format.braceStyle.desc": "将大括号放在规则的同一行(`折叠`)或将大括号放在自己所在行上(`展开`)。", "less.format.enable.desc": "启用/禁用默认的 LESS 格式化程序。", "less.format.maxPreserveNewLines.desc": "启用 `#less.format.preserveNewLines#` 后要在一个区块中保留的最大换行符数。", "less.format.newlineBetweenRules.desc": "用空白行分隔规则集。", "less.format.newlineBetweenSelectors.desc": "用新行分隔选择器。", "less.format.preserveNewLines.desc": "是否应保留规则和声明之前的现有换行符。", "less.format.spaceAroundSelectorSeparator.desc": "确保选择器分隔符 '>'、'+'、'~' (例如 `a > b`)周围有空格字符。", "less.hover.documentation": "在 LESS 悬停中显示属性和值文档。", "less.hover.references": "在 LESS 悬停时显示 MDN 的引用。", "less.lint.argumentsInColorFunction.desc": "参数数目无效。", "less.lint.boxModel.desc": "在使用 `padding` 或 `border` 时，不要使用 `width` 或 `height`。", "less.lint.compatibleVendorPrefixes.desc": "使用厂商特定的前缀时，同时添加所有其他厂商特定的属性。", "less.lint.duplicateProperties.desc": "不要使用重复的样式定义。", "less.lint.emptyRules.desc": "不要使用空规则集。", "less.lint.float.desc": "避免使用 `float`。浮动会使 CSS 变得脆弱。即使只更改了一部分布局，也很容易造成破坏。", "less.lint.fontFaceProperties.desc": "`@font-face` 规则必须定义 `src` 和 `font-family` 属性。", "less.lint.hexColorLength.desc": "十六进制颜色必须包含 3、4、6 或 8 个十六进制数字。", "less.lint.idSelector.desc": "选择器不应包含 ID，因为这些规则与 HTML 的耦合过于紧密。", "less.lint.ieHack.desc": "仅在需要支持 IE7 及更低版本时，才需要 IE hack。", "less.lint.importStatement.desc": "import 语句没有并行加载。", "less.lint.important.desc": "避免使用 `!important`。它表明整个 CSS 的优先级已经失去控制且需要进行重构。", "less.lint.propertyIgnoredDueToDisplay.desc": "由于 `display` 属性值，属性被忽略。例如，使用 `display: inline` 时，`width`、`height`、`margin-top`、`margin-bottom` 和 `float` 属性将不起作用。", "less.lint.universalSelector.desc": "通配选择符 (`*`) 的运行效率低。", "less.lint.unknownAtRules.desc": "未知的 @ 规则。", "less.lint.unknownProperties.desc": "未知的属性。", "less.lint.unknownVendorSpecificProperties.desc": "未知的供应商特定属性。", "less.lint.validProperties.desc": "不根据 \"unknownProperties\" 规则进行验证的属性列表。", "less.lint.vendorPrefix.desc": "使用厂商特定的前缀时，同时添加标准属性。", "less.lint.zeroUnits.desc": "零不需要单位。", "less.title": "LESS", "less.validate.desc": "启用或禁用所有验证。", "less.validate.title": "控制 LESS 验证和问题严重性。", "scss.colorDecorators.enable.deprecationMessage": "已弃用设置 \"scss.colorDecorators.enable\"，请改用 \"editor.colorDecorators\"。", "scss.completion.completePropertyWithSemicolon.desc": "补全 CSS 属性时在行尾插入分号。", "scss.completion.triggerPropertyValueCompletion.desc": "默认情况下，VS Code 在选择 CSS 属性后触发属性值完成。使用此设置可禁用此行为。", "scss.format.braceStyle.desc": "将大括号放在规则的同一行(`折叠`)或将大括号放在自己所在行上(`展开`)。", "scss.format.enable.desc": "启用/禁用默认的 SCSS 格式化程序。", "scss.format.maxPreserveNewLines.desc": "启用 `#scss.format.preserveNewLines#` 后要在一个区块中保留的最大换行符数。", "scss.format.newlineBetweenRules.desc": "用空白行分隔规则集。", "scss.format.newlineBetweenSelectors.desc": "用新行分隔选择器。", "scss.format.preserveNewLines.desc": "是否应保留规则和声明之前的现有换行符。", "scss.format.spaceAroundSelectorSeparator.desc": "确保选择器分隔符 '>'、'+'、'~' (例如 `a > b`)周围有空格字符。", "scss.hover.documentation": "在 SCSS 悬停中显示属性和值文档。", "scss.hover.references": "在 SCSS 悬停时显示 MDN 的引用。", "scss.lint.argumentsInColorFunction.desc": "参数数目无效。", "scss.lint.boxModel.desc": "在使用 `padding` 或 `border` 时，不要使用 `width` 或 `height`。", "scss.lint.compatibleVendorPrefixes.desc": "使用厂商特定的前缀时，同时添加所有其他厂商特定的属性。", "scss.lint.duplicateProperties.desc": "不要使用重复的样式定义。", "scss.lint.emptyRules.desc": "不要使用空规则集。", "scss.lint.float.desc": "避免使用 `float`。浮动会使 CSS 变得脆弱。即使只更改了一部分布局，也很容易造成破坏。", "scss.lint.fontFaceProperties.desc": "`@font-face` 规则必须定义 `src` 和 `font-family` 属性。", "scss.lint.hexColorLength.desc": "十六进制颜色必须包含 3、4、6 或 8 个十六进制数字。", "scss.lint.idSelector.desc": "选择器不应包含 ID，因为这些规则与 HTML 的耦合过于紧密。", "scss.lint.ieHack.desc": "仅在需要支持 IE7 及更低版本时，才需要 IE hack。", "scss.lint.importStatement.desc": "import 语句没有并行加载。", "scss.lint.important.desc": "避免使用 `!important`。它表明整个 CSS 的优先级已经失去控制且需要进行重构。", "scss.lint.propertyIgnoredDueToDisplay.desc": "由于 `display` 属性值，属性被忽略。例如，使用 `display: inline` 时，`width`、`height`、`margin-top`、`margin-bottom` 和 `float` 属性将不起作用。", "scss.lint.universalSelector.desc": "通配选择符 (`*`) 的运行效率低。", "scss.lint.unknownAtRules.desc": "未知的 @ 规则。", "scss.lint.unknownProperties.desc": "未知的属性。", "scss.lint.unknownVendorSpecificProperties.desc": "未知的供应商特定属性。", "scss.lint.validProperties.desc": "不根据 \"unknownProperties\" 规则进行验证的属性列表。", "scss.lint.vendorPrefix.desc": "使用厂商特定的前缀时，同时添加标准属性。", "scss.lint.zeroUnits.desc": "零不需要单位。", "scss.title": "SCSS (Sass)", "scss.validate.desc": "启用或禁用所有验证。", "scss.validate.title": "控制 SCSS 验证和问题严重性。"}}}