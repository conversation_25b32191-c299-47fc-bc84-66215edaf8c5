{"": ["--------------------------------------------------------------------------------------------", "Copyright (c) Microsoft Corporation. All rights reserved.", "Licensed under the MIT License. See License.txt in the project root for license information.", "--------------------------------------------------------------------------------------------", "Do not edit this file. It is machine generated."], "version": "1.0.0", "contents": {"bundle": {"Continue": "继续", "Don't show again": "不再显示", "Port Forwarding": "端口转发", "Private": "专用", "Public": "公共", "You're about to create a publicly forwarded port. Anyone on the internet will be able to connect to the service listening on port {0}. You should only proceed if this service is secure and non-sensitive.": "你即将创建公开转发的端口。Internet 上的任何人都可以连接到侦听端口 {0} 的服务。仅当此服务安全且不敏感时，才应继续操作。"}, "package": {"category": "端口转发", "command.restart": "重新启动转发系统", "command.showLog": "显示日志", "description": "允许通过 Internet 访问转发本地端口。", "displayName": "本地隧道端口转发"}}}