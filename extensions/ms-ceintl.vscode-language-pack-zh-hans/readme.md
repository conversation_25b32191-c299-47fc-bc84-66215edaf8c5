#  适用于 VS Code 的中文（简体）语言包

此中文（简体）语言包为 VS Code 提供本地化界面。

## 使用方法

通过使用“Configure Display Language”命令显式设置 VS Code 显示语言，可以替代默认 UI 语言。
按下“Ctrl+Shift+P”组合键以显示“命令面板”，然后键入“display”以筛选并显示“Configure Display Language”命令。按“Enter”，然后会按区域设置显示安装的语言列表，并突出显示当前语言设置。选择另一个“语言”以切换 UI 语言。
请参阅[文档](https://go.microsoft.com/fwlink/?LinkId=761051)并获取更多信息。

## 参与

有关翻译改进的反馈，请在 [vscode-loc](https://github.com/microsoft/vscode-loc) 存储库中创建问题。
翻译字符串在 Microsoft 本地化平台中维护。只能在 Microsoft 本地化平台中进行更改，然后才能导出到 vscode-loc 存储库。因此，vscode-loc 存储库中不接受拉取请求。

## 许可证

源代码和字符串使用[MIT](https://github.com/Microsoft/vscode-loc/blob/master/LICENSE.md)许可进行授权。

## 感谢

此中文（简体）语言包是“来自社区，奉献社区”的社区本地化工作的成果。

特别感谢社区中每一位向这个项目做出贡献的朋友。

**杰出贡献者：**

* Joel Yang：在此项目向社区开放之后，翻译了大部分新增字符串。先后翻译了4万余字。
* Daniel Ye
* Neng Xue

**贡献者：**

* YF
* 陈嘉恺
* pluwen
* Shawn Dai
* Ying Feng
* Simon Chan
* 王子实
* 王韦煊
* 王旭晨
* Zijian Zhou
* wwj403
* Shizeng Zhou
* Aifen Qin
* lychichem
* Wang Dongcheng
* Yurui Zhang
* DongWei
* Bingxing Wang
* 林昊
* KingofCoding
* 潘冬冬
* 陈仁松
* Henry Chu
* Zhijian Zeng
* aimin guo
* 刘丁明
* hackereric
* Zou Jian
* Jianfeng Fang
* Ricky Wang
* Egg Zhang

**尽情享用吧！**

#  Chinese (Simplified) Language Pack for VS Code

Chinese (Simplified) Language Pack provides localized UI experience for VS Code.

## Usage

You can override the default UI language by explicitly setting the VS Code display language using the **Configure Display Language** command.

Press `Ctrl+Shift+P` to bring up the **Command Palette** then start typing `display` to filter and display the **Configure Display Language** command.

Press `Enter` and a list of installed languages by locale is displayed, with the current locale highlighted. Select another `locale` to switch UI language.

See [Docs](https://go.microsoft.com/fwlink/?LinkId=761051) for more information.

## Contributing

For feedback of translation improvement, please create Issue in [vscode-loc](https://github.com/microsoft/vscode-loc) repo.

The translation strings are maintained in Microsoft Localization Platform. Change can only be made in Microsoft Localization Platform then export to vscode-loc repo. So pull request won't be accepted in vscode-loc repo.

## License

The source code and strings are licensed under the [MIT](https://github.com/Microsoft/vscode-loc/blob/master/LICENSE.md) license.

## Credits

Chinese (Simplified) Language Pack had received contribution through "By the community, for the community" community localization effort.

Special thanks to community contributors for making it available.

**Top Contributors:**

* Joel Yang: localized majority of the new translation volume since open the project to community. Total 40k words localized.

**Contributors:**

* YF
* 陈嘉恺
* pluwen
* Shawn Dai
* Ying Feng
* Simon Chan
* 王子实
* 王韦煊
* Zijian Zhou
* wwj403
* Shizeng Zhou
* Aifen Qin
* lychichem
* Wang Dongcheng
* Yurui Zhang
* DongWei
* Bingxing Wang
* 林昊
* KingofCoding
* 潘冬冬
* 陈仁松
* Henry Chu
* Zhijian Zeng
* aimin guo
* 刘丁明
* hackereric
* Zou Jian
* Jianfeng Fang
* Ricky Wang
* Egg Zhang

**Enjoy!**
