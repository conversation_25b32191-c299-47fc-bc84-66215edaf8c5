{"description": "Extension to add task support for npm scripts.", "displayName": "NPM support for JoyCode", "workspaceTrust": "This extension executes tasks, which require trust to run.", "virtualWorkspaces": "Functionality that requires running the 'npm' command is not available in virtual workspaces.", "config.npm.autoDetect": "Controls whether npm scripts should be automatically detected.", "config.npm.runSilent": "Run npm commands with the `--silent` option.", "config.npm.packageManager": "The package manager used to install dependencies.", "config.npm.packageManager.npm": "Use npm as the package manager.", "config.npm.packageManager.yarn": "Use yarn as the package manager.", "config.npm.packageManager.pnpm": "Use pnpm as the package manager.", "config.npm.packageManager.bun": "Use bun as the package manager.", "config.npm.packageManager.auto": "Auto-detect which package manager to use based on lock files and installed package managers.", "config.npm.scriptRunner": "The script runner used to run scripts.", "config.npm.scriptRunner.npm": "Use npm as the script runner.", "config.npm.scriptRunner.yarn": "Use yarn as the script runner.", "config.npm.scriptRunner.pnpm": "Use pnpm as the script runner.", "config.npm.scriptRunner.bun": "Use bun as the script runner.", "config.npm.scriptRunner.node": "Use Node.js as the script runner.", "config.npm.scriptRunner.auto": "Auto-detect which script runner to use based on lock files and installed package managers.", "config.npm.exclude": "Configure glob patterns for folders that should be excluded from automatic script detection.", "config.npm.enableScriptExplorer": "Enable an explorer view for npm scripts when there is no top-level 'package.json' file.", "config.npm.scriptExplorerAction": "The default click action used in the NPM Scripts Explorer: `open` or `run`, the default is `open`.", "config.npm.scriptExplorerExclude": "An array of regular expressions that indicate which scripts should be excluded from the NPM Scripts view.", "config.npm.enableRunFromFolder": "Enable running npm scripts contained in a folder from the Explorer context menu.", "config.npm.fetchOnlinePackageInfo": "Fetch data from https://registry.npmjs.org and https://registry.bower.io to provide auto-completion and information on hover features on npm dependencies.", "config.npm.scriptHover": "Display hover with 'Run' and 'Debug' commands for scripts.", "npm.parseError": "Npm task detection: failed to parse the file {0}", "taskdef.script": "The npm script to customize.", "taskdef.path": "The path to the folder of the package.json file that provides the script. Can be omitted.", "view.name": "NPM Scripts", "command.refresh": "Refresh", "command.run": "Run", "command.debug": "Debug", "command.openScript": "Open", "command.runInstall": "Run Install", "command.runSelectedScript": "<PERSON>", "command.runScriptFromFolder": "Run NPM Script in Folder...", "command.packageManager": "Get Configured Package Manager"}