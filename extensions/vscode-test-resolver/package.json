{"name": "vscode-test-resolver", "description": "Test resolver for JoyCode", "version": "0.0.1", "publisher": "vscode", "license": "MIT", "enabledApiProposals": ["resolvers", "tunnels"], "private": true, "engines": {"vscode": "^1.25.0"}, "icon": "media/icon.png", "extensionKind": ["ui"], "scripts": {"compile": "node ./node_modules/vscode/bin/compile -watch -p ./", "vscode:prepublish": "node ../../node_modules/gulp/bin/gulp.js --gulpfile ../../build/gulpfile.extensions.js compile-extension:vscode-test-resolver"}, "activationEvents": ["onResolveRemoteAuthority:test", "onCommand:vscode-testresolver.newWindow", "onCommand:vscode-testresolver.currentWindow", "onCommand:vscode-testresolver.newWindowWithError", "onCommand:vscode-testresolver.showLog", "onCommand:vscode-testresolver.openTunnel", "onCommand:vscode-testresolver.startRemoteServer", "onCommand:vscode-testresolver.toggleConnectionPause"], "main": "./out/extension", "browser": "./dist/browser/testResolverMain", "devDependencies": {"@types/node": "20.x"}, "capabilities": {"untrustedWorkspaces": {"supported": true}, "virtualWorkspaces": true}, "contributes": {"resourceLabelFormatters": [{"scheme": "vscode-remote", "authority": "test+*", "formatting": {"label": "${path}", "separator": "/", "tildify": true, "workspaceSuffix": "TestResolver", "workspaceTooltip": "Remote running on the same machine"}}], "commands": [{"title": "New TestResolver Window", "category": "Remote-TestResolver", "command": "vscode-testresolver.newWindow"}, {"title": "Connect to TestResolver in Current Window", "category": "Remote-TestResolver", "command": "vscode-testresolver.currentWindow"}, {"title": "Connect to TestResolver in Current Window with Managed Connection", "category": "Remote-TestResolver", "command": "vscode-testresolver.currentWindowManaged"}, {"title": "Show TestResolver Log", "category": "Remote-TestResolver", "command": "vscode-testresolver.showLog"}, {"title": "Kill Remote Server and Trigger Handled Error", "category": "Remote-TestResolver", "command": "vscode-testresolver.killServerAndTriggerHandledError"}, {"title": "Open Tunnel...", "category": "Remote-TestResolver", "command": "vscode-testresolver.openTunnel"}, {"title": "Open a Remote Port...", "category": "Remote-TestResolver", "command": "vscode-testresolver.startRemoteServer"}, {"title": "Pause Connection (Test Reconnect)", "category": "Remote-TestResolver", "command": "vscode-testresolver.toggleConnectionPause"}, {"title": "Slowdown Connection (Test Slow Down Indicator)", "category": "Remote-TestResolver", "command": "vscode-testresolver.toggleConnectionSlowdown"}], "menus": {"commandPalette": [{"command": "vscode-testresolver.openTunnel", "when": "remoteName == test"}, {"command": "vscode-testresolver.startRemoteServer", "when": "remoteName == test"}, {"command": "vscode-testresolver.toggleConnectionPause", "when": "remoteName == test"}], "statusBar/remoteIndicator": [{"command": "vscode-testresolver.newWindow", "when": "!remoteName && !virtualWorkspace", "group": "remote_90_test_1_local@2"}, {"command": "vscode-testresolver.showLog", "when": "remoteName == test", "group": "remote_90_test_1_open@3"}, {"command": "vscode-testresolver.newWindow", "when": "remoteName == test", "group": "remote_90_test_1_open@1"}, {"command": "vscode-testresolver.openTunnel", "when": "remoteName == test", "group": "remote_90_test_2_more@4"}, {"command": "vscode-testresolver.startRemoteServer", "when": "remoteName == test", "group": "remote_90_test_2_more@5"}, {"command": "vscode-testresolver.toggleConnectionPause", "when": "remoteName == test", "group": "remote_90_test_2_more@6"}]}, "configuration": {"properties": {"testresolver.startupDelay": {"description": "If set, the resolver will delay for the given amount of seconds. Use ths setting for testing a slow resolver", "type": "number", "default": 0}, "testresolver.startupError": {"description": "If set, the resolver will fail. Use ths setting for testing the failure of a resolver.", "type": "boolean", "default": false}, "testresolver.supportPublicPorts": {"description": "If set, the test resolver tunnel factory will support mock public ports. Forwarded ports will not actually be public. Requires reload.", "type": "boolean", "default": false}}}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}