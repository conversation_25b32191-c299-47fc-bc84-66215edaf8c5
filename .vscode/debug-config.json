{"version": "0.2.0", "configurations": [{"type": "node", "request": "attach", "name": "Attach to JoyCode Main Process", "port": 5875, "outFiles": ["${workspaceFolder}/out/**/*.js"]}, {"type": "chrome", "request": "attach", "name": "Attach to JoyCode Renderer Process", "port": 9122, "webRoot": "${workspaceFolder}", "outFiles": ["${workspaceFolder}/out/**/*.js"]}], "compounds": [{"name": "Debug JoyCode (All Processes)", "configurations": ["Attach to JoyCode Main Process", "Attach to JoyCode Renderer Process"]}]}