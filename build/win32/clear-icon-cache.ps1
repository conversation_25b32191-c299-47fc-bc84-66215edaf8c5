# 清除Windows图标缓存的PowerShell脚本

# 停止Windows资源管理器进程
Stop-Process -Name explorer -Force -ErrorAction SilentlyContinue

# 删除图标缓存文件
$iconCacheFiles = @(
    "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\iconcache*.db",
    "$env:LOCALAPPDATA\Microsoft\Windows\Explorer\thumbcache*.db",
    "$env:LOCALAPPDATA\IconCache.db"
)

foreach ($file in $iconCacheFiles) {
    Remove-Item -Path $file -Force -ErrorAction SilentlyContinue
}

# 清除系统图标缓存
[System.Runtime.InteropServices.Marshal]::ReleaseComObject([System.Drawing.Icon]::ExtractAssociatedIcon((Get-Command explorer).Path)) | Out-Null

# 重启Windows资源管理器
Start-Process explorer
