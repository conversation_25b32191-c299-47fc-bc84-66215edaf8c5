/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { createRequire } from 'node:module';
import type { IProductConfiguration } from './vs/base/common/product.js';
import * as fs from 'fs';
import * as path from 'path';

const require = createRequire(import.meta.url);

// 获取ElectronBuildId
function getElectronBuildId(): string {
	try {
		const npmrcPath = path.join(path.dirname(path.dirname(import.meta.url.substring(7))), '.npmrc');
		// 检查文件是否存在
		if (!fs.existsSync(npmrcPath)) {
			return '11161602'; // 默认值，文件不存在时静默返回
		}
		const npmrc = fs.readFileSync(npmrcPath, 'utf8');
		const match = /^ms_build_id="(.*)"$/m.exec(npmrc);
		return match ? match[1] : '11161602'; // 默认值
	} catch (err) {
		// 不记录错误，静默返回默认值
		return '11161602'; // 默认值
	}
}

let productObj: Partial<IProductConfiguration> & { BUILD_INSERT_PRODUCT_CONFIGURATION?: string } = { BUILD_INSERT_PRODUCT_CONFIGURATION: 'BUILD_INSERT_PRODUCT_CONFIGURATION' }; // DO NOT MODIFY, PATCHED DURING BUILD
if (productObj['BUILD_INSERT_PRODUCT_CONFIGURATION']) {
	const temp = require('../product.json'); // Running out of sources
	// 固定commit值为特定值
	// temp.commit = 'bf58862d8d60ad8747b8588ad1b8c1ae956ddad6';
	// 设置ElectronBuildId
	temp.electronBuildId = getElectronBuildId();
	productObj = temp;
}

let pkgObj = { BUILD_INSERT_PACKAGE_CONFIGURATION: 'BUILD_INSERT_PACKAGE_CONFIGURATION' }; // DO NOT MODIFY, PATCHED DURING BUILD
if (pkgObj['BUILD_INSERT_PACKAGE_CONFIGURATION']) {
	pkgObj = require('../package.json'); // Running out of sources
}

export const product = productObj;
export const pkg = pkgObj;
