/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

export interface UpdateOptions {
    upDataUrl?: string;
    joyCoderVersion?: string;
}

export interface CustomAdapter {
    on(event: string, callback: (params?: any) => void): void;
    checkForUpdatesAndNotify(isClickMenu?: boolean): Promise<void>;
    quitAndInstall(isReload?: boolean): void;
    updatePath?: string;
    emit(event: string, params?: any): void;
    joyCoderVersion: string;
    latestVersion: string;
    feedUrl: string;
    upDataInfo: any;
    isClickMenu: boolean;
    latestRelease: {
        osx: string;
        linux: string;
        windows: string;
    };
}

export interface CustomAdapterOptions {
    getRemoteLatest?: () => Promise<unknown>;
    log?: boolean;
    url?: string;
    updateAvailableCallback?: () => void;
    downloadFinishedCallback?: () => void;
    [key: string]: any;
}

export interface BaseAdapter {
    log: boolean;
    latestRelease: {
        osx: string;
        linux: string;
        windows: string;
    };
    latestVersion: string;
    feedUrl: string;
    upDataInfo: any;
    joyCoderVersion: string;
    updatePath?: string;
    updateAvailableCallback: () => void;
    downloadFinishedCallback: () => void;
    getRemoteLatest: () => Promise<unknown>;
    setFeedUrl(url: string): void;
    checkForUpdatesAndNotify(): Promise<void>;
    checkUpdate(): Promise<boolean>;
    parseVersionNum(versionStr: string): number;
    getDownloadUrl(): string;
    download(): Promise<void | false>;
    quitAndInstall(isReload?: boolean): void;
    isDownload(filePath: string, expectedSha256?: string): boolean;
    on(event: string, cb: (params?: any) => void): void;
    emit(event: string, params?: any): void;
}
