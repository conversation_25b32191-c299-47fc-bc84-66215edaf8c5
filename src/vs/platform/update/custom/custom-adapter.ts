/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import BaseAdapterImpl from './base-adapter.js'
import { CustomAdapterOptions } from './types.js';

export default class extends BaseAdapterImpl {
	constructor(options: CustomAdapterOptions) {
		super(options)
		if (options && options.url) {
			this.setFeedUrl(options.url)
		} else if (!options || !options.getRemoteLatest) {
			throw new Error('Can not find getRemoteLatest function definition when use custom type without url')
		}
	}
}
