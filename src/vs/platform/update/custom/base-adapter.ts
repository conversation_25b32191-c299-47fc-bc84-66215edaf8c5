import https from 'https';
import { URL } from 'url';
import fs from 'fs';
import path from 'path';
import { app, dialog } from 'electron';
import { spawn, execSync, exec } from 'child_process';
import os from 'os';
import crypto from 'crypto';
import { BaseAdapter, CustomAdapterOptions } from './types.js';


export default class BaseAdapterImpl implements BaseAdapter {
    log = false;
    latestRelease = {
        osx: '',
        linux: '',
        windows: ''
    };
    latestVersion = '';
    feedUrl = '';
    upDataInfo: any = {};
    joyCoderVersion = '0.0.0';
    updatePath?: string;
    // 是否是用户主动触发
    isClickMenu: boolean = false;
    updateAvailableCallback: () => void = () => this.download();
    downloadFinishedCallback: () => void = () => { };
    getRemoteLatest: () => Promise<unknown> = () => this._getRemoteLatest();
    private willQuitHandlerRegistered = false;

    // 使用索引签名来解决索引访问问题
    [key: string]: any;

    constructor(options: CustomAdapterOptions) {
        const {
            log = false,
            updateAvailableCallback,
            downloadFinishedCallback,
            getRemoteLatest
        } = options;
        this.log = log;
        if (updateAvailableCallback) this.updateAvailableCallback = updateAvailableCallback;
        if (downloadFinishedCallback) this.downloadFinishedCallback = downloadFinishedCallback;
        if (getRemoteLatest) this.getRemoteLatest = getRemoteLatest;
    }

    setFeedUrl(url: string): void {
        this.feedUrl = url;
    }

    async checkForUpdatesAndNotify(isClickMenu = false): Promise<void> {
        this.isClickMenu = isClickMenu;
        this.emit('checking-for-update');
        try {
            if (await this.checkUpdate()) {
                this.emit('update-available', this.upDataInfo);
                this.updateAvailableCallback();
            } else {
                this.emit('update-not-available', this.upDataInfo);
            }
        } catch (e) {
            this.emit('error', e);
        }
    }

    async checkUpdate(): Promise<boolean> {
        try {
            const latestVersion = this.parseVersionNum(String(await this.getRemoteLatest()));
            const localVersion = this.parseVersionNum(this.joyCoderVersion);
            this.emit('log', { latestVersion, localVersion });
            return latestVersion > localVersion;
        } catch (e) {
            return Promise.reject(e);
        }
    }

    _getRemoteLatest(): Promise<unknown> {
        this.emit('log', 'getRemoteLatest');
        this.emit('log', `feedurl: ${this.feedUrl}`);
        return new Promise((resolve, reject) => {
            if (!this.feedUrl) {
                return reject('Feed URL is not set');
            }

            const url = new URL(this.feedUrl);
            https.get({
                hostname: url.hostname,
                port: 443,
                path: url.pathname,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_4) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.87 Safari/537.36'
                }
            }, (res) => {
                if (res.statusCode && res.statusCode >= 400) {
                    return reject('Cannot get latest version, please check the feed url.');
                }
                let data = '';
                res.on('data', (chunk) => {
                    data += chunk;
                });
                res.on('end', () => {
                    try {
                        const { version, linux, osx, windows } = JSON.parse(data);
                        if (version) {
                            this.latestVersion = version;
                            this.latestRelease.linux = linux;
                            this.latestRelease.osx = osx;
                            this.latestRelease.windows = windows;
                            this.emit('log', this.latestRelease);
                            resolve(version);
                        } else {
                            reject('Cannot get latest version, please check the feed url.');
                        }
                    } catch (e) {
                        reject(`Error parsing response: ${e}`);
                    }
                });
            }).on("error", (err) => {
                reject(err);
            });
        });
    }

    parseVersionNum(versionStr: string): number {
        return versionStr.split('.').map(i => parseInt(i)).reduce((a, b, _i) => a * 1000 + b, 0);
    }

    getDownloadUrl(): string {
        switch (process.platform) {
            case 'darwin':
                return this.latestRelease.osx;
            case 'win32':
                return this.latestRelease.windows;
            default:
                return this.latestRelease.linux;
        }
    }

    async download(): Promise<void | false> {
        const platformExtension: Record<string, string> = {
            darwin: 'zip',
            osx: 'zip',
            win32: 'exe',
            linux: 'AppImage',
            freebsd: 'AppImage',
            openbsd: 'AppImage',
            netbsd: 'AppImage',
            sunos: 'AppImage',
            aix: 'AppImage',
            android: 'AppImage',
            cygwin: 'AppImage',
            haiku: 'AppImage'
        };

        const extension = platformExtension[process.platform] || 'AppImage';
        this.updatePath = path.join(
            os.tmpdir(),
            `${app.getName()}_${this.latestVersion}.${extension}`
        );

        const isDown = this.isDownload(this.updatePath, this.upDataInfo?.sha256);
        if (!isDown) {
            console.log('无需重复下载');
            // this.quitAndInstall(true);
			this.emit('log', `下载写入文件流结束，路径是： ${this.updatePath}`);
			this.emit('update-downloaded', this.upDataInfo);
            return false;
        }

        const filePath = this.updatePath;
        const file = fs.createWriteStream(filePath);
        let downloadUrl;
        // 新增清理函数，避免多处重复代码
        function cleanup() {
            try { file.close(); } catch (e) { }
            if (filePath) {
                fs.unlink(filePath, () => { });
            }
        }
        try {
            downloadUrl = this.getDownloadUrl();
        } catch (e) {
            this.emit('error', e);
            console.error(e);
            // dialog.showMessageBox({
            //     type: 'info',
            //     title: '下载提醒',
            //     message: '下载失败，无法获取当前平台程序的下载地址',
            // });
            return;
        }

        try {
            const url = new URL(downloadUrl);
            const req = https.get({
                hostname: url.hostname,
                port: 443,
                path: url.pathname,
                timeout: 1000 * 10, // 设置超时时间为 10 秒
                rejectUnauthorized: false
            }, (res) => {
                const len = res.headers['content-length'];
                let downloaded = 0;
                let percent = 0;
                this.emit('download-progress', '开始下载');

                // 新增：空间检查
                (async () => {
                    if (len) {
                        const fileSize = parseInt(len, 10);
                        try {
                            const { free } = await BaseAdapterImpl.getDiskFreeSpace(filePath);
                            if (free < fileSize * 5) {
                                const requiredMB = ((fileSize * 5) / (1024 * 1024)).toFixed(2);
                                // this.emit('error', `磁盘空间不足，至少需要文件大小的2倍（${requiredMB} MB）空间`);
                                cleanup();
                                req.abort();
                                throw new Error(`磁盘空间不足，至少需要（${requiredMB} MB）空间`);
                            }
                        } catch (e) {
                            this.emit('error', '检查磁盘空间失败: ' + (e && e.message ? e.message : e));
                            cleanup();
                            req.abort();
                            throw e;
                        }
                    }
                })();

                res.pipe(file);
                res.on('data', (chunk) => {
                    downloaded += chunk.length;
                    if (len) {
                        let newPercent = parseInt(String(downloaded * 100 / parseInt(len)));
                        if (newPercent > percent) {
                            this.emit('download-progress', newPercent);
                            percent = newPercent;
                        }

                    }
                });
                // 新增：监听下载流被中断
                res.on('aborted', () => {
                    this.emit('error', '下载被中断');
                    cleanup();
                });
                // res.on('close', () => {
                //     // 如果未下载完就关闭，视为异常
                //     if (len && downloaded < parseInt(len)) {
                //         this.emit('error', '连接意外关闭，下载未完成');
                //         cleanup();
                //     }
                // });
                file.on('finish', () => {
                    file.close();
                    // 修改文件的权限以允许任何人访问
                    if (filePath) {
                        fs.chmod(filePath, 0o777, (err) => {
                            if (err) throw err;
                            console.log('文件权限已更改为 777');
                        });
                    }

                    this.emit('log', `下载写入文件流结束，路径是： ${this.updatePath}`);
                    this.emit('update-downloaded', this.upDataInfo);
                });
                // 新增：监听文件写入错误
                file.on('error', (err) => {
                    this.emit('error', '文件写入失败: ' + err.message);
                    cleanup();
                });
                res.setTimeout(10000, () => {
                    this.emit('error', '下载超时');
                    cleanup();
                    req.abort();
                });
            });
            req.on('error', (err) => {
                const error = err as any;
                if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
                    this.emit('error', '无法连接到服务器');
                } else if (error.code === 'ETIMEDOUT') {
                    this.emit('error', '连接超时');
                } else if (['ECONNRESET', 'EPIPE', 'ENETUNREACH'].includes(error.code)) {
                    this.emit('error', '网络中断，下载失败');
                } else {
                    this.emit('error', err);
                }
                cleanup();
            });
        } catch (e) {
            this.emit('error', e);
            console.error(e);
            // dialog.showMessageBox({
            //     type: 'info',
            //     title: '更新下载提醒',
            //     message: '更新下载失败，请检查网络连接或稍后再试',
            // });
        }
    }

    quitAndInstall(isReload = true): void {
        switch (process.platform) {
            case 'darwin':
                if (this.updatePath) {
                    const scriptPath = path.join(os.tmpdir(), 'update_and_relaunch.sh');
                    const appName = app.getName();
                    const appPath = `/Applications/${appName}.app`;

                    const scriptContent = `#!/bin/bash\nunzip -o -q "${this.updatePath}" -d "/Applications/"\nrm -f "${this.updatePath}"\nopen "${appPath}"\n`;
                    fs.writeFileSync(scriptPath, scriptContent, { mode: 0o755 });

                    spawn('sh', [scriptPath], {
                        detached: true,
                        stdio: 'ignore'
                    }).unref();

                    this.emit('log', `已生成更新脚本并开始后台解压，主程序即将退出`);
                    app.exit(0);
                }
                break;
            case 'win32':
                if (this.updatePath) {
                    try {
                        // 检查文件是否存在
                        if (!fs.existsSync(this.updatePath)) {
                            throw new Error(`更新文件不存在: ${this.updatePath}`);
                        }

                        // 检查文件权限
                        try {
                            fs.accessSync(this.updatePath, fs.constants.X_OK);
                        } catch (e) {
                            // 如果文件没有执行权限，尝试修改权限
                            fs.chmodSync(this.updatePath, 0o755);
                        }

                        // 准备安装参数
                        const args = [
                            "/S",           // 静默安装
                            "/NOCANCEL",    // 禁止取消
                            "/FORCECLOSEAPPLICATIONS", // 强制关闭应用程序
                            "/MERGETASKS=runcode,!desktopicon,!quicklaunchicon" // 合并任务
                        ];

                        // 如果是更新模式，添加更新参数
                        if (isReload) {
                            args.push("--updated");
                            this.emit("log", "进入立即更新模式");

                            // 启动安装程序
                            const childProcess = spawn(this.updatePath, args, {
                                detached: true,
                                stdio: 'ignore',
                                windowsVerbatimArguments: true
                            });

                            // 记录进程信息
                            this.emit('log', `启动安装程序，PID: ${childProcess.pid}`);

                            // 监听进程事件
                            childProcess.on('error', (error) => {
                                this.emit('error', `安装程序启动失败: ${error.message}`);
                                console.error('安装程序启动失败:', error);
                            });

                            childProcess.on('exit', (code) => {
                                if (code === 0) {
                                    this.emit('log', '安装程序成功退出');
                                } else {
                                    this.emit('error', `安装程序异常退出，代码: ${code}`);
                                    console.error(`安装程序异常退出，代码: ${code}`);
                                }
                            });

                            // 解除进程关联
                            childProcess.unref();

                            app.exit(0);
                        } else {
                            this.emit("log", "进入稍后更新模式");
                            if (!this.willQuitHandlerRegistered) {
                                this.willQuitHandlerRegistered = true;
                                const willQuitHandler = () => {
                                    this.emit("log", "程序即将推出，开始执行安装");

                                    if (!this.updatePath) {
                                        this.emit('error', 'Update path is not defined');
                                        return;
                                    }

                                    // 启动安装程序
                                    const childProcess = spawn(this.updatePath, args, {
                                        detached: true,
                                        stdio: 'ignore',
                                        windowsVerbatimArguments: true
                                    });

                                    // 记录进程信息
                                    this.emit('log', `启动安装程序，PID: ${childProcess.pid}`);

                                    // 监听进程事件
                                    childProcess.on('error', (error: Error) => {
                                        this.emit('error', `安装程序启动失败: ${error.message}`);
                                        console.error('安装程序启动失败:', error);
                                    });

                                    childProcess.on('exit', (code: number | null) => {
                                        if (code === 0) {
                                            this.emit('log', '安装程序成功退出');
                                        } else {
                                            this.emit('error', `安装程序异常退出，代码: ${code}`);
                                            console.error(`安装程序异常退出，代码: ${code}`);
                                        }
                                    });

                                    // 解除进程关联
                                    childProcess.unref();

                                    // 移除事件监听器
                                    app.removeListener('will-quit', willQuitHandler);
                                    this.willQuitHandlerRegistered = false;
                                };

                                app.on('will-quit', willQuitHandler);
                            }
                        }



                    } catch (e) {
                        this.emit('error', e);
                        console.error('Windows 更新安装失败:', e);

                        // 显示错误对话框
                        dialog.showMessageBox({
                            type: 'error',
                            title: '更新安装失败',
                            message: '无法启动更新安装程序，请尝试手动安装。',
                            detail: e.message
                        });
                    }
                }

                break;
            default:
                if (this.updatePath) {
                    fs.chmodSync(this.updatePath, 0o755);
                }
                const appImageFile = process.env.APPIMAGE;
                if (appImageFile == null) {
                    this.emit('error', "APPIMAGE env is not defined");
                }

                if (appImageFile) {
                    fs.unlinkSync(appImageFile);
                }

                let destination;
                if (this.updatePath && appImageFile && path.basename(this.updatePath) === path.basename(appImageFile)) {
                    // no version in the file name, overwrite existing
                    destination = appImageFile;
                } else if (appImageFile && this.updatePath) {
                    destination = path.join(path.dirname(appImageFile), path.basename(this.updatePath));
                }

                if (this.updatePath && destination) {
                    execSync(`mv -f ${this.updatePath} ${destination}`);
                }

                app.relaunch({
                    args: process.argv.slice(1).concat(['--relaunch']),
                    execPath: destination
                });
                app.exit(0);
                break;
        }
    }

    isDownload(filePath: string, expectedSha256?: string): boolean {
        // 检查文件是否存在
        if (fs.existsSync(filePath)) {
            // 读取文件内容
            const fileContent = fs.readFileSync(filePath);

            // 计算文件的 SHA256 哈希值
            const hash = crypto.createHash('sha256');
            hash.update(fileContent);
            const sha256 = hash.digest('hex');

            // 对比哈希值
            if (sha256 === expectedSha256) {
                console.log('文件哈希与预期值匹配');
                return false;
            } else {
                console.log('文件哈希与预期值不匹配');
                return true;
            }
        } else {
            console.log('老旧更新文件不存在');
            return true;
        }
    }

    on(event: string, cb: (params?: any) => void): void {
        this[`${event}_cb`] = cb;
    }

    emit(event: string, params?: any): void {
        if (event === 'log' && !this.log) return;
        const callback = this[`${event}_cb`];
        if (typeof callback === 'function') {
            callback(params);
        }
    }

    /**
     * 获取指定路径所在分区的剩余和总空间（字节）
     * @param absolutePath 绝对路径
     * @returns Promise<{ free: number, total: number }>
     */
    static async getDiskFreeSpace(absolutePath: string): Promise<{ free: number, total: number }> {
        const platform = process.platform;
        if (platform === 'win32') {
            // Windows: 只用 Node.js 内置模块和 wmic 命令
            // 确保输入是绝对路径
            if (!path.isAbsolute(absolutePath)) {
                throw new Error('必须提供绝对路径');
            }

            // 获取路径的驱动器号（如 C:）
            const driveLetter = path.parse(absolutePath).root.split(':')[0];
            if (!driveLetter) {
                throw new Error('无法解析驱动器号');
            }

            // 检查访问权限
            try {
                await fs.promises.access(`${driveLetter}:\\`, fs.constants.R_OK);
                console.log('有读取权限:', `${driveLetter}:\\`);
            } catch (error) {
                console.error(`无权限访问 ${driveLetter}: 驱动器: ${error.message}`);
                throw new Error(`无权限访问 ${driveLetter}: 驱动器: ${error.message}`);
            }

            // 使用 wmic 命令获取分区信息
            try {
                const command = `wmic logicaldisk where "DeviceID='${driveLetter}:'" get FreeSpace,Size /value`;
                const output = execSync(command).toString().trim();
                // 输出格式: FreeSpace=123456\nSize=987654\n
                let free = null, total = null;
                output.split(/\r?\n/).forEach((line: string) => {
                    if (line.startsWith('FreeSpace=')) {
                        free = parseInt(line.replace('FreeSpace=', '').trim(), 10);
                    } else if (line.startsWith('Size=')) {
                        total = parseInt(line.replace('Size=', '').trim(), 10);
                    }
                });
                if (typeof free !== 'number' || isNaN(free) || typeof total !== 'number' || isNaN(total)) {
                    throw new Error('无法获取磁盘信息');
                }
                return { free, total };
            } catch (error) {
                console.error(`执行WMIC命令失败，可能需要管理员权限: ${error.message}`);
                throw new Error(`执行WMIC命令失败，可能需要管理员权限: ${error.message}`);
            }
        } else {
            // macOS/Linux: df -k <path>
            return new Promise((resolve, reject) => {
                const cmd = `df -k '${absolutePath.replace(/'/g, "'\\''")}'`;
                exec(cmd, (err: any, stdout: string) => {
                    if (err) return reject(err);
                    const lines = stdout.trim().split(/\r?\n/);
                    if (lines.length < 2) return reject(new Error('未找到磁盘信息'));
                    // 取第二行
                    const columns = lines[1].split(/\s+/);
                    if (columns.length < 6) return reject(new Error('磁盘信息格式错误'));
                    const total = parseInt(columns[1], 10) * 1024;
                    const free = parseInt(columns[3], 10) * 1024;
                    resolve({ free, total });
                });
            });
        }
    }
}
