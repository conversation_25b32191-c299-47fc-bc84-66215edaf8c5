/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import CustomAdapter from './custom-adapter.js';
import { CustomAdapter as ICustomAdapter } from './types.js';

interface Config {
    type: string;
    options: any;
}

export default function (config: Config): ICustomAdapter {
    if (!config) throw new Error('Can not find update config');
    if (!config.type) throw new Error(`Can not find update config's type field`);
    if (!config.options) throw new Error(`Can not find update config's options field`);
    switch (config.type) {
        case 'custom':
            return new CustomAdapter(config.options);
        default:
            throw new Error(`Can not find update adapter`);
    }
}
