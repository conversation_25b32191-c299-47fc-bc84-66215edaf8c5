/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import autoUpdater from './app.js';
import { UpdateOptions, CustomAdapter } from './types.js';

// 生成更新服务
export const createUpdate = (options?: UpdateOptions): CustomAdapter => {
	const { upDataUrl = "", joyCoderVersion = '0.0.0' } = options || {};
	return autoUpdater({
		type: 'custom',
		options: {
			joyCoderVersion,
			latestVersion: '',
			feedUrl: '',
			upDataInfo: {},
			latestRelease: {
				osx: '',
				linux: '',
				windows: ''
			},
			emit(_event: string, _params?: any) {
				// 实现在 base-adapter.js 中
			},
			getRemoteLatest() {
				return new Promise(async (resolve) => {
					try {
						this.joyCoderVersion = joyCoderVersion;
						const isWindows = (process.platform === 'win32');
						const isLinux = (process.platform === 'linux');
						const isDarwin = (process.platform === 'darwin');
						// 完善信息，根据操作系统和版本号获取
						// 【本地测试】接口地址写死。 生产环境未发布该接口，需要李志强哥支持。
						// const response = await fetch("https://joycoder-api-inner-pr.jd.com/api/saas/ideVersion/v1/version/joycoder-ide/stable/darwin-arm64/latest.json");
						const response = await fetch(upDataUrl + "latest.json");
						const data = await response.json();

						if (data && data.version) {
							this.latestVersion = data.version;
							this.feedUrl = data.fileUrl;
							this.upDataInfo = {
								version: data.version,
								url: data.fileUrl,
								sha256: data.sha256,
								forceUpdate: data.forceUpdate,
							};

							// 根据平台设置下载链接
							if (data.fileUrl) {
								if (isDarwin) {
									this.latestRelease.osx = data.fileUrl;
								}
								if (isWindows) {
									this.latestRelease.windows = data.fileUrl;
								}
								if (isLinux) {
									this.latestRelease.linux = data.fileUrl;
								}
							}

							// // 更新本地版本号，用于更新日志
							// this.emit('update-available', this.upDataInfo)

							// 记录更新日志
							this.emit('log', {
								version: this.latestVersion,
								downloads: this.latestRelease
							});

							resolve(this.latestVersion);
						} else {
							console.error('更新数据无效:', data);
							resolve('0.0.0');
						}
					} catch (error) {
						console.error('检查更新时出错:', error);
						resolve('0.0.0');
					}
				});
			},
			log: true
		}
	});
}

