/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as electron from 'electron';
import { CancellationToken } from '../../../base/common/cancellation.js';
import { streamToBuffer } from '../../../base/common/buffer.js';
import { IConfigurationService } from '../../configuration/common/configuration.js';
import { IEnvironmentMainService } from '../../environment/electron-main/environmentMainService.js';
import { ILifecycleMainService } from '../../lifecycle/electron-main/lifecycleMainService.js';
import { ILogService } from '../../log/common/log.js';
import { IProductService } from '../../product/common/productService.js';
import { IRequestService } from '../../request/common/request.js';
import { IUpdate, State, UpdateType } from '../common/update.js';
import { AbstractUpdateService, createUpdateURL } from './abstractUpdateService.js';
import { IDialogMainService } from '../../dialogs/electron-main/dialogMainService.js';
import { localize } from '../../../nls.js';
import { IJoyCoderUserService } from '../../user/joyCoderUserService.js';
interface VersionResponse {
	version: string;
	files: Array<{
		url: string;
		sha512: string;
		size: number;
	}>;
	path: string;
	indexUrl: string;
	forceUpdate: boolean;
}


export class SimpleUpdateService extends AbstractUpdateService {

	protected override url: string | undefined;
	private currentVersion: string | undefined;

	constructor(
		@ILifecycleMainService lifecycleMainService: ILifecycleMainService,
		@IConfigurationService configurationService: IConfigurationService,
		@IEnvironmentMainService environmentMainService: IEnvironmentMainService,
		@IRequestService requestService: IRequestService,
		@ILogService logService: ILogService,
		@IProductService productService: IProductService,
		@IDialogMainService private readonly dialogMainService: IDialogMainService,
		@IJoyCoderUserService private readonly joyCoderUserService: IJoyCoderUserService

	) {
		super(lifecycleMainService, configurationService, environmentMainService, requestService, logService, productService);

		// 获取当前版本
		this.currentVersion = this.productService.version;
	}



	protected override async initialize(): Promise<void> {
		await super.initialize();
		// 设置初始状态
		this.setState(State.Idle(UpdateType.Archive));

	}

	protected buildUpdateFeedUrl(quality: string): string | undefined {
		let assetID: string;
		if (!this.productService.darwinUniversalAssetId) {
			assetID = process.arch === 'x64' ? 'darwin' : 'darwin-arm64';
		} else {
			assetID = this.productService.darwinUniversalAssetId;
		}
		const url = createUpdateURL(assetID, quality, this.productService);
		try {
			// electron.autoUpdater.setFeedURL({ url });
			this.url = url;
		} catch (e) {
			// application is very likely not signed
			this.logService.error('Failed to set update feed URL', e);
			return undefined;
		}
		return url;
	}


	protected doCheckForUpdates(explicit: boolean): void {

		this.logService.info('SimpleUpdateService: Checking for updates...');
		// 发送请求检查更新
		const userInfo = this.joyCoderUserService.getUserInfo();
		const ptkey = userInfo?.ptKey || '';

		this.requestService.request({
			url: this.url + "latest.yml",
			headers: {
				'ptkey': ptkey
			}
		}, CancellationToken.None)
			.then(async context => {
				// 解析响应
				const buffer = await streamToBuffer(context.stream);
				const response = buffer.toString();
				try {
					// 解析YAML格式的响应（这里简化处理为JSON）
					const updateInfo = JSON.parse(response) as VersionResponse;
					this.logService.info(`SimpleUpdateService: Server version: ${updateInfo.version}, Current version: ${this.currentVersion}`);
					// 比较版本号
					if (this.shouldUpdate(updateInfo.version, this.currentVersion || '')) {
						this.logService.info(`SimpleUpdateService: Update available: ${updateInfo.version}`);

						// 创建更新对象
						const update: IUpdate = {
							version: updateInfo.version,
							productVersion: updateInfo.version
						};
						// 显示通知
						this.showUpdateNotification(update, updateInfo);
					} else {
						this.logService.info('SimpleUpdateService: No update available');
					}
				} catch (error) {
					this.logService.error('SimpleUpdateService: Error parsing update response', error);
				}
			})
			.catch(error => {
				this.logService.error('SimpleUpdateService: Error checking for updates', error);
			});
	}

	/**
	 * 比较版本号，判断是否需要更新
	 * @param serverVersion 服务器版本
	 * @param currentVersion 当前版本
	 * @returns 如果服务器版本大于当前版本，返回true
	 */
	private shouldUpdate(serverVersion: string, currentVersion: string): boolean {
		// 简单的版本比较，假设版本号格式为 x.y.z
		const serverParts = serverVersion.split('.').map(Number);
		const currentParts = currentVersion.split('.').map(Number);

		for (let i = 0; i < Math.max(serverParts.length, currentParts.length); i++) {
			const serverPart = serverParts[i] || 0;
			const currentPart = currentParts[i] || 0;
			if (serverPart > currentPart) {
				return true;
			} else if (serverPart < currentPart) {
				return false;
			}
		}

		// 版本相同
		return false;
	}

	/**
	 * 显示更新通知
	 * @param update 更新信息
	 */
	private async showUpdateNotification(update: IUpdate, updateInfo: VersionResponse): Promise<void> {
		var updateButton = [localize('autoUpdate.buttonDownload', "去下载")]
		if (!updateInfo.forceUpdate) {
			updateButton.push(localize('autoUpdate.cancle', "取消"))
		}

		await this.dialogMainService.showMessageBox({
			type: 'info',
			title: localize('autoUpdate.title', '发现新版本'),
			message: localize('autoUpdate.message', 'JoyCode IDE {0} 可用', updateInfo.version),
			detail: localize('autoUpdate.detail', '点击"下载"按钮前往下载页面更新。'),
			buttons: updateButton,
			defaultId: 0,
			cancelId: 1,
		}, electron.BrowserWindow.getFocusedWindow() ?? undefined).then(({ response }) => {
			if (response === 0) {
				this.logService.info('SimpleUpdateService: Update dialog confirmed, opening download page');
				electron.shell.openExternal(updateInfo.indexUrl);
			}
		});

	}

}
