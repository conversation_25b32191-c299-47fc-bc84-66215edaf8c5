/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { timeout } from '../../../base/common/async.js';
import { CancellationToken } from '../../../base/common/cancellation.js';
import { Emitter, Event } from '../../../base/common/event.js';
import { IConfigurationService } from '../../configuration/common/configuration.js';
import { IEnvironmentMainService } from '../../environment/electron-main/environmentMainService.js';
import { ILifecycleMainService, LifecycleMainPhase } from '../../lifecycle/electron-main/lifecycleMainService.js';
import { ILogService } from '../../log/common/log.js';
import { IProductService } from '../../product/common/productService.js';
import { IRequestService } from '../../request/common/request.js';
import { AvailableForDownload, DisablementReason, IUpdateService, State, StateType, UpdateType } from '../common/update.js';

/**
 * 创建更新URL
 * @param platform 平台
 * @param quality 质量
 * @param productService 产品服务
 * @returns 更新URL字符串
 */
export function createUpdateURL(platform: string, quality: string, productService: IProductService): string {
	// return `https://joycoder.s3.cn-north-1.jdcloud-oss.com/joycoder-ide/darwin-x64/`;
	console.log("createUpdateURL", `${productService.updateUrl}/stable/${platform}/`);
	return `${productService.updateUrl}/stable/${platform}/`;
	// return `${productService.updateUrl}/api/update/${platform}/${quality}/${productService.commit}`;
}

export type UpdateNotAvailableClassification = {
	owner: 'joaomoreno';
	explicit: { classification: 'SystemMetaData'; purpose: 'FeatureInsight'; comment: 'Whether the user has manually checked for updates, or this was an automatic check.' };
	comment: 'This is used to understand how often VS Code pings the update server for an update and there\'s none available.';
};

export type UpdateErrorClassification = {
	owner: 'joaomoreno';
	messageHash: { classification: 'SystemMetaData'; purpose: 'FeatureInsight'; comment: 'The hash of the error message.' };
	comment: 'This is used to know how often VS Code updates have failed.';
};

export abstract class AbstractUpdateService implements IUpdateService {

	declare readonly _serviceBrand: undefined;

	protected url: string | undefined;

	private _state: State = State.Uninitialized;

	private readonly _onStateChange = new Emitter<State>();
	readonly onStateChange: Event<State> = this._onStateChange.event;

	get state(): State {
		return this._state;
	}

	protected setState(state: State): void {
		this.logService.info('update#setState', state.type);
		this._state = state;
		this._onStateChange.fire(state);
	}

	constructor(
		@ILifecycleMainService protected readonly lifecycleMainService: ILifecycleMainService,
		@IConfigurationService protected configurationService: IConfigurationService,
		@IEnvironmentMainService private readonly environmentMainService: IEnvironmentMainService,
		@IRequestService protected requestService: IRequestService,
		@ILogService protected logService: ILogService,
		@IProductService protected readonly productService: IProductService
	) {
		lifecycleMainService.when(LifecycleMainPhase.AfterWindowOpen)
			.finally(() => this.initialize());
	}


	// 新增: 调度每天早上6点的检查
	// private scheduleDailyCheckAt6AM(): Promise<void> {
	// 	// 获取当前时间
	// 	const now = new Date();
	//
	// 	// 计算下一个早上6点的时间
	// 	const nextCheckTime = new Date(
	// 		now.getFullYear(),
	// 		now.getMonth(),
	// 		now.getHours() >= 6 ? now.getDate() + 1 : now.getDate(),
	// 		6, 0, 0
	// 	);
	//
	// 	// 计算距离下次检查的延迟时间(毫秒)
	// 	const timeUntilNextCheck = nextCheckTime.getTime() - now.getTime();
	//
	// 	this.logService.info('update#ctor - 调度每天早上6点的检查:' + timeUntilNextCheck);
	// 	return timeout(timeUntilNextCheck)
	// 		.then(() => this.checkForUpdates(false))
	// 		.then(() => {
	// 			// 安排下一天的检查
	// 			return this.scheduleDailyCheckAt6AM();
	// 		});
	// }


	/**
	 * This must be called before any other call. This is a performance
	 * optimization, to avoid using extra CPU cycles before first window open.
	 * https://github.com/microsoft/vscode/issues/89784
	 */
	protected async initialize(): Promise<void> {
		if (!this.environmentMainService.isBuilt) {
			// this.setState(State.Disabled(DisablementReason.NotBuilt));
			// return; // updates are never enabled when running out of sources
		}

		if (this.environmentMainService.disableUpdates) {
			// this.setState(State.Disabled(DisablementReason.DisabledByEnvironment));
			// this.logService.info('update#ctor - updates are disabled by the environment');
			// return;
		}

		if (!this.productService.updateUrl || !this.productService.commit) {
			this.setState(State.Disabled(DisablementReason.MissingConfiguration));
			this.logService.info('update#ctor - updates are disabled as there is no update URL');
			return;
		}

		const updateMode = this.configurationService.getValue<'none' | 'manual' | 'start' | 'default'>('update.mode');
		const quality = this.getProductQuality(updateMode);

		if (!quality) {
			this.setState(State.Disabled(DisablementReason.ManuallyDisabled));
			this.logService.info('update#ctor - updates are disabled by user preference');
			return;
		}

		this.url = this.buildUpdateFeedUrl(quality);
		if (!this.url) {
			this.setState(State.Disabled(DisablementReason.InvalidConfiguration));
			this.logService.info('update#ctor - updates are disabled as the update URL is badly formed');
			return;
		}

		// hidden setting
		if (this.configurationService.getValue<boolean>('_update.prss')) {
			const url = new URL(this.url);
			url.searchParams.set('prss', 'true');
			this.url = url.toString();
		}

		this.setState(State.Idle(this.getUpdateType()));

		if (updateMode === 'manual') {
			this.logService.info('update#ctor - manual checks only; automatic updates are disabled by user preference');
			return;
		}

		if (updateMode === 'start') {
			this.logService.info('update#ctor - startup checks only; automatic updates are disabled by user preference');

			// Check for updates only once after 30 seconds
			setTimeout(() => this.checkForUpdates(false), 30 * 1000);
		} else {
			// Start checking for updates after 30 seconds
			this.scheduleCheckForUpdates(30 * 1000).then(undefined, err => this.logService.error(err));

			// 如果是 default 模式，额外添加每天早上6点的检查
			// if (updateMode === 'default') {
			// 	this.logService.info('update#ctor - scheduling additional daily check at 6:00 AM');
			// 	this.scheduleDailyCheckAt6AM().then(undefined, err => this.logService.error(err));
			// }
		}
	}

	private getProductQuality(updateMode: string): string | undefined {
		return updateMode === 'none' ? undefined : this.productService.quality;
	}

	private scheduleCheckForUpdates(delay = 60 * 60 * 1000): Promise<void> {
		return timeout(delay)
			.then(() => this.checkForUpdates(false))
			.then(() => {
				// Check again after 1 hour
				return this.scheduleCheckForUpdates(60 * 60 * 1000);
			});
	}

	async checkForUpdates(explicit: boolean): Promise<void> {
		this.logService.trace('update#checkForUpdates, state = ', this.state.type);

		if (this.state.type !== StateType.Idle) {
			return;
		}

		this.doCheckForUpdates(explicit);
	}

	async downloadUpdate(): Promise<void> {
		this.logService.trace('update#downloadUpdate, state = ', this.state.type);

		if (this.state.type !== StateType.AvailableForDownload) {
			return;
		}

		await this.doDownloadUpdate(this.state);
	}

	protected async doDownloadUpdate(state: AvailableForDownload): Promise<void> {
		// noop
	}

	async applyUpdate(): Promise<void> {
		this.logService.trace('update#applyUpdate, state = ', this.state.type);

		if (this.state.type !== StateType.Downloaded) {
			return;
		}

		await this.doApplyUpdate();
	}

	protected async doApplyUpdate(): Promise<void> {
		// noop
	}

	quitAndInstall(): Promise<void> {
		this.logService.trace('update#quitAndInstall, state = ', this.state.type);

		if (this.state.type !== StateType.Ready) {
			return Promise.resolve(undefined);
		}

		this.logService.trace('update#quitAndInstall(): before lifecycle quit()');

		// this.lifecycleMainService.quit(true /* will restart */).then(vetod => {
		// 	this.logService.trace(`update#quitAndInstall(): after lifecycle quit() with veto: ${vetod}`);
		// 	if (vetod) {
		// 		return;
		// 	}

		// 	this.logService.trace('update#quitAndInstall(): running raw#quitAndInstall()');
		// 	this.doQuitAndInstall();
		// });
		this.doQuitAndInstall();

		return Promise.resolve(undefined);
	}

	waitAndInstall(): Promise<void> {
		this.logService.trace('update#waitAndInstall, state = ', this.state.type);

		if (this.state.type !== StateType.Ready) {
			return Promise.resolve(undefined);
		}

		this.logService.trace('update#waitAndInstall(): before lifecycle quit()');

		// this.lifecycleMainService.quit(true /* will restart */).then(vetod => {
		// 	this.logService.trace(`update#quitAndInstall(): after lifecycle quit() with veto: ${vetod}`);
		// 	if (vetod) {
		// 		return;
		// 	}

		// 	this.logService.trace('update#quitAndInstall(): running raw#quitAndInstall()');
		// 	this.doQuitAndInstall();
		// });
		this.doWaitAndInstall();

		return Promise.resolve(undefined);
	}

	async isLatestVersion(): Promise<boolean | undefined> {
		if (!this.url) {
			return undefined;
		}

		const mode = this.configurationService.getValue<'none' | 'manual' | 'start' | 'default'>('update.mode');

		if (mode === 'none') {
			return false;
		}

		try {
			const context = await this.requestService.request({ url: this.url }, CancellationToken.None);
			// The update server replies with 204 (No Content) when no
			// update is available - that's all we want to know.
			return context.res.statusCode === 204;

		} catch (error) {
			this.logService.error('update#isLatestVersion(): failed to check for updates');
			this.logService.error(error);
			return undefined;
		}
	}

	async _applySpecificUpdate(packagePath: string): Promise<void> {
		// noop
	}

	protected getUpdateType(): UpdateType {
		return UpdateType.Archive;
	}

	protected doQuitAndInstall(): void {
		// noop
	}

	protected doWaitAndInstall(): void {
		// noop
	}

	protected abstract buildUpdateFeedUrl(quality: string): string | undefined;
	protected abstract doCheckForUpdates(context: any): void;
}
