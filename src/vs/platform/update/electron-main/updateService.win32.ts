/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { spawn } from 'child_process';
import * as fs from 'fs';
import { tmpdir } from 'os';
import { timeout } from '../../../base/common/async.js';
// import { CancellationToken } from '../../../base/common/cancellation.js';
import { memoize } from '../../../base/common/decorators.js';
// import { hash } from '../../../base/common/hash.js';
import * as path from '../../../base/common/path.js';
// import { URI } from '../../../base/common/uri.js';
// import { checksum } from '../../../base/node/crypto.js';
import * as pfs from '../../../base/node/pfs.js';
import { IConfigurationService } from '../../configuration/common/configuration.js';
import { IEnvironmentMainService } from '../../environment/electron-main/environmentMainService.js';
// import { IFileService } from '../../files/common/files.js';
import { ILifecycleMainService, IRelaunchHandler, IRelaunchOptions } from '../../lifecycle/electron-main/lifecycleMainService.js';
import { ILogService } from '../../log/common/log.js';
import { INativeHostMainService } from '../../native/electron-main/nativeHostMainService.js';
import { IProductService } from '../../product/common/productService.js';
import {
	// asJson,
	IRequestService
} from '../../request/common/request.js';
// import { ITelemetryService } from '../../telemetry/common/telemetry.js';
import { AvailableForDownload, DisablementReason, IUpdate, State, StateType, UpdateType } from '../common/update.js';
import {
	AbstractUpdateService, createUpdateURL,
	// UpdateErrorClassification,
	// UpdateNotAvailableClassification
} from './abstractUpdateService.js';
import { createUpdate } from "../custom/custom.js";
import { CustomAdapter } from "../custom/types.js";
import electron from "electron";
import {IDialogMainService} from "../../dialogs/electron-main/dialogMainService.js";


async function pollUntil(fn: () => boolean, millis = 1000): Promise<void> {
	while (!fn()) {
		await timeout(millis);
	}
}

interface IAvailableUpdate {
	packagePath: string;
	updateFilePath?: string;
}

let _updateType: UpdateType | undefined = undefined;
function getUpdateType(): UpdateType {
	if (typeof _updateType === 'undefined') {
		_updateType = fs.existsSync(path.join(path.dirname(process.execPath), 'unins000.exe'))
			? UpdateType.Setup
			: UpdateType.Archive;
	}

	return _updateType;
}

export class Win32UpdateService extends AbstractUpdateService implements IRelaunchHandler {

	private availableUpdate: IAvailableUpdate | undefined;
	private customUpdate: CustomAdapter;

	@memoize
	get cachePath(): Promise<string> {
		const result = path.join(tmpdir(), `vscode-${this.productService.quality}-${this.productService.target}-${process.arch}`);
		return fs.promises.mkdir(result, { recursive: true }).then(() => result);
	}

	constructor(
		@ILifecycleMainService lifecycleMainService: ILifecycleMainService,
		@IConfigurationService configurationService: IConfigurationService,
		// @ITelemetryService private readonly telemetryService: ITelemetryService,
		@IEnvironmentMainService environmentMainService: IEnvironmentMainService,
		@IRequestService requestService: IRequestService,
		@ILogService logService: ILogService,
		// @IFileService private readonly fileService: IFileService,
		@INativeHostMainService private readonly nativeHostMainService: INativeHostMainService,
		@IProductService productService: IProductService,
		@IDialogMainService private readonly dialogMainService: IDialogMainService
	) {
		super(lifecycleMainService, configurationService, environmentMainService, requestService, logService, productService);

		this.customUpdate = createUpdate({
			upDataUrl: this.buildUpdateFeedUrl(''),
			joyCoderVersion: this.productService.joyCoderVersion as string,
		});
		lifecycleMainService.setRelaunchHandler(this);
	}

	handleRelaunch(options?: IRelaunchOptions): boolean {
		if (options?.addArgs || options?.removeArgs) {
			return false; // we cannot apply an update and restart with different args
		}

		if (this.state.type !== StateType.Ready || !this.availableUpdate) {
			return false; // we only handle the relaunch when we have a pending update
		}

		this.logService.trace('update#handleRelaunch(): running raw#quitAndInstall()');
		this.doQuitAndInstall();

		return true;
	}

	protected override async initialize(): Promise<void> {
		if (this.productService.target === 'user' && await this.nativeHostMainService.isAdmin(undefined)) {
			this.setState(State.Disabled(DisablementReason.RunningAsAdmin));
			this.logService.info('update#ctor - updates are disabled due to running as Admin in user setup');
			return;
		}

		await super.initialize();

		this.customUpdate.on('checking-for-update', (info: any) => {
			this.logService.info('检测更新：', info);
			this.setState(State.CheckingForUpdates(false));
		});

		this.customUpdate.on('update-available', (info: any) => {
			const update: IUpdate = {
				version: info.version,
				productVersion: info.version,
				url: info.url
			};
			this.logService.info('开始更新：', update);
			this.setState(State.AvailableForDownload(update));
		});

		this.customUpdate.on('update-not-available', (info: any) => {
			this.logService.info('版本一致，无需更新。', info);
			this.setState(State.Idle(UpdateType.Setup));
			if (this.customUpdate.isClickMenu) {
				this.dialogMainService.showMessageBox({
					type: 'info',
					title: '检查更新',
					message: '当前没有可用更新',
					buttons: ['确定'],
				}, electron.BrowserWindow.getFocusedWindow() ?? undefined);
			}
		});

		this.customUpdate.on('error', (err: any) => {
			this.logService.info('更新错误:', err);
			if (typeof err === 'string' && err.includes('磁盘空间')) {
				// 通知所有窗口弹出磁盘空间不足的消息
				const allWindows = electron.BrowserWindow.getAllWindows();
				allWindows?.forEach(win => {
					// console.log('发送到窗口ID:', win.id);
					win.webContents.send('vscode:show-disk-space-warning', err);
				});
				// console.log('磁盘空间警告消息已全部发送');
			}
			this.setState(State.Idle(UpdateType.Setup));
		});

		this.customUpdate.on('download-progress', (percent: any) => {
			this.logService.info('下载进度', percent);
			if (this.customUpdate.isClickMenu && typeof percent ===  'number') {
				this.setState(State.DownloadingPro(percent));
			}else {
				this.setState(State.AutoDownloading);
			}
		});

		this.customUpdate.on('update-downloaded', (info: any) => {
			this.logService.info('下载完成', info);
			const update: IUpdate = {
				version: info.version,
				productVersion: info.version
			};
			this.setState(State.Downloaded(update));
			this.setState(State.Ready(update));
		});

		this.customUpdate.on('log', (info: any) => {
			this.logService.info('自动更新log', info);
		});
	}

	protected buildUpdateFeedUrl(quality: string): string | undefined {
		let platform = `win32-${process.arch}`;

		if (getUpdateType() === UpdateType.Archive) {
			// platform += '-archive';
			platform += '';
		} else if (this.productService.target === 'user') {
			platform += '';
		}

		return createUpdateURL(platform, quality, this.productService);
	}

	protected doCheckForUpdates(context: any): void {
		if (!this.url) {
			return;
		}

		this.setState(State.CheckingForUpdates(context));
		this.customUpdate.checkForUpdatesAndNotify(context);
	}

	protected override async doDownloadUpdate(state: AvailableForDownload): Promise<void> {
		if (state.update.url) {
			this.nativeHostMainService.openExternal(undefined, state.update.url);
		}
		this.setState(State.Idle(getUpdateType()));
	}

	// private async getUpdatePackagePath(version: string): Promise<string> {
	// 	const cachePath = await this.cachePath;
	// 	return path.join(cachePath, `CodeSetup-${this.productService.quality}-${version}.exe`);
	// }

	// private async cleanup(exceptVersion: string | null = null): Promise<void> {
	// 	const filter = exceptVersion ? (one: string) => !(new RegExp(`${this.productService.quality}-${exceptVersion}\\.exe$`).test(one)) : () => true;

	// 	const cachePath = await this.cachePath;
	// 	const versions = await pfs.Promises.readdir(cachePath);

	// 	const promises = versions.filter(filter).map(async one => {
	// 		try {
	// 			await fs.promises.unlink(path.join(cachePath, one));
	// 		} catch (err) {
	// 			// ignore
	// 		}
	// 	});

	// 	await Promise.all(promises);
	// }

	protected override async doApplyUpdate(): Promise<void> {
		if (this.state.type !== StateType.Downloaded) {
			return Promise.resolve(undefined);
		}

		if (!this.availableUpdate) {
			return Promise.resolve(undefined);
		}

		const update = this.state.update;
		this.setState(State.Updating(update));

		const cachePath = await this.cachePath;

		this.availableUpdate.updateFilePath = path.join(cachePath, `CodeSetup-${this.productService.quality}-${update.version}.flag`);

		await pfs.Promises.writeFile(this.availableUpdate.updateFilePath, 'flag');
		const child = spawn(this.availableUpdate.packagePath, ['/verysilent', '/log', `/update="${this.availableUpdate.updateFilePath}"`, '/nocloseapplications', '/mergetasks=runcode,!desktopicon,!quicklaunchicon'], {
			detached: true,
			stdio: ['ignore', 'ignore', 'ignore'],
			windowsVerbatimArguments: true
		});

		child.once('exit', () => {
			this.availableUpdate = undefined;
			this.setState(State.Idle(getUpdateType()));
		});

		const readyMutexName = `${this.productService.win32MutexName}-ready`;
		const mutex = await import('@vscode/windows-mutex');

		// poll for mutex-ready
		pollUntil(() => mutex.isActive(readyMutexName))
			.then(() => this.setState(State.Ready(update)));
	}

	protected override doQuitAndInstall(): void {
		if (this.state.type !== StateType.Ready) {
			return;
		}

		this.logService.trace('update#quitAndInstall(): running raw#quitAndInstall()');
		this.customUpdate.quitAndInstall();
	}

	protected override doWaitAndInstall(): void {
		if (this.state.type !== StateType.Ready) {
			return;
		}

		this.logService.trace('update#quitAndInstall(): running raw#quitAndInstall()');
		this.customUpdate.quitAndInstall(false);
	}

	protected override getUpdateType(): UpdateType {
		return getUpdateType();
	}

	override async _applySpecificUpdate(packagePath: string): Promise<void> {
		if (this.state.type !== StateType.Idle) {
			return;
		}

		const fastUpdatesEnabled = this.configurationService.getValue('update.enableWindowsBackgroundUpdates');
		const update: IUpdate = { version: 'unknown', productVersion: 'unknown' };

		this.setState(State.Downloading);
		this.availableUpdate = { packagePath };
		this.setState(State.Downloaded(update));

		if (fastUpdatesEnabled) {
			if (this.productService.target === 'user') {
				this.doApplyUpdate();
			}
		} else {
			this.setState(State.Ready(update));
		}
	}
}
