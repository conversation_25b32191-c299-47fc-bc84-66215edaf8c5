/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as electron from 'electron';
// import { MacUpdater } from 'electron-updater';
import { memoize } from '../../../base/common/decorators.js';
import { Event } from '../../../base/common/event.js';
import { hash } from '../../../base/common/hash.js';
import { DisposableStore } from '../../../base/common/lifecycle.js';
import { IConfigurationService } from '../../configuration/common/configuration.js';
import { IEnvironmentMainService } from '../../environment/electron-main/environmentMainService.js';
import { ILifecycleMainService, IRelaunchHandler, IRelaunchOptions } from '../../lifecycle/electron-main/lifecycleMainService.js';
import { ILogService } from '../../log/common/log.js';
import { IProductService } from '../../product/common/productService.js';
import { IRequestService } from '../../request/common/request.js';
import { ITelemetryService } from '../../telemetry/common/telemetry.js';
import { IUpdate, State, StateType, UpdateType } from '../common/update.js';
import { AbstractUpdateService, createUpdateURL, UpdateErrorClassification, UpdateNotAvailableClassification } from './abstractUpdateService.js';
import { createUpdate } from "../custom/custom.js";
import { CustomAdapter } from "../custom/types.js";
import { IDialogMainService } from '../../dialogs/electron-main/dialogMainService.js';


export class DarwinUpdateService extends AbstractUpdateService implements IRelaunchHandler {

	private readonly disposables = new DisposableStore();
	protected override url: string | undefined;
	private customUpdate: CustomAdapter;

	@memoize private get onRawError(): Event<string> { return Event.fromNodeEventEmitter(electron.autoUpdater, 'error', (_, message) => message); }
	@memoize private get onRawUpdateNotAvailable(): Event<void> { return Event.fromNodeEventEmitter<void>(electron.autoUpdater, 'update-not-available'); }
	@memoize private get onRawUpdateAvailable(): Event<void> { return Event.fromNodeEventEmitter(electron.autoUpdater, 'update-available'); }
	@memoize private get onRawUpdateDownloaded(): Event<IUpdate> { return Event.fromNodeEventEmitter(electron.autoUpdater, 'update-downloaded', (_, _releaseNotes, version, timestamp) => ({ version, productVersion: version, timestamp })); }

	constructor(
		@ILifecycleMainService lifecycleMainService: ILifecycleMainService,
		@IConfigurationService configurationService: IConfigurationService,
		@ITelemetryService private readonly telemetryService: ITelemetryService,
		@IEnvironmentMainService environmentMainService: IEnvironmentMainService,
		@IRequestService requestService: IRequestService,
		@ILogService logService: ILogService,
		@IProductService productService: IProductService,
		@IDialogMainService private readonly dialogMainService: IDialogMainService
	) {
		super(lifecycleMainService, configurationService, environmentMainService, requestService, logService, productService);

		this.customUpdate = createUpdate({
			upDataUrl: this.buildUpdateFeedUrl(''),
			joyCoderVersion: this.productService.joyCoderVersion as string,
		});
		lifecycleMainService.setRelaunchHandler(this);
	}

	handleRelaunch(options?: IRelaunchOptions): boolean {
		if (options?.addArgs || options?.removeArgs) {
			return false; // we cannot apply an update and restart with different args
		}

		if (this.state.type !== StateType.Ready) {
			return false; // we only handle the relaunch when we have a pending update
		}

		this.logService.trace('update#handleRelaunch(): running raw#quitAndInstall()');
		this.doQuitAndInstall();

		return true;
	}

	protected override async initialize(): Promise<void> {
		await super.initialize();
		this.onRawError(this.onError, this, this.disposables);
		this.onRawUpdateAvailable(this.onUpdateAvailable, this, this.disposables);
		this.onRawUpdateDownloaded(this.onUpdateDownloaded, this, this.disposables);
		this.onRawUpdateNotAvailable(this.onUpdateNotAvailable, this, this.disposables);

		this.customUpdate.on('checking-for-update', (info: any) => {
			this.logService.info('检测更新：', info);
			this.setState(State.CheckingForUpdates(false));
		});
		this.customUpdate.on('update-available', (info: any) => {
			const update: IUpdate = {
				version: info.version,
				productVersion: info.version,
				url: info.url
			};
			this.logService.info('开始更新：', update);
			this.setState(State.AvailableForDownload(update));
		});
		this.customUpdate.on('update-not-available', (info: any) => {
			this.logService.info('版本一致，无需更新。', info);
			this.setState(State.Idle(UpdateType.Setup));
			if (this.customUpdate.isClickMenu) {
				this.dialogMainService.showMessageBox({
					type: 'info',
					title: '检查更新',
					message: '当前没有可用更新',
					buttons: ['确定'],
				}, electron.BrowserWindow.getFocusedWindow() ?? undefined);
			}
		});

		this.customUpdate.on('error', (err: any) => {
			this.logService.info('更新错误:', err);
			if (typeof err === 'string' && err.includes('磁盘空间')) {
				// 通知所有窗口弹出磁盘空间不足的消息
				const allWindows = electron.BrowserWindow.getAllWindows();
				allWindows?.forEach(win => {
					// console.log('发送到窗口ID:', win.id);
					win.webContents.send('vscode:show-disk-space-warning', err);
				});
				// console.log('磁盘空间警告消息已全部发送');
			}
			this.setState(State.Idle(UpdateType.Setup));
		});

		this.customUpdate.on('download-progress', (percent: any) => {
			this.logService.info('下载进度', percent);
			if (this.customUpdate.isClickMenu && typeof percent === 'number') {
				this.setState(State.DownloadingPro(percent));
			} else {
				this.setState(State.AutoDownloading);
			}
		});

		this.customUpdate.on('update-downloaded', (info: any) => {
			this.logService.info('下载完成', info);

			const update: IUpdate = {
				version: info.version,
				productVersion: info.version
			};
			this.setState(State.Downloaded(update));
			this.onUpdateDownloaded(update);
		});

		this.customUpdate.on('log', (info: any) => {
			this.logService.info('自动更新log', info);
		});

	}

	private onError(err: string): void {
		this.telemetryService.publicLog2<{ messageHash: string }, UpdateErrorClassification>('update:error', { messageHash: String(hash(String(err))) });
		this.logService.error('UpdateService error:', err);

		// only show message when explicitly checking for updates
		const message = (this.state.type === StateType.CheckingForUpdates && this.state.explicit) ? err : undefined;
		this.setState(State.Idle(UpdateType.Archive, message));
	}

	protected buildUpdateFeedUrl(quality: string): string | undefined {
		let assetID: string;
		if (!this.productService.darwinUniversalAssetId) {
			assetID = process.arch === 'x64' ? 'darwin-x64' : 'darwin-arm64';
		} else {
			assetID = this.productService.darwinUniversalAssetId;
		}
		const url = createUpdateURL(assetID, quality, this.productService);
		try {
			// electron.autoUpdater.setFeedURL({ url });
			this.url = url;
		} catch (e) {
			// application is very likely not signed
			this.logService.error('Failed to set update feed URL', e);
			return undefined;
		}
		return url;
	}

	protected doCheckForUpdates(_context: any): void {
		this.logService.info('检查更新是否为用户主动触发:', _context);
		// @ts-ignore
		const customUpdate = this.customUpdate;
		// 检测更新
		customUpdate.checkForUpdatesAndNotify(!!_context);
	}
	// protected doCheckForUpdates2(context: any): void {
	// 	// customUpdate;
	//
	// 	this.setState(State.CheckingForUpdates(context));
	// 	// electron.autoUpdater.checkForUpdates();
	// 	const options: any = {
	// 		requestHeaders: {
	// 			// Any request headers to include here
	// 		},
	// 		provider: 'generic',
	// 		url: this.url,
	// 		// 移除自定义缓存目录名称，使用默认的基于应用程序ID的缓存目录
	// 	}
	// 	this.logService.info('MacUpdater options:', JSON.stringify(options, null, 2));
	// 	const autoUpdater = new MacUpdater(options);
	// 	this.logService.info('MacUpdater instance created');
	// 	autoUpdater.setFeedURL({
	// 		provider: 'generic',
	// 		url: this.url ?? '',
	// 	});
	// 	// autoUpdater.downloadPath = path.join(os.homedir(), '.config', 'Code', 'config.json');
	// 	autoUpdater.forceDevUpdateConfig = true; // 开发环境强制检测
	// 	autoUpdater.disableWebInstaller = true;
	// 	autoUpdater.allowDowngrade = false;
	// 	autoUpdater.allowPrerelease = false;
	// 	autoUpdater.disableDifferentialDownload = false;
	// 	autoUpdater.fullChangelog = true;
	// 	// 自动下载
	// 	autoUpdater.autoDownload = true; // 启用自动下载
	//
	// 	// 触发检测
	// 	this.logService.info('开始检查更新，URL:', this.url);
	// 	autoUpdater.checkForUpdatesAndNotify().catch((err) => {
	// 		this.logService.error('checkForUpdatesAndNotify错误:', err);
	// 		// 尝试直接调用checkForUpdates作为备选方案
	// 		try {
	// 			autoUpdater.checkForUpdates().catch(e => this.logService.error('checkForUpdates备选方案错误:', e));
	// 		} catch (e) {
	// 			this.logService.error('调用checkForUpdates失败:', e);
	// 		}
	// 	});
	//
	// 	const isUpdaterActive = autoUpdater.isUpdaterActive();
	// 	this.logService.info('更新服务状态 isUpdaterActive:', isUpdaterActive);
	//
	// 	autoUpdater.on('update-available', (info) => {
	// 		// const cachePath =path.join(os.homedir(), 'Library/Caches/JoyCode'); // 指定更新缓存路径
	// 		// 设置下载路径
	// 		// autoUpdater.downloadPath = cachePath;
	// 		this.logService.info('有新的更新可用:', info);
	// 		this.logService.info('开始下载更新...');
	//
	// 		autoUpdater.downloadUpdate().catch(e => this.logService.error('下载更新失败:', e));
	// 	});
	//
	// 	autoUpdater.on('update-not-available', (info) => {
	// 		this.logService.info("无需更新:", info);
	// 	});
	//
	// 	autoUpdater.on('download-progress', (progress) => {
	// 		this.logService.info(`下载进度: ${progress.percent}%`);
	// 	});
	//
	// 	autoUpdater.on('update-downloaded', (info) => {
	// 		this.logService.info('更新已下载，准备安装');
	// 		// 在这里可以获取下载的文件路径
	// 		this.logService.info('更新已下载，准备安装，获取安装路径' + info.downloadedFile);
	// 		autoUpdater.quitAndInstall();
	// 	});
	//
	// 	autoUpdater.on('error', (error) => {
	// 		this.logService.error('更新失败:', error);
	// 		// 记录更详细的错误信息
	// 		if (error && error.stack) {
	// 			this.logService.error('错误堆栈:', error.stack);
	// 		}
	// 		// 添加更多诊断信息
	// 		this.logService.error('当前应用版本:', this.productService.version);
	// 		this.logService.error('更新 URL:', this.url);
	// 	});
	//
	// 	// 添加新的日志记录
	// 	this.logService.info('当前应用版本:', this.productService.version);
	// 	this.logService.info('更新 URL:', this.url);
	// }

	private onUpdateAvailable(): void {
		if (this.state.type !== StateType.CheckingForUpdates) {
			return;
		}

		this.setState(State.Downloading);
	}

	private onUpdateDownloaded(update: IUpdate): void {
		// if (this.state.type !== StateType.Downloading) {
		// 	return;
		// }

		this.setState(State.Downloaded(update));

		type UpdateDownloadedClassification = {
			owner: 'joaomoreno';
			version: { classification: 'SystemMetaData'; purpose: 'FeatureInsight'; comment: 'The version number of the new VS Code that has been downloaded.' };
			comment: 'This is used to know how often VS Code has successfully downloaded the update.';
		};
		this.telemetryService.publicLog2<{ version: String }, UpdateDownloadedClassification>('update:downloaded', { version: update.version });

		this.setState(State.Ready(update));
	}

	private onUpdateNotAvailable(): void {
		if (this.state.type !== StateType.CheckingForUpdates) {
			return;
		}
		this.telemetryService.publicLog2<{ explicit: boolean }, UpdateNotAvailableClassification>('update:notAvailable', { explicit: this.state.explicit });

		this.setState(State.Idle(UpdateType.Archive));
	}

	protected override doQuitAndInstall(): void {
		this.logService.trace('update#quitAndInstall(): running raw#quitAndInstall()');
		// @ts-ignore
		const customUpdate = this.customUpdate;
		this.logService.info('customUpdate instance:', customUpdate);
		this.logService.info('updatePath:', customUpdate.updatePath);

		if (!customUpdate.updatePath) {
			this.logService.error('Cannot quit and install: updatePath is not set. Make sure download is completed.');
			return;
		}

		// 立即重启安装
		customUpdate.quitAndInstall();
	}

	protected override doWaitAndInstall(): void {
		this.logService.trace('update#waitAndInstall(): running raw#waitAndInstall()');
		// @ts-ignore
		const customUpdate = this.customUpdate;
		this.logService.info('customUpdate instance:', customUpdate);
		this.logService.info('updatePath:', customUpdate.updatePath);

		if (!customUpdate.updatePath) {
			this.logService.error('Cannot wait and install: updatePath is not set. Make sure download is completed.');
			return;
		}

		// 立即重启安装
		customUpdate.quitAndInstall(false);
	}

	dispose(): void {
		this.disposables.dispose();
	}
}
