import { IStorageMainService } from '../../platform/storage/electron-main/storageMainService.js';
import { IUserDataProfilesMainService } from '../../platform/userDataProfile/electron-main/userDataProfile.js';
import { createDecorator } from '../instantiation/common/instantiation.js';
import { ILifecycleMainService, LifecycleMainPhase } from '../lifecycle/electron-main/lifecycleMainService.js';
import { ILogService } from '../log/common/log.js';
import { asJson, IRequestService } from '../request/common/request.js';
import { IProductService } from '../product/common/productService.js';
import { CancellationToken } from '../../base/common/observableInternal/commonFacade/cancellation.js';
import { Disposable } from '../../base/common/lifecycle.js';
import { fetchUserInfo } from '../login/electron-main/loginMainService.js';

export interface UserInfo {
	userName: string;
	userId: string;
	loginType: string;
	loginUrl: string;
	pk: string;
	ptKey: string;
}
export interface CommonResult<T = ConfigData> {
	code: number;
	msg: string;
	data: T;
}

export interface ConfigData {
	mainKey: string;
	syncKeys: string[];
}



export interface JoyCoderEditorValue {
	jdhLoginInfo: UserInfo;
	joyCoderUser: UserInfo;
}

export interface IJoyCoderUserService {
	getJoyCoderEditorValue(mainKey: string): JoyCoderEditorValue | null;
	getUserInfo(): UserInfo | null;
	writeMainUserInfo(joyCoderUser: UserInfo | null): void;
	syncUserInfo(): Promise<void>;
}
export const IJoyCoderUserService = createDecorator<IJoyCoderUserService>('joyCoderUserService');


export class JoyCoderUserService extends Disposable implements IJoyCoderUserService {
	private intervalId: NodeJS.Timeout | null = null;
	// private readonly syncInterval = 60 * 1000; // 1分钟

	constructor(
		@IStorageMainService private readonly storageMainService: IStorageMainService,
		@IUserDataProfilesMainService private readonly userDataProfilesService: IUserDataProfilesMainService,
		@ILogService private readonly logService: ILogService,
		@ILifecycleMainService private readonly lifecycleMainService: ILifecycleMainService,
		@IRequestService private readonly requestService: IRequestService,
		@IProductService private readonly productService: IProductService
	) {
		super();
		this._register(this.lifecycleMainService.onWillShutdown(() => this.dispose()));
		this.lifecycleMainService.when(LifecycleMainPhase.AfterWindowOpen)
			.finally(() => this.initialize());
	}
	async syncUserInfo(): Promise<void> {
		const userInfo = this.getUserInfo();
		if (!this.isValidUserInfo(userInfo)) {
			this.clearMainUser();
			this.onlySyncUser(null);
			return;
		}

		try {
			const queryUser = await this.fetchAndValidateUserInfo(userInfo);
			if (!this.isValidUserInfo(queryUser)) {
				this.logService.debug('Invalid user info received from server');
				this.clearMainUser();
				this.onlySyncUser(null);
				return;
			}
			this.onlySyncUser(userInfo);

		} catch (error) {
			this.handleSyncError(error);
		}
	}

	async onlySyncUser(userInfo: any): Promise<void> {
		const syncKeyInfo = await this.getKeyList(userInfo?.ptKey);
		if (!this.isValidSyncKeyInfo(syncKeyInfo)) {
			this.logService.debug('No sync keys available for user');
			return;
		}
		this.syncUserInfoToKeys(syncKeyInfo, userInfo);
	}

	async clearMainUser(): Promise<void> {
		const baseGlobalName = this.productService.baseGlobalName;
		const baseGlobalUserKey = this.productService.baseGlobalUserKey;
		const globalStorage = this.storageMainService.profileStorage(this.userDataProfilesService.defaultProfile);
		if (baseGlobalName && baseGlobalUserKey) {
			const rowData = globalStorage.storage.get(baseGlobalName, '{}');
			const rowJson = JSON.parse(rowData);
			rowJson[baseGlobalUserKey] = {};
			globalStorage.storage.set(baseGlobalName, JSON.stringify(rowJson));
		}

	}

	private isValidUserInfo(userInfo: any): userInfo is UserInfo {
		return userInfo && typeof userInfo.ptKey === 'string' && userInfo.ptKey.trim() !== '' &&
			typeof userInfo.userId === 'string' && userInfo.userId.trim() !== '' &&
			typeof userInfo.loginType === 'string';
	}

	private async fetchAndValidateUserInfo(userInfo: { ptKey: string; loginType: string }): Promise<any> {
		return await fetchUserInfo(userInfo.ptKey, userInfo.loginType, this.productService, this.logService);
	}

	private isValidSyncKeyInfo(syncKeyInfo: any): syncKeyInfo is { mainKey: string; syncKeys: string[] } {
		return syncKeyInfo && syncKeyInfo.mainKey && Array.isArray(syncKeyInfo.syncKeys) && syncKeyInfo.syncKeys.length > 0;
	}


	private handleSyncError(error: unknown): void {
		if (error instanceof Error) {
			this.logService.error(`Failed to sync user info: ${error.message}`);
		} else {
			this.logService.error('Failed to sync user info: Unknown error');
		}
	}

	initialize(): void {
		this.logService.info('JoyCoderUserService initializing');
	}

	getJoyCoderEditorValue(mainKey: string): JoyCoderEditorValue | null {
		if (!mainKey) {
			this.logService.debug('getJoyCoderEditorValue called with empty mainKey');
			return null;
		}
		const globalStorage = this.storageMainService.profileStorage(this.userDataProfilesService.defaultProfile);
		const rawValue = globalStorage.storage.get(mainKey, '');

		if (!rawValue) {
			return null;
		}

		try {
			return JSON.parse(rawValue) as JoyCoderEditorValue;
		} catch (error) {
			this.logService.error('Failed to parse JoyCode editor value:', error);
			return null;
		}
	}

	getUserInfo(): UserInfo | null {
		// 首先尝试从IDE获取用户信息
		const baseGlobalName = this.productService.baseGlobalName;
		if (baseGlobalName) {
			const editorValueIde = this.getJoyCoderEditorValue(baseGlobalName);
			if (this.isValidUserInfo(editorValueIde?.joyCoderUser)) {
				return editorValueIde?.joyCoderUser;
			}
		}
		// 如果IDE中没有用户信息，尝试从扩展中获取
		// TODO: 待IDE开发完登录态后可以删除以下代码逻辑
		// const extGlobalName = this.productService.joyCoderExtGlobalName;
		// if (extGlobalName) {
		// 	const editorValue = this.getJoyCoderEditorValue(extGlobalName);
		// 	if (this.isValidUserInfo(editorValue?.jdhLoginInfo)) {
		// 		return editorValue?.jdhLoginInfo;
		// 	}
		// }

		return null;
	}

	writeMainUserInfo(joyCoderUser: UserInfo | null): void {
		const baseGlobalName = this.productService.baseGlobalName;
		const baseGlobalUserKey = this.productService.baseGlobalUserKey;
		if (baseGlobalName && baseGlobalUserKey) {
			const globalStorage = this.storageMainService.profileStorage(this.userDataProfilesService.defaultProfile);
			const rowData = globalStorage.storage.get(baseGlobalName, '{}');
			const rowJson = JSON.parse(rowData);
			rowJson[baseGlobalUserKey] = joyCoderUser || {};
			globalStorage.storage.set(baseGlobalName, JSON.stringify(rowJson));
			this.syncUserInfo();
		}
	}

	private async syncUserInfoToKeys(syncKeyInfo: ConfigData, userInfo: UserInfo): Promise<void> {
		const globalStorage = this.storageMainService.profileStorage(this.userDataProfilesService.defaultProfile);
		if (!globalStorage || !globalStorage.storage) {
			this.logService.error('Global storage not available');
			return;
		}

		const failures: string[] = [];

		for (const key of syncKeyInfo.syncKeys) {
			try {
				const rowData = globalStorage.storage.get(key, '{}');
				const rowJson = JSON.parse(rowData);
				rowJson[syncKeyInfo.mainKey] = userInfo;
				if (key === this.productService.joyCoderExtGlobalName && this.productService.joyCoderExtGlobalUserKey) {
					rowJson[this.productService.joyCoderExtGlobalUserKey] = userInfo;
				}
				globalStorage.storage.set(key, JSON.stringify(rowJson));
				this.logService.debug(`Successfully synced user info to key: ${key}`);
			} catch (error) {
				this.logService.error(`Failed to sync user info to key: ${key}`, error);
				failures.push(key);
			}
		}

		if (failures.length > 0) {
			this.logService.warn(`Failed to sync user info for ${failures.length} keys out of ${syncKeyInfo.syncKeys.length}`);
		}
	}

	async getKeyList(ptKey: string | ''): Promise<ConfigData> {
		try {
			const apiUrl = `${this.productService.joyCoderBaseUrl}/api/saas/config/v1/config/syncUserKey`;
			const context = await this.requestService.request({
				type: 'GET',
				url: apiUrl,
				headers: { 'ptKey': ptKey ? ptKey : '' }
			}, CancellationToken.None);

			if (!context.res.statusCode || context.res.statusCode !== 200) {
				this.logService.warn(`Failed to get key list: HTTP ${context.res.statusCode}`);
				return { mainKey: '', syncKeys: [] };
			}

			const result = await asJson(context) as CommonResult<ConfigData>;
			if (result.code !== 0 || !result.data) {
				this.logService.warn(`Invalid response from key list API: code=${result.code}, message=${result.msg}`);
				return { mainKey: '', syncKeys: [] };
			}

			return result.data;
		} catch (error) {
			this.logService.error('Error fetching key list:', error);
			return { mainKey: '', syncKeys: [] };
		}
	}


	override dispose(): void {
		if (this.intervalId) {
			clearInterval(this.intervalId);
			this.intervalId = null;
			this.logService.debug('User info sync interval stopped');
		}
		super.dispose();
	}
}
