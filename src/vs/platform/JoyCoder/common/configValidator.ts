/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import * as fs from 'original-fs';
import * as path from 'path';
import { ILogService } from '../../log/common/log.js';
import { IEnvironmentService } from '../../environment/common/environment.js';
import { IFileService } from '../../files/common/files.js';
import { IDialogMainService } from '../../dialogs/electron-main/dialogMainService.js';
import { localize } from '../../../nls.js';


/**
 * 验证用户配置文件是否有效
 * 如果配置文件损坏，则重命名配置目录并创建新的默认配置
 * 如果是第一次启动，则初始化配置目录，确保必要的目录和文件存在
 */
/**
 * 初始化配置目录
 * 在第一次启动时调用
 * 检查目录是否存在，如果不存在则创建
 */
async function initializeConfigDirs(logService: ILogService): Promise<void> {
    try {
        // 获取用户主目录路径
        const homePath = process.env.HOME || process.env.USERPROFILE || '';
        if (!homePath) {
            logService.error('[ConfigValidator] 无法获取用户主目录路径');
            return;
        }

        // 配置目录路径
        const joycoderPath = path.join(homePath, '.joycoder');
        const joycoderEditorPath = path.join(homePath, '.joycoder-editor');

        logService.info(`[ConfigValidator] 初始化配置目录: ${joycoderPath} 和 ${joycoderEditorPath}`);

        // 检查目录是否存在，如果不存在则创建
        if (!fs.existsSync(joycoderPath)) {
            logService.info(`[ConfigValidator] 创建目录: ${joycoderPath}`);
            await fs.promises.mkdir(joycoderPath, { recursive: true });
        } else {
            logService.info(`[ConfigValidator] 目录已存在: ${joycoderPath}`);
        }

        if (!fs.existsSync(joycoderEditorPath)) {
            logService.info(`[ConfigValidator] 创建目录: ${joycoderEditorPath}`);
            await fs.promises.mkdir(joycoderEditorPath, { recursive: true });

            // 创建必要的子目录
            const userDataProfilePath = path.join(joycoderEditorPath, 'User');
            await ensureDirectoryExists(userDataProfilePath, logService);

            const globalStoragePath = path.join(userDataProfilePath, 'globalStorage');
            await ensureDirectoryExists(globalStoragePath, logService);

            // 创建默认的 argv.json 文件
            const argvJsonPath = path.join(joycoderEditorPath, 'argv.json');
            await ensureValidArgvJson(argvJsonPath, logService);
        } else {
            logService.info(`[ConfigValidator] 目录已存在: ${joycoderEditorPath}`);

            // 确保必要的子目录存在
            const userDataProfilePath = path.join(joycoderEditorPath, 'User');
            await ensureDirectoryExists(userDataProfilePath, logService);

            const globalStoragePath = path.join(userDataProfilePath, 'globalStorage');
            await ensureDirectoryExists(globalStoragePath, logService);

            // 检查并修复 argv.json 文件
            const argvJsonPath = path.join(joycoderEditorPath, 'argv.json');
            await ensureValidArgvJson(argvJsonPath, logService);
        }

        logService.info('[ConfigValidator] 配置目录初始化完成');
    } catch (error) {
        logService.error('[ConfigValidator] 初始化配置目录时出错:', error);
    }
}

/**
 * 确保目录存在，如果不存在则创建
 */
async function ensureDirectoryExists(dirPath: string, logService: ILogService): Promise<void> {
    try {
        if (!fs.existsSync(dirPath)) {
            logService.info(`[ConfigValidator] 创建目录: ${dirPath}`);
            await fs.promises.mkdir(dirPath, { recursive: true });
        }
    } catch (error) {
        logService.error(`[ConfigValidator] 创建目录失败: ${dirPath}`, error);
        throw error;
    }
}

/**
 * 确保 argv.json 文件存在且格式正确
 * 如果文件格式不正确，则直接删除并创建新文件
 */
async function ensureValidArgvJson(filePath: string, logService: ILogService): Promise<void> {
    try {
        let needsCreate = false;

        // 检查文件是否存在
        if (fs.existsSync(filePath)) {
            try {
                // 尝试读取并解析现有文件
                const content = fs.readFileSync(filePath, 'utf8');
                JSON.parse(content); // 只是检查是否是有效的 JSON
                logService.info(`[ConfigValidator] 成功读取 argv.json: ${filePath}`);
            } catch (e) {
                // 文件存在但格式不正确，删除并重新创建
                logService.warn(`[ConfigValidator] argv.json 格式不正确，将删除并重新创建: ${e instanceof Error ? e.message : String(e)}`);
                try {
                    // 删除有问题的文件
                    fs.unlinkSync(filePath);
                    logService.info(`[ConfigValidator] 已删除损坏的 argv.json 文件: ${filePath}`);
                } catch (unlinkError) {
                    logService.error(`[ConfigValidator] 删除损坏的 argv.json 文件失败: ${filePath}`, unlinkError);
                }
                needsCreate = true;
            }
        } else {
            // 文件不存在，需要创建
            logService.info(`[ConfigValidator] argv.json 不存在，将创建新文件: ${filePath}`);
            needsCreate = true;
        }

        if (needsCreate) {
            // 创建默认的 argv.json 文件
            const defaultArgvJson = {
                'enable-crash-reporter': true,
                'crash-reporter-id': 'JoyCode',
                'use-inmemory-secretstorage': true
            };

            // 写入文件
            await fs.promises.writeFile(filePath, JSON.stringify(defaultArgvJson, null, 2), 'utf8');
            logService.info(`[ConfigValidator] 已创建新的 argv.json 文件: ${filePath}`);
        }
    } catch (error) {
        logService.error(`[ConfigValidator] 处理 argv.json 文件时出错: ${filePath}`, error);
    }
}

export async function validateUserConfiguration(
    _environmentService: IEnvironmentService,
    logService: ILogService,
    _fileService: IFileService,
    dialogMainService?: IDialogMainService,
    isFirstLaunch?: boolean
): Promise<boolean> {
    try {
        // 如果是第一次启动，初始化配置目录
        if (isFirstLaunch) {
            logService.info('[ConfigValidator] 检测到第一次启动，初始化配置目录');
            await initializeConfigDirs(logService);
            return true; // 返回 true 表示配置已初始化
        }

        // 获取用户主目录路径
        const homePath = process.env.HOME || process.env.USERPROFILE || '';

        // 检查 .joycoder 和 .joycoder-editor 目录
        const joycoderPath = path.join(homePath, '.joycoder');
        const joycoderEditorPath = path.join(homePath, '.joycoder-editor');

        logService.info(`[ConfigValidator] 检查用户配置目录: ${joycoderPath} 和 ${joycoderEditorPath}`);

        // 确保必要的目录存在
        await ensureDirectoryExists(joycoderPath, logService);
        await ensureDirectoryExists(joycoderEditorPath, logService);

        // 确保用户数据目录存在
        const userDataProfilePath = path.join(joycoderEditorPath, 'User');
        await ensureDirectoryExists(userDataProfilePath, logService);

        // 确保全局存储目录存在
        const globalStoragePath = path.join(userDataProfilePath, 'globalStorage');
        await ensureDirectoryExists(globalStoragePath, logService);

        // 检查并修复 argv.json 文件
        const argvJsonPath = path.join(joycoderEditorPath, 'argv.json');
        await ensureValidArgvJson(argvJsonPath, logService);

        logService.info('[ConfigValidator] 已确保必要的目录和文件存在');

        let configCorrupted = false;
        let errorMessage = '';

        // 验证配置文件
        try {
            // 尝试读取一些关键配置文件来验证它们是否有效
            if (fs.existsSync(joycoderPath)) {
                // 检查 .joycoder 目录中的关键文件
                const storageFilePath = path.join(joycoderPath, 'storage.json');
                if (fs.existsSync(storageFilePath)) {
                    try {
                        const content = fs.readFileSync(storageFilePath, 'utf8');
                        JSON.parse(content); // 尝试解析 JSON
                    } catch (e) {
                        configCorrupted = true;
                        errorMessage = `配置文件损坏: ${storageFilePath}`;
                        logService.error(`[ConfigValidator] ${errorMessage}`, e);
                    }
                }
            }

            if (fs.existsSync(joycoderEditorPath)) {
                // 检查 .joycoder-editor 目录中的关键文件
                const userDataProfilePath = path.join(joycoderEditorPath, 'User');
                if (fs.existsSync(userDataProfilePath)) {
                    const settingsPath = path.join(userDataProfilePath, 'settings.json');
                    if (fs.existsSync(settingsPath)) {
                        try {
                            const content = fs.readFileSync(settingsPath, 'utf8');
                            JSON.parse(content); // 尝试解析 JSON
                        } catch (e) {
                            configCorrupted = true;
                            errorMessage = `配置文件损坏: ${settingsPath}`;
                            logService.error(`[ConfigValidator] ${errorMessage}`, e);
                        }
                    }

                    const storageFilePath = path.join(userDataProfilePath, 'globalStorage', 'storage.json');
                    if (fs.existsSync(storageFilePath)) {
                        try {
                            const content = fs.readFileSync(storageFilePath, 'utf8');
                            JSON.parse(content); // 尝试解析 JSON
                        } catch (e) {
                            configCorrupted = true;
                            errorMessage = `配置文件损坏: ${storageFilePath}`;
                            logService.error(`[ConfigValidator] ${errorMessage}`, e);
                        }
                    }
                }
            }
        } catch (e) {
            configCorrupted = true;
            errorMessage = `验证配置文件时出错`;
            logService.error(`[ConfigValidator] ${errorMessage}`, e);
        }

        // 如果配置文件损坏，重命名配置目录并创建新的默认配置
        if (configCorrupted) {
            logService.warn(`[ConfigValidator] 检测到配置文件损坏，将重置配置`);

            // 重命名配置目录
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

            if (fs.existsSync(joycoderPath)) {
                const backupPath = `${joycoderPath}.backup-${timestamp}`;
                logService.info(`[ConfigValidator] 重命名 ${joycoderPath} 为 ${backupPath}`);
                await fs.promises.rename(joycoderPath, backupPath);
            }

            if (fs.existsSync(joycoderEditorPath)) {
                const backupPath = `${joycoderEditorPath}.backup-${timestamp}`;
                logService.info(`[ConfigValidator] 重命名 ${joycoderEditorPath} 为 ${backupPath}`);
                await fs.promises.rename(joycoderEditorPath, backupPath);
            }

            // 如果提供了对话框服务，显示通知
            if (dialogMainService) {
                dialogMainService.showMessageBox({
                    type: 'info',
                    title: localize('configCorruptedTitle', "JoyCode 配置已重置"),
                    message: localize('configCorruptedMessage', "JoyCode 检测到配置文件损坏，已自动重置配置。\n\n原配置已备份到：\n{0}.backup-{1}\n{2}.backup-{1}", joycoderPath, timestamp, joycoderEditorPath),
                    buttons: [localize('ok', "确定")]
                });
            }

            return true; // 配置已重置
        }

        return false; // 配置正常
    } catch (error) {
        logService.error('[ConfigValidator] 验证用户配置时出错:', error);
        return false;
    }
}
