/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ILoginMainService, ILoginInfo } from "../common/login.js";
// import { IStorageMainService } from "../../../platform/storage/electron-main/storageMainService.js";
import { ILogService } from "../../../platform/log/common/log.js";
import { IJoyCoderUserService } from "../../../platform/user/joyCoderUserService.js";
import { IProductService } from "../../../platform/product/common/productService.js";
import { shell } from "electron";
import { HttpAuthService } from "../common/httpAuthService.js";
import { Emitter } from "../../../base/common/event.js";
// import { URI } from "../../../base/common/uri.js";
import * as https from 'https';
import * as http from 'http';
import { URL } from 'url';
import { UserInfo } from '../../../platform/user/joyCoderUserService.js';
// import { exec, spawn } from 'child_process';
// import { INativeHostMainService } from '../../native/electron-main/nativeHostMainService.js';

/**
 * 从pt_key获取用户信息
 * @param ptKey 用户登录凭证
 * @param loginType 登录类型，默认为PIN
 * @returns 用户信息或null
 */
export async function fetchUserInfo(ptKey: string, loginType: string = 'PIN', productService: IProductService, logService: ILogService): Promise<any | null> {
	return new Promise<any | null>((resolve) => {
		try {
			// 设置请求URL
			const apiUrl = `${productService.joyCoderBaseUrl}/api/saas/user/v1/userInfo`;
			const parsedUrl = new URL(apiUrl);
			// 请求选项
			const options = {
				hostname: parsedUrl.hostname,
				port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
				path: parsedUrl.pathname + parsedUrl.search,
				method: 'GET',
				headers: {
					'ptKey': ptKey,
					'loginType': loginType
				}
			};

			// 选择http或https模块
			const requestModule = parsedUrl.protocol === 'https:' ? https : http;

			// 发送请求
			const req = requestModule.request(options, (res) => {
				let data = '';

				// 接收数据
				res.on('data', (chunk) => {
					data += chunk;
				});

				// 请求完成
				res.on('end', () => {
					// 检查状态码
					if (res.statusCode !== 200) {
						logService.error('获取用户信息失败，HTTP状态码:', res.statusCode);
						logService.error('登录发生错误！请求参数:', options, '返回结果：', res.statusCode);
						resolve('获取用户信息失败，HTTP状态码:' + res.statusCode);
						return;
					}

					try {
						// 解析响应数据
						const result = JSON.parse(data);

						// 检查响应状态
						if (result.code !== 0) {
							logService.error('获取用户信息失败，错误码:', result.code, '错误信息:', result.msg);
							logService.error('登录发生错误！请求参数:', options, '返回结果：', result);
							resolve('获取用户信息失败，错误码:' + result.code + '错误信息:' + result.msg);
							return;
						}

						// 检查数据是否存在
						if (!result.data) {
							logService.error('获取用户信息失败，返回数据为空');
							logService.error('登录发生错误！请求参数:', options, '返回结果：', result);
							resolve('获取用户信息失败，返回数据为空');
							return;
						}

						resolve(result.data);
					} catch (error) {
						logService.error('解析用户信息响应失败:', error);
						logService.error('登录发生错误！请求参数:', options, '返回结果：', error);
						resolve('解析用户信息响应失败');
					}
				});
			});

			// 处理请求错误
			req.on('error', (error) => {
				logService.error('获取用户信息请求错误:', error);
				logService.error('登录发生错误！请求参数:', options, '返回结果：', error);
				resolve('获取用户信息请求错误');
			});

			// 结束请求
			req.end();
		} catch (error) {
			logService.error('获取用户信息过程中出错:', error);
			resolve('获取用户信息过程中出错');
		}
	});
}

export class LoginMainService implements ILoginMainService {
	declare readonly _serviceBrand: undefined;

	// private static readonly LOGIN_URL = 'https://joycode.jd.com/login?ideAppName=JoyCode&fromIde=ide';
	// private static readonly LOGIN_URL = 'https://pre-joycoder.jd.com/login?ideAppName=JoyCode&fromIde=ide';


	private readonly _onDidChangeLoginStatus = new Emitter<boolean>();
	public readonly onDidChangeLoginStatus = this._onDidChangeLoginStatus.event;

	private httpAuthService: HttpAuthService;

	constructor(
		// @IStorageMainService private readonly storageMainService: IStorageMainService,
		@ILogService private readonly logService: ILogService,
		@IJoyCoderUserService private readonly joyCoderUserService: IJoyCoderUserService,
		@IProductService private readonly productService: IProductService
	) {
		this.httpAuthService = HttpAuthService.getInstance();
		this.httpAuthService.setLoginMainService(this);
	}

	public async login(): Promise<boolean> {
		try {
			const LOGIN_URL = this.productService?.joyCoderLoginUrl || '';
			// 启动HTTP认证服务
			const authInfo = await this.httpAuthService.startServer();

			// 构建带认证信息的登录URL
			const loginUrl = new URL(LOGIN_URL);
			loginUrl.searchParams.append('authPort', authInfo.port.toString());
			loginUrl.searchParams.append('authKey', authInfo.authKey);

			this.logService.info(`即将跳转到登录或者授权页面，地址：`, loginUrl.toString());
			await shell.openExternal(loginUrl.toString());
			return true;
		} catch (error) {
			this.logService.error("Failed to open login page:", error);
			return false;
		}
	}

	public async handleLoginCallback(pt_key: string): Promise<boolean | string> {
		try {
			const ptKey = pt_key;
			if (!ptKey) {
				return "pt_key错误，请重新登录";
			}

			// 获取用户信息
			const fetchDataInfo = await fetchUserInfo(ptKey, 'PIN', this.productService, this.logService);
			if (!fetchDataInfo?.pk) {
				return fetchDataInfo;
			}

			const userInfo: UserInfo = {
				ptKey: ptKey,
				userName: fetchDataInfo.userId,
				userId: fetchDataInfo.userId,
				loginType: fetchDataInfo.loginType,
				loginUrl: fetchDataInfo.loginUrl,
				pk: fetchDataInfo.pk,
			};

			this.logService.info('userInfo', userInfo);

			// 将登录信息存储到全局存储中
			this.joyCoderUserService.writeMainUserInfo(userInfo);

			this._onDidChangeLoginStatus.fire(true);
			return true;
		} catch (error) {
			this.logService.error('Failed to handle login callback:', error);
			return "登录发生错误";
		}
	}



	public async getLoginInfo(): Promise<ILoginInfo | null> {
		// 从joyCoderUserService获取用户信息而不是applicationStorage
		const userInfo = this.joyCoderUserService.getUserInfo();

		if (!userInfo || !userInfo.ptKey) {
			return null;
		}

		try {
			// 将UserInfo转换为ILoginInfo
			return {
				pt_key: userInfo.ptKey,
				userName: userInfo.userName,
				userId: userInfo.userId
			} as ILoginInfo;
		} catch (error) {
			this.logService.error('Failed to process login info:', error);
			return null;
		}
	}

	public async isLoggedIn(): Promise<boolean> {
		const loginInfo = await this.getLoginInfo();
		return !!loginInfo && !!loginInfo.pt_key;
	}

	public async logout(): Promise<boolean> {
		try {
			// 使用joyCoderUserService写入空的用户信息
			this.joyCoderUserService.writeMainUserInfo(null);

			// 触发登录状态变更事件
			this._onDidChangeLoginStatus.fire(false);
			return true;
		} catch (error) {
			this.logService.error('退出登录失败:', error);
			return false;
		}
	}
}

// 不再使用registerSingleton，改为手动注册
