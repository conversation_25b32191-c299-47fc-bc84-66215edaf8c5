/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ILoginService, ILoginInfo } from '../common/login.js';
import { Emitter } from '../../../base/common/event.js';
import { InstantiationType, registerSingleton } from '../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../base/common/lifecycle.js';
import { IMainProcessService } from '../../../platform/ipc/common/mainProcessService.js';
import { LoginChannelClient } from '../common/loginIpc.js';

export class LoginService extends Disposable implements ILoginService {
	declare readonly _serviceBrand: undefined;

	private readonly _onDidChangeLoginStatus = this._register(new Emitter<boolean>());
	public readonly onDidChangeLoginStatus = this._onDidChangeLoginStatus.event;

	private readonly loginMainService: LoginChannelClient;
	public loginStatus: boolean | null = null;

	constructor(
		@IMainProcessService mainProcessService: IMainProcessService
	) {
		super();

		const channel = mainProcessService.getChannel('login');
		this.loginMainService = new LoginChannelClient(channel);

		// 转发登录状态变化事件
		this._register(this.loginMainService.onDidChangeLoginStatus(status => {
			if (this.loginStatus !== status) {
				this._onDidChangeLoginStatus.fire(status);
			}
			this.loginStatus = status;
		}));
	}

	async login(): Promise<boolean> {
		return this.loginMainService.login();
	}

	async isLoggedIn(): Promise<boolean> {
		return this.loginMainService.isLoggedIn();
	}

	async getLoginInfo(): Promise<ILoginInfo | null> {
		return this.loginMainService.getLoginInfo();
	}

	async logout(): Promise<boolean> {
		return this.loginMainService.logout();
	}
}

// 注册服务
// 使用 Eager 模式确保服务在应用启动时就被初始化
registerSingleton(ILoginService, LoginService, InstantiationType.Eager);
