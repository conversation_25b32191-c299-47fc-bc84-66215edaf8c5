/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { createDecorator } from '../../../platform/instantiation/common/instantiation.js';
import { Event } from '../../../base/common/event.js';

export const ILoginService = createDecorator<ILoginService>('loginService');
export const ILoginMainService = createDecorator<ILoginMainService>('loginMainService');

export interface ILoginInfo {
	pt_key: string;
	[key: string]: any;
}

export interface ILoginService {
	readonly _serviceBrand: undefined;

	/**
	 * 登录状态变化事件
	 */
	readonly onDidChangeLoginStatus: Event<boolean>;

	/**
	 * 登录方法，打开系统默认浏览器进行登录
	 * @returns Promise<boolean> 登录是否成功启动
	 */
	login(): Promise<boolean>;

	/**
	 * 获取当前登录状态
	 * @returns Promise<boolean> 是否已登录
	 */
	isLoggedIn(): Promise<boolean>;

	/**
	 * 获取登录信息
	 * @returns Promise<ILoginInfo | null> 登录信息对象
	 */
	getLoginInfo(): Promise<ILoginInfo | null>;

	/**
	 * 退出登录
	 * @returns Promise<boolean> 是否成功退出
	 */
	logout(): Promise<boolean>;
}

export interface ILoginMainService {
	readonly _serviceBrand: undefined;

	/**
	 * 登录状态变化事件
	 */
	readonly onDidChangeLoginStatus: Event<boolean>;

	/**
	 * 登录方法，打开系统默认浏览器进行登录
	 * @returns Promise<boolean> 登录是否成功启动
	 */
	login(): Promise<boolean>;

	/**
	 * 处理登录回调
	 * @param pt_key 登录信息对象
	 * @returns Promise<boolean> 处理是否成功
	 */
	handleLoginCallback(pt_key: string): Promise<boolean | string>;

	/**
	 * 获取当前登录状态
	 * @returns Promise<boolean> 是否已登录
	 */
	isLoggedIn(): Promise<boolean>;

	/**
	 * 获取登录信息
	 * @returns Promise<ILoginInfo | null> 登录信息对象
	 */
	getLoginInfo(): Promise<ILoginInfo | null>;

	/**
	 * 退出登录
	 * @returns Promise<boolean> 是否成功退出
	 */
	logout(): Promise<boolean>;
}
