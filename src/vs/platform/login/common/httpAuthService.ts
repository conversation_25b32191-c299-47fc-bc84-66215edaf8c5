/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { createServer, Server, IncomingMessage, ServerResponse } from 'http';
import { randomBytes } from 'crypto';
import { BrowserWindow, app } from "electron";
import { ILoginMainService } from './login.js';
import { URL } from 'url';

export interface HttpAuthInfo {
	port: number;
	authKey: string;
}

export class HttpAuthService {
	private static instance: HttpAuthService;
	private server: Server | null = null;
	private currentAuthInfo: HttpAuthInfo | null = null;
	private timeout: NodeJS.Timeout | null = null;
	private loginMainService: ILoginMainService | null = null;

	private constructor() { }

	public static getInstance(): HttpAuthService {
		if (!HttpAuthService.instance) {
			HttpAuthService.instance = new HttpAuthService();
		}
		return HttpAuthService.instance;
	}

	public setLoginMainService(service: ILoginMainService): void {
		this.loginMainService = service;
	}

	private async findAvailablePort(startPort: number): Promise<number> {
		const maxPort = 65535;
		for (let port = startPort; port <= maxPort; port++) {
			try {
				const server = createServer();
				await new Promise((resolve, reject) => {
					server.once('error', reject);
					server.once('listening', () => {
						server.close();
						resolve(port);
					});
					server.listen(port);
				});
				return port;
			} catch (err) {
				continue;
			}
		}
		throw new Error('No available ports found');
	}

	private generateAuthKey(): string {
		return randomBytes(16).toString('hex');
	}

	private parseQueryParams(url: string): { [key: string]: string } {
		const queryParams: { [key: string]: string } = {};
		const urlObj = new URL(url, 'http://localhost');
		urlObj.searchParams.forEach((value, key) => {
			queryParams[key] = value;
		});
		return queryParams;
	}

	private sendResponse(res: ServerResponse, statusCode: number, data: any): void {
		res.writeHead(statusCode, {
			'Content-Type': 'application/json',
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type'
		});
		if (statusCode === 307) {
			res.end(JSON.stringify(data));
		} else {
			res.end();
		}

	}

	private redirectResponse(res: ServerResponse, location: string, redirect: string, message?: string): void {
		const urlObj = new URL(location);
		urlObj.searchParams.set('redirect', redirect);
		message && urlObj.searchParams.set('message', message);
		const newCallbackUrl = urlObj.toString();
		// console.log('redirectResponse----url', newCallbackUrl);

		res.writeHead(307, {
			'Location': newCallbackUrl,
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type'
		});
		res.end();
	}

	public async startServer(): Promise<HttpAuthInfo> {
		if (this.loginMainService?.logout) {
			this.loginMainService?.logout();
		}
		if (this.server) {
			return this.currentAuthInfo!;
		}

		const port = await this.findAvailablePort(10000 + Math.floor(Math.random() * 50000));
		const authKey = this.generateAuthKey();

		this.server = createServer(async (req: IncomingMessage, res: ServerResponse) => {
			// 处理 CORS 预检请求
			if (req.method === 'OPTIONS') {
				res.writeHead(204, {
					'Access-Control-Allow-Origin': '*',
					'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
					'Access-Control-Allow-Headers': 'Content-Type'
				});
				res.end();
				return;
			}

			if (req.method === 'GET' && req.url) {
				const queryParams = this.parseQueryParams(req.url);
				const { authPort, pt_key, ideAppName, fromIde, callbackUrl } = queryParams;
				const receivedKey = queryParams.authKey;

				if (receivedKey === authKey && Number(authPort) === Number(port)) {
					console.log('Received parameters:', queryParams);

					// 处理登录回调
					if (this.loginMainService && pt_key && ideAppName === "JoyCode" && fromIde === "ide") {
						try {
							const login_status = await this.loginMainService.handleLoginCallback(pt_key || '');
							if (typeof login_status === 'boolean' && login_status) {
								// 获取当前窗口并设置置顶
								// const win = BrowserWindow?.getFocusedWindow();
								// if (win) {
								// 	// 确保窗口未被最小化
								// 	if (win.isMinimized()) {
								// 		win.restore();
								// 	}

								// 	// 显示窗口
								// 	win.show();
								// 	// 设置窗口置顶
								// 	win.setAlwaysOnTop(true);
								// 	// 获取焦点
								// 	win.focus();

								// 	// 如果是 Mac 系统，使用特殊方式获取焦点
								// 	if (process.platform === 'darwin') {
								// 		app.focus({ steal: true });
								// 	}

								// 	// 短暂延时后取消置顶，避免一直保持在最上层
								// 	setTimeout(() => {
								// 		win.setAlwaysOnTop(false);
								// 	}, 1000);
								// } else {
								// 如果没有获得焦点的窗口，尝试获取最后一个活动窗口
								const lastActiveWindow = BrowserWindow.getAllWindows()[0] || null;
								// const lastActiveWindow = BrowserWindow.getAllWindows().find(w => !w.isMinimized());
								if (lastActiveWindow) {
									// 确保窗口未被最小化
									if (lastActiveWindow.isMinimized()) {
										lastActiveWindow.restore();
									}
									lastActiveWindow.show();

									// 关键：加一点延时再置顶和 focus
									setTimeout(() => {
										lastActiveWindow.setAlwaysOnTop(true);
										lastActiveWindow.focus();
										if (process.platform === 'darwin') {
											app.focus({ steal: true });
										}
										setTimeout(() => {
											lastActiveWindow.setAlwaysOnTop(false);
										}, 1000);
									}, 100); // 100ms 延时
								}
								// }


								// this.sendResponse(res, 200, { success: true, message: '认证成功' });
								this.redirectResponse(res, callbackUrl, '1', '认证成功');
								console.log('认证成功，即将关闭认证服务器');
								this.stopServer();
							} else {
								console.log('ide登录报错');
								this.redirectResponse(res, callbackUrl, '2', 'ide处理登录信息失败，请联系管理员');
								// this.sendResponse(res, 500, { success: false, message: login_status });
							}

						} catch (error) {
							console.error('Failed to handle login callback:', error);
							this.redirectResponse(res, callbackUrl, '2', '处理登录信息失败，请联系管理员');
						}
					} else {
						this.redirectResponse(res, callbackUrl, '2', '缺少登录信息，请重新从ide登录');
					}
				} else {
					this.redirectResponse(res, callbackUrl, '2', '与ide认证失败，请重新从ide登录');
				}
			} else {
				this.sendResponse(res, 405, { success: false, message: '方法不允许，请重新从ide登录' });
			}
		});

		this.server.listen(port);
		this.currentAuthInfo = { port, authKey };

		// 设置10分钟超时
		this.timeout = setTimeout(() => {
			this.stopServer();
		}, 10 * 60 * 1000);

		return this.currentAuthInfo;
	}

	public stopServer(): void {
		if (this.server) {
			this.server.close();
			this.server = null;
			this.currentAuthInfo = null;
			if (this.timeout) {
				clearTimeout(this.timeout);
				this.timeout = null;
			}
		}
	}

	public getCurrentAuthInfo(): HttpAuthInfo | null {
		return this.currentAuthInfo;
	}
}
