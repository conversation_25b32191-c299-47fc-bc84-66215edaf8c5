/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Event } from '../../../base/common/event.js';
import { IChannel, IServerChannel } from '../../../base/parts/ipc/common/ipc.js';
import { ILoginInfo, ILoginMainService } from './login.js';

export class LoginChannel implements IServerChannel {
	constructor(private readonly service: ILoginMainService) { }

	listen(_: unknown, event: string): Event<any> {
		switch (event) {
			case 'onDidChangeLoginStatus': return this.service.onDidChangeLoginStatus;
			default: throw new Error(`未知事件: ${event}`);
		}
	}

	call(_: unknown, command: string, arg?: any): Promise<any> {
		switch (command) {
			case 'login': return this.service.login();
			case 'handleLoginCallback': return this.service.handleLoginCallback(arg);
			case 'isLoggedIn': return this.service.isLoggedIn();
			case 'getLoginInfo': return this.service.getLoginInfo();
			case 'logout': return this.service.logout();
			default: throw new Error(`未知方法: ${command}`);
		}
	}
}

export class LoginChannelClient implements ILoginMainService {
	declare readonly _serviceBrand: undefined;

	private readonly _onDidChangeLoginStatus: Event<boolean>;

	constructor(private readonly channel: IChannel) {
		this._onDidChangeLoginStatus = this.channel.listen('onDidChangeLoginStatus');
	}

	get onDidChangeLoginStatus(): Event<boolean> {
		return this._onDidChangeLoginStatus;
	}

	async login(): Promise<boolean> {
		return this.channel.call('login');
	}

	async handleLoginCallback(pt_key: string): Promise<boolean | string> {
		return this.channel.call('handleLoginCallback', pt_key);
	}

	async isLoggedIn(): Promise<boolean> {
		return this.channel.call('isLoggedIn');
	}

	async getLoginInfo(): Promise<ILoginInfo | null> {
		return this.channel.call('getLoginInfo');
	}

	async logout(): Promise<boolean> {
		return this.channel.call('logout');
	}
}
