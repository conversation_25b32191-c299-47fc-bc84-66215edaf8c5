# JoyCode IDE 登录功能实现技术文档

## 一、功能概述

实现完整的登录服务体系，允许其他模块通过调用该服务的方法，使系统默认浏览器打开指定的登录网页，并在用户完成登录后，通过自定义协议`joycoder://`回调 IDE 应用，将登录信息存储到 globalStorage 中。该功能同时支持插件通过命令调用登录相关方法。

## 二、技术方案

### 2.1 核心组件

1. **主进程登录服务**：实现 ILoginMainService 接口，提供 login、isLoggedIn、getLoginInfo 等方法
2. **浏览器进程登录服务**：实现 ILoginService 接口，转发主进程的登录服务功能到浏览器进程
3. **协议处理器**：在 Electron 主进程中处理自定义协议`joycoder://`的回调
4. **命令注册**：注册登录相关命令供渲染进程和插件调用
5. **登录状态跟踪器**：监控和更新登录状态并绑定到上下文键
6. **登录状态服务**：作为服务封装登录状态跟踪器，提供给其他组件使用
7. **工作台贡献**：负责初始化和注册登录相关命令和服务
8. **存储机制**：通过 IJoyCoderUserService 存储登录状态，方法为：storageMainService.profileStorage 存储到 globalStorage 中

### 2.2 目录结构

```
src/vs/platform/login/
├── common/
│   ├── login.ts                  # 通用接口定义
│   └── loginIpc.ts               # IPC通道定义
├── browser/
│   └── loginService.ts           # 浏览器进程登录服务实现
├── electron-main/
│   ├── loginMainService.ts       # 主进程登录服务实现
│   ├── loginMainServiceRegistration.ts  # 主进程服务注册
│   └── loginProtocolHandler.ts   # 协议处理器
└── 登录功能实现文档.md           # 本文档

src/vs/workbench/contrib/JoyCode/browser/
├── loginAction.ts                # 登录Action和状态跟踪器定义
├── loginCommands.ts              # 登录命令注册
├── loginStatusService.ts         # 登录状态服务实现
└── loginContribution.ts          # 登录工作台贡献
```

## 三、详细设计

### 3.1 登录服务接口

```typescript
// src/vs/platform/login/common/login.ts

import { createDecorator } from "../../../platform/instantiation/common/instantiation";
import { Event } from "../../../base/common/event";

export interface ILoginInfo {
	userName: string;
	pt_key: string;
	[key: string]: any;
}

// 浏览器进程登录服务接口
export interface ILoginService {
	readonly _serviceBrand: undefined;

	/**
	 * 登录状态变化事件
	 */
	readonly onDidChangeLoginStatus: Event<boolean>;

	/**
	 * 登录方法，打开系统默认浏览器进行登录
	 * @returns Promise<boolean> 登录是否成功启动
	 */
	login(): Promise<boolean>;

	/**
	 * 获取当前登录状态
	 * @returns Promise<boolean> 是否已登录
	 */
	isLoggedIn(): Promise<boolean>;

	/**
	 * 获取登录信息
	 * @returns Promise<ILoginInfo | null> 登录信息对象
	 */
	getLoginInfo(): Promise<ILoginInfo | null>;

	/**
	 * 退出登录
	 * @returns Promise<boolean> 是否成功退出
	 */
	logout(): Promise<boolean>;
}

// 主进程登录服务接口
export interface ILoginMainService {
	readonly _serviceBrand: undefined;

	/**
	 * 登录状态变化事件
	 */
	readonly onDidChangeLoginStatus: Event<boolean>;

	/**
	 * 登录方法，打开系统默认浏览器进行登录
	 * @returns Promise<boolean> 登录是否成功启动
	 */
	login(): Promise<boolean>;

	/**
	 * 处理登录回调
	 * @param url 回调URL
	 * @returns Promise<boolean> 处理是否成功
	 */
	handleLoginCallback(url: string): Promise<boolean>;

	/**
	 * 获取当前登录状态
	 * @returns Promise<boolean> 是否已登录
	 */
	isLoggedIn(): Promise<boolean>;

	/**
	 * 获取登录信息
	 * @returns Promise<ILoginInfo | null> 登录信息对象
	 */
	getLoginInfo(): Promise<ILoginInfo | null>;

	/**
	 * 退出登录
	 * @returns Promise<boolean> 是否成功退出
	 */
	logout(): Promise<boolean>;
}

export const ILoginService = createDecorator<ILoginService>("loginService");
export const ILoginMainService =
	createDecorator<ILoginMainService>("loginMainService");
```

### 3.2 IPC 通道定义

```typescript
// src/vs/platform/login/common/loginIpc.ts

import { IChannel } from "../../../base/parts/ipc/common/ipc";
import { ILoginInfo, ILoginMainService } from "./login";
import { Event } from "../../../base/common/event";
import { Emitter } from "../../../base/common/event";

export class LoginChannel implements IChannel {
	constructor(private service: ILoginMainService) {}

	listen(_: string, event: string): Event<any> {
		switch (event) {
			case "onDidChangeLoginStatus":
				return this.service.onDidChangeLoginStatus;
			default:
				throw new Error(`Event not found: ${event}`);
		}
	}

	call(context: any, command: string, arg?: any): Promise<any> {
		switch (command) {
			case "login":
				return this.service.login();
			case "isLoggedIn":
				return this.service.isLoggedIn();
			case "getLoginInfo":
				return this.service.getLoginInfo();
			case "logout":
				return this.service.logout();
			default:
				throw new Error(`Command not found: ${command}`);
		}
	}
}

export class LoginChannelClient implements ILoginMainService {
	readonly _serviceBrand: undefined;

	private readonly _onDidChangeLoginStatus = new Emitter<boolean>();
	readonly onDidChangeLoginStatus = this._onDidChangeLoginStatus.event;

	constructor(private readonly channel: IChannel) {
		this.channel.listen(
			"onDidChangeLoginStatus",
			"onDidChangeLoginStatus"
		)((loginStatus) => {
			this._onDidChangeLoginStatus.fire(loginStatus);
		});
	}

	async login(): Promise<boolean> {
		return this.channel.call("login");
	}

	async handleLoginCallback(url: string): Promise<boolean> {
		return this.channel.call("handleLoginCallback", url);
	}

	async isLoggedIn(): Promise<boolean> {
		return this.channel.call("isLoggedIn");
	}

	async getLoginInfo(): Promise<ILoginInfo | null> {
		return this.channel.call("getLoginInfo");
	}

	async logout(): Promise<boolean> {
		return this.channel.call("logout");
	}
}
```

### 3.3 主进程登录服务实现

```typescript
// src/vs/platform/login/electron-main/loginMainService.ts

import { ILoginMainService, ILoginInfo } from "../common/login";
import { IStorageMainService } from "../../../platform/storage/electron-main/storageMainService";
import { URI } from "../../../base/common/uri";
import { shell } from "electron";
import { Emitter } from "../../../base/common/event";
import { ILogService } from "../../../platform/log/common/log";
import {
	InstantiationType,
	registerSingleton,
} from "../../../platform/instantiation/common/extensions";

export class LoginMainService implements ILoginMainService {
	declare readonly _serviceBrand: undefined;

	private static readonly LOGIN_URL =
		"https://utest.jr.jd.com/JoyCode-home/login?source=joyCoderFe&ideAppName=JoyCode";
	private static readonly LOGIN_INFO_STORAGE_KEY = "joycoder.info";

	private readonly _onDidChangeLoginStatus = new Emitter<boolean>();
	public readonly onDidChangeLoginStatus = this._onDidChangeLoginStatus.event;

	constructor(
		@IStorageMainService
		private readonly storageMainService: IStorageMainService,
		@ILogService private readonly logService: ILogService
	) {}

	public async login(): Promise<boolean> {
		try {
			await shell.openExternal(LoginMainService.LOGIN_URL);
			return true;
		} catch (error) {
			this.logService.error("Failed to open login page:", error);
			return false;
		}
	}

	public async handleLoginCallback(url: string): Promise<boolean> {
		try {
			const uri = URI.parse(url);
			if (uri.scheme !== "joycoder") {
				return false;
			}

			const query = uri.query;
			const params = new URLSearchParams(query);
			const infoParam = params.get("info");

			if (!infoParam) {
				return false;
			}

			const loginInfo = JSON.parse(decodeURIComponent(infoParam)) as ILoginInfo;

			// 将登录信息存储到全局存储中
			await this.storageMainService.applicationStorage.setValue(
				LoginMainService.LOGIN_INFO_STORAGE_KEY,
				JSON.stringify(loginInfo)
			);

			this._onDidChangeLoginStatus.fire(true);
			return true;
		} catch (error) {
			this.logService.error("Failed to handle login callback:", error);
			return false;
		}
	}

	public async getLoginInfo(): Promise<ILoginInfo | null> {
		const loginInfoStr = this.storageMainService.applicationStorage.getValue(
			LoginMainService.LOGIN_INFO_STORAGE_KEY
		);

		if (!loginInfoStr) {
			return null;
		}

		try {
			return JSON.parse(loginInfoStr) as ILoginInfo;
		} catch (error) {
			this.logService.error("Failed to parse login info:", error);
			return null;
		}
	}

	public async isLoggedIn(): Promise<boolean> {
		const loginInfo = await this.getLoginInfo();
		return !!loginInfo && !!loginInfo.pt_key;
	}

	public async logout(): Promise<boolean> {
		try {
			// 清除登录信息
			await this.storageMainService.applicationStorage.setValue(
				LoginMainService.LOGIN_INFO_STORAGE_KEY,
				""
			);

			this._onDidChangeLoginStatus.fire(false);
			return true;
		} catch (error) {
			this.logService.error("Failed to logout:", error);
			return false;
		}
	}
}

// 注册服务
registerSingleton(
	ILoginMainService,
	LoginMainService,
	InstantiationType.Delayed
);
```

### 3.4 主进程服务注册

```typescript
// src/vs/platform/login/electron-main/loginMainServiceRegistration.ts

import { IMainProcessService } from "vs/platform/ipc/electron-main/services";
import { ILogService } from "vs/platform/log/common/log";
import { ILifecycleMainService } from "vs/platform/lifecycle/electron-main/lifecycleMainService";
import { LoginChannel } from "../common/loginIpc";
import { LoginMainService } from "./loginMainService";
import { LoginProtocolHandler } from "./loginProtocolHandler";
import { IJoyCoderUserService } from "vs/platform/user/joyCoderUserService";

export function registerLoginMainService(
	mainProcessServer: IMainProcessService,
	logService: ILogService,
	lifecycleMainService: ILifecycleMainService,
	joyCoderUserService: IJoyCoderUserService
) {
	// 1. 创建登录服务实例
	const loginMainService = new LoginMainService(joyCoderUserService);

	// 2. 注册IPC通道
	const loginChannel = new LoginChannel(loginMainService);
	mainProcessServer.registerChannel("login", loginChannel);

	// 3. 创建协议处理器
	const loginProtocolHandler = new LoginProtocolHandler(
		loginMainService,
		logService
	);

	// 4. 生命周期管理
	lifecycleMainService.onWillShutdown(() => {
		loginProtocolHandler.dispose();
	});
}
```

### 3.5 协议处理实现

```typescript
// src/vs/platform/login/electron-main/loginProtocolHandler.ts

import { app } from "electron";
import { ILoginMainService } from "../common/login";
import { ILogService } from "../../../platform/log/common/log";
import { Disposable } from "../../../base/common/lifecycle";

export class LoginProtocolHandler extends Disposable {
	private static readonly PROTOCOL_SCHEME = "joycoder";

	constructor(
		@ILoginMainService private readonly loginService: ILoginMainService,
		@ILogService private readonly logService: ILogService
	) {
		super();
		this.registerProtocolHandler();
	}

	private registerProtocolHandler(): void {
		// 设置为应用的默认协议处理程序
		if (!app.isDefaultProtocolClient(LoginProtocolHandler.PROTOCOL_SCHEME)) {
			const success = app.setAsDefaultProtocolClient(
				LoginProtocolHandler.PROTOCOL_SCHEME
			);
			if (!success) {
				this.logService.warn(
					"Failed to register protocol handler for:",
					LoginProtocolHandler.PROTOCOL_SCHEME
				);
			}
		}

		// 处理Windows/Linux系统启动参数
		const gotTheLock = app.requestSingleInstanceLock();
		if (!gotTheLock) {
			app.quit();
			return;
		}

		// 监听second-instance事件
		const secondInstanceHandler = (_event: any, commandLine: string[]) => {
			const protocolUrl = commandLine.find((arg) =>
				arg.startsWith(`${LoginProtocolHandler.PROTOCOL_SCHEME}://`)
			);
			if (protocolUrl) {
				this.loginService.handleLoginCallback(protocolUrl);
			}
		};

		app.on("second-instance", secondInstanceHandler);

		// 处理macOS协议打开
		const openUrlHandler = (event: any, url: string) => {
			event.preventDefault();
			if (url.startsWith(`${LoginProtocolHandler.PROTOCOL_SCHEME}://`)) {
				this.loginService.handleLoginCallback(url);
			}
		};

		app.on("open-url", openUrlHandler);

		// 处理可能已经收到的参数
		const args = process.argv;
		const protocolUrl = args.find((arg) =>
			arg.startsWith(`${LoginProtocolHandler.PROTOCOL_SCHEME}://`)
		);
		if (protocolUrl) {
			this.loginService.handleLoginCallback(protocolUrl);
		}
	}
}
```

### 3.6 浏览器进程登录服务实现

```typescript
// src/vs/platform/login/browser/loginService.ts

import { ILoginService, ILoginInfo } from "../common/login";
import { Event, Emitter } from "../../../base/common/event";
import { registerSingleton } from "../../../platform/instantiation/common/extensions";
import { IMainProcessService } from "../../../platform/ipc/common/services";
import { Disposable } from "../../../base/common/lifecycle";
import { LoginChannelClient } from "../common/loginIpc";

export class LoginService extends Disposable implements ILoginService {
	declare readonly _serviceBrand: undefined;

	private readonly _onDidChangeLoginStatus = new Emitter<boolean>();
	readonly onDidChangeLoginStatus = this._onDidChangeLoginStatus.event;

	private readonly loginMainService: LoginChannelClient;

	constructor(@IMainProcessService mainProcessService: IMainProcessService) {
		super();

		const channel = mainProcessService.getChannel("login");
		this.loginMainService = new LoginChannelClient(channel);

		// 转发登录状态变化事件
		this._register(
			this.loginMainService.onDidChangeLoginStatus((status) => {
				this._onDidChangeLoginStatus.fire(status);
			})
		);
	}

	async login(): Promise<boolean> {
		return this.loginMainService.login();
	}

	async isLoggedIn(): Promise<boolean> {
		return this.loginMainService.isLoggedIn();
	}

	async getLoginInfo(): Promise<ILoginInfo | null> {
		return this.loginMainService.getLoginInfo();
	}

	async logout(): Promise<boolean> {
		return this.loginMainService.logout();
	}
}

// 注册服务
registerSingleton(ILoginService, LoginService, true);
```

### 3.7 命令注册

```typescript
// src/vs/workbench/contrib/JoyCode/browser/loginCommands.ts

import { CommandsRegistry } from "../../../../platform/commands/common/commands.js";
import { ILoginService } from "../../../../platform/login/common/login.js";
import { ServicesAccessor } from "../../../../platform/instantiation/common/instantiation.js";
import {
	LOGIN_ACTION_ID,
	IS_LOGGED_IN_COMMAND_ID,
	GET_LOGIN_INFO_COMMAND_ID,
} from "./loginAction.js";

// 注册登录命令
export function registerLoginCommands() {
	// 登录命令
	CommandsRegistry.registerCommand(
		LOGIN_ACTION_ID,
		async (accessor: ServicesAccessor) => {
			const loginService = accessor.get(ILoginService);
			return loginService.login();
		}
	);

	// 检查登录状态命令
	CommandsRegistry.registerCommand(
		IS_LOGGED_IN_COMMAND_ID,
		async (accessor: ServicesAccessor) => {
			const loginService = accessor.get(ILoginService);
			return loginService.isLoggedIn();
		}
	);

	// 获取登录信息命令
	CommandsRegistry.registerCommand(
		GET_LOGIN_INFO_COMMAND_ID,
		async (accessor: ServicesAccessor) => {
			const loginService = accessor.get(ILoginService);
			return loginService.getLoginInfo();
		}
	);
}
```

### 3.8 登录状态服务实现

```typescript
// src/vs/workbench/contrib/JoyCode/browser/loginStatusService.ts

import {
	InstantiationType,
	registerSingleton,
} from "../../../../platform/instantiation/common/extensions.js";
import { createDecorator } from "../../../../platform/instantiation/common/instantiation.js";
import { LoginStatusTracker } from "./loginAction.js";
import { IContextKeyService } from "../../../../platform/contextkey/common/contextkey.js";
import { ILoginService } from "../../../../platform/login/common/login.js";

// 创建服务接口
export const ILoginStatusService =
	createDecorator<ILoginStatusService>("loginStatusService");

// 定义服务接口
export interface ILoginStatusService {
	readonly _serviceBrand: undefined;
}

// 登录状态服务实现
class LoginStatusService
	extends LoginStatusTracker
	implements ILoginStatusService
{
	readonly _serviceBrand: undefined;
	private readonly _loginService: ILoginService;

	constructor(
		@IContextKeyService contextKeyService: IContextKeyService,
		@ILoginService loginService: ILoginService
	) {
		super(contextKeyService, loginService);

		// 存储一份登录服务的引用
		this._loginService = loginService;

		// 立即检查一次登录状态
		this.checkInitialLoginStatus();
	}

	// 单独检查初始登录状态的方法
	private async checkInitialLoginStatus(): Promise<void> {
		try {
			const isLoggedIn = await this._loginService.isLoggedIn();
			console.log("LoginStatusService: 当前登录状态:", isLoggedIn);
		} catch (error) {
			console.error("LoginStatusService: 检查登录状态失败:", error);
		}
	}
}

// 注册服务为急切实例化，确保在应用启动时就初始化
registerSingleton(
	ILoginStatusService,
	LoginStatusService,
	InstantiationType.Eager
);
```

### 3.9 登录工作台贡献实现

```typescript
// src/vs/workbench/contrib/JoyCode/browser/loginContribution.ts

import { Registry } from "../../../../platform/registry/common/platform.js";
import {
	Extensions as WorkbenchExtensions,
	IWorkbenchContribution,
	IWorkbenchContributionsRegistry,
} from "../../../../workbench/common/contributions.js";
import { LifecyclePhase } from "../../../../workbench/services/lifecycle/common/lifecycle.js";
import { registerLoginCommands } from "./loginCommands.js";
import { Disposable } from "../../../../base/common/lifecycle.js";
import { IInstantiationService } from "../../../../platform/instantiation/common/instantiation.js";
import { ILoginStatusService } from "./loginStatusService.js";

class LoginContribution extends Disposable implements IWorkbenchContribution {
	constructor(
		@IInstantiationService
		private readonly instantiationService: IInstantiationService
	) {
		super();
		console.log("LoginContribution: 构造函数开始执行");
		this.initialize();
	}

	private initialize(): void {
		// 注册登录命令
		registerLoginCommands();

		// 确保登录状态服务被初始化
		try {
			const loginStatusService = this.instantiationService.invokeFunction(
				(accessor) => accessor.get(ILoginStatusService)
			);
			console.log(
				"LoginContribution: 登录状态服务已获取",
				!!loginStatusService
			);
		} catch (error) {
			console.error("LoginContribution: 获取登录状态服务失败", error);
		}
	}
}

// 注册工作台贡献，使用Eventually阶段，确保在系统稳定后初始化
Registry.as<IWorkbenchContributionsRegistry>(
	WorkbenchExtensions.Workbench
).registerWorkbenchContribution(LoginContribution, LifecyclePhase.Eventually);
```

### 3.10 JoyCode 贡献加载

```typescript
// src/vs/workbench/contrib/JoyCode/browser/joyCoder.contribution.ts

// 其他导入...

// settings pane
import "./joyCoderSettingsPane.js";
import "./loginAction.js";

// register login contributions
import "./loginContribution.js";

// register login status tracker
import "./loginStatusService.js";

// register css
import "./media/JoyCode.css";
```

## 四、初始化流程

1. **IDE 启动**: 在 VS Code/JoyCode 启动时，加载`workbench.common.main.ts`
2. **JoyCode 贡献加载**: 加载`joyCoder.contribution.js`，它导入所有 JoyCode 相关模块
3. **登录状态服务注册**: `loginStatusService.js`被导入，注册`ILoginStatusService`为`InstantiationType.Eager`(急切实例化)
4. **工作台贡献注册**: `loginContribution.js`被导入，注册`LoginContribution`在`LifecyclePhase.Eventually`阶段初始化
5. **服务初始化**: 由于`ILoginStatusService`是急切实例化的，它会在注册后立即被创建
6. **上下文键设置**: `LoginStatusTracker`(被`LoginStatusService`继承)设置上下文键(`NOT_LOGGED_IN_CONTEXT_KEY`)
7. **命令注册**: 在`LoginContribution`初始化时，通过`registerLoginCommands()`注册登录相关命令
8. **UI 响应**: 登录按钮(`LOGIN_ACTION_ID`)根据上下文键状态显示或隐藏

## 五、使用方式

### 5.1 渲染进程中使用登录服务

```typescript
// 在渲染进程的组件中使用登录服务
import { ILoginService, ILoginInfo } from 'vs/platform/login/common/login';

// 注入登录服务
constructor(
    @ILoginService private readonly loginService: ILoginService
) {}

// 调用登录
const handleLogin = async () => {
    try {
        const success = await this.loginService.login();
        if (success) {
            console.log('登录页面已打开');
        }
    } catch (error) {
        console.error('登录失败:', error);
    }
};

// 检查登录状态
const checkLoginStatus = async () => {
    const isLoggedIn = await this.loginService.isLoggedIn();
    console.log('是否已登录:', isLoggedIn);
};

// 获取登录信息
const getLoginInfo = async () => {
    const loginInfo = await this.loginService.getLoginInfo();
    console.log('登录信息:', loginInfo);
};

// 退出登录
const handleLogout = async () => {
    const success = await this.loginService.logout();
    if (success) {
        console.log('已成功登出');
    }
};

// 监听登录状态变化
this.loginService.onDidChangeLoginStatus((isLoggedIn) => {
    console.log('登录状态变化:', isLoggedIn ? '已登录' : '未登录');
});
```

### 5.2 React 组件中访问登录服务

```tsx
// 在React组件中使用登录服务
import React, { useEffect, useState, useCallback } from "react";
import { useAccessor } from "../util/services";

const LoginComponent = () => {
	const { get } = useAccessor();
	const loginService = get("ILoginService");
	const [isLoggedIn, setIsLoggedIn] = useState(false);
	const [loginInfo, setLoginInfo] = useState<any>(null);

	// 初始化登录状态
	useEffect(() => {
		const initLoginStatus = async () => {
			try {
				const status = await loginService.isLoggedIn();
				setIsLoggedIn(status);

				if (status) {
					const info = await loginService.getLoginInfo();
					setLoginInfo(info);
				}
			} catch (error) {
				console.error("初始化登录状态失败:", error);
			}
		};

		initLoginStatus();

		// 监听登录状态变化
		const disposable = loginService.onDidChangeLoginStatus(async (status) => {
			setIsLoggedIn(status);

			if (status) {
				const info = await loginService.getLoginInfo();
				setLoginInfo(info);
			} else {
				setLoginInfo(null);
			}
		});

		return () => {
			disposable.dispose();
		};
	}, [loginService]);

	// 登录方法
	const handleLogin = useCallback(async () => {
		try {
			await loginService.login();
		} catch (error) {
			console.error("登录失败:", error);
		}
	}, [loginService]);

	// 登出方法
	const handleLogout = useCallback(async () => {
		try {
			await loginService.logout();
		} catch (error) {
			console.error("登出失败:", error);
		}
	}, [loginService]);

	return (
		<div>
			{isLoggedIn ? (
				<>
					<p>已登录用户: {loginInfo?.userName}</p>
					<button onClick={handleLogout}>登出</button>
				</>
			) : (
				<button onClick={handleLogin}>登录</button>
			)}
		</div>
	);
};

export default LoginComponent;
```

### 5.3 通过命令调用登录功能

```typescript
// 通过命令服务调用登录功能
import { ICommandService } from 'vs/platform/commands/common/commands';

// 注入命令服务
constructor(
    @ICommandService private readonly commandService: ICommandService
) {}

// 调用登录命令
const triggerLogin = async () => {
    await this.commandService.executeCommand('workbench.action.joycoderLogin');
};

// 检查登录状态
const checkLoginStatus = async () => {
    const isLoggedIn = await this.commandService.executeCommand('workbench.action.joycoderIsLoggedIn');
    console.log('登录状态:', isLoggedIn);
};

// 获取登录信息
const getLoginInfo = async () => {
    const loginInfo = await this.commandService.executeCommand('workbench.action.joycoderGetLoginInfo');
    console.log('登录信息:', loginInfo);
};
```

### 5.4 在插件中使用登录功能

插件可以通过 VS Code 的命令 API 调用登录相关功能：

```typescript
import * as vscode from "vscode";

// 插件激活函数
export function activate(context: vscode.ExtensionContext) {
	// 注册一个命令，可以在插件中触发登录功能
	let loginCommand = vscode.commands.registerCommand(
		"myExtension.login",
		async () => {
			try {
				// 调用VS Code中的登录命令
				const result = await vscode.commands.executeCommand(
					"workbench.action.joycoderLogin"
				);
				vscode.window.showInformationMessage(`登录操作已执行: ${result}`);
			} catch (error) {
				vscode.window.showErrorMessage(`登录失败: ${error}`);
			}
		}
	);

	// 检查登录状态
	let checkLoginStatusCommand = vscode.commands.registerCommand(
		"myExtension.checkLoginStatus",
		async () => {
			try {
				const isLoggedIn = await vscode.commands.executeCommand(
					"workbench.action.joycoderIsLoggedIn"
				);
				vscode.window.showInformationMessage(
					`当前登录状态: ${isLoggedIn ? "已登录" : "未登录"}`
				);
			} catch (error) {
				vscode.window.showErrorMessage(`检查登录状态失败: ${error}`);
			}
		}
	);

	// 获取登录信息
	let getLoginInfoCommand = vscode.commands.registerCommand(
		"myExtension.getLoginInfo",
		async () => {
			try {
				const loginInfo = await vscode.commands.executeCommand(
					"workbench.action.joycoderGetLoginInfo"
				);
				if (loginInfo) {
					vscode.window.showInformationMessage(
						`当前登录用户: ${JSON.stringify(loginInfo)}`
					);
				} else {
					vscode.window.showInformationMessage("未登录或无法获取登录信息");
				}
			} catch (error) {
				vscode.window.showErrorMessage(`获取登录信息失败: ${error}`);
			}
		}
	);

	// 将命令添加到上下文
	context.subscriptions.push(
		loginCommand,
		checkLoginStatusCommand,
		getLoginInfoCommand
	);
}
```

## 六、调试与日志

为了便于诊断登录功能的工作状态，系统在关键节点添加了详细的日志输出：

1. **服务注册时**:

   ```
   LoginStatusService: 正在注册服务为InstantiationType.Eager
   LoginStatusService: 服务注册完成
   ```

2. **工作台贡献初始化时**:

   ```
   LoginContribution: 构造函数开始执行
   LoginContribution: 开始初始化
   LoginContribution: 登录命令已注册
   LoginContribution: 登录状态服务已获取 true
   LoginContribution: 初始化完成
   ```

3. **登录状态跟踪器初始化时**:

   ```
   LoginStatusTracker: 构造函数开始执行
   LoginStatusTracker: 初始化登录状态检查开始
   LoginStatusTracker: 初始登录状态: false
   LoginStatusTracker: 上下文键已设置为: true
   LoginStatusTracker: 构造函数执行完成，已注册登录状态监听
   ```

4. **登录状态变化时**:
   ```
   LoginStatusTracker: 检测到登录状态变化事件
   LoginStatusTracker: 更新登录状态检查开始
   LoginStatusTracker: 更新后登录状态: true
   LoginStatusTracker: 上下文键已更新为: false
   ```

这些日志可以通过开发者工具控制台查看，帮助开发者定位可能的问题。

## 七、实现步骤

1. 实现登录服务接口定义
2. 实现主进程登录服务和 IPC 通道
3. 实现浏览器进程登录服务
4. 实现协议处理器以接收登录回调
5. 实现登录状态跟踪器，更新上下文键
6. 实现登录状态服务，封装状态跟踪器功能
7. 实现工作台贡献，处理初始化逻辑
8. 注册登录相关命令
9. 创建登录 UI 组件

## 八、注意事项

1. **安全性**：确保登录信息的安全存储，避免敏感信息泄露

2. **多平台兼容**：

   - Windows 和 Linux：通过`second-instance`事件处理协议调用
   - macOS：通过`open-url`事件处理协议调用

3. **错误处理**：

   - 各类异步操作都应有完善的错误处理
   - 网络请求可能失败，需要妥善处理异常情况
   - 登录状态变化需要正确传播到 UI 层

4. **插件兼容性**：

   - 确保插件能够通过命令系统访问登录功能
   - 命令必须返回有意义的结果，便于插件处理

5. **回调 URL 格式**：处理形如以下格式的回调 URL：
   ```
   joycoder://callback?info=%7B%22userName%22%3A%22example%22%2C%22pt_key%22%3A%22AAJn2nlvADD8U2strJSBubBIou9zexrE9fHEJFwcAnP1G_Ty8Yhix8eqHf5RWBaIj4Hz9uHbup4%22%7D
   ```

## 九、总结

登录功能通过完整的服务体系和命令系统，为渲染进程和插件提供了一致的登录体验。登录状态通过上下文键系统与 UI 集成，使登录按钮能根据登录状态自动显示或隐藏。在整个系统中，采用了分层设计：主进程服务处理底层通信和存储，浏览器进程服务提供 API 接口，工作台贡献协调初始化流程，登录状态服务监控状态变化。这种设计不仅保证了功能的可靠性，也提供了良好的扩展性和可维护性。
