/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { localize } from '../../../../nls.js';

export const titlebarMessages = {
	login: localize('joycode.setting.menu.login', '登录账号'),
	checkUpdate: localize('joycode.setting.menu.checkUpdate', '检查更新...'),
	projectSettings: localize('joycode.setting.menu.projectSettings', '项目设置'),
	settings: localize('joycode.setting.menu.settings', '设置...'),
	joycodeSettings: localize('joycode.setting.menu.joycodeSettings', 'JoyCode 设置'),
	theme: localize('joycode.setting.menu.theme', '主题'),
	language: localize('joycode.setting.menu.language', '语言'),
	keybindings: localize('joycode.setting.menu.keybindings', '键盘快捷键'),
	extensions: localize('joycode.setting.menu.extensions', '扩展'),
	about: localize('joycode.setting.menu.about', '关于'),
	releaseNote: localize('joycode.setting.menu.releaseNote', '发行说明'),
	logout: localize('joycode.setting.menu.logout', '退出登录')
};

export const changeDisplayLanguageMessages = {
	confirm: localize('joycode.setting.language.confirm', '是否要将显示语言更改为{0}？需要重启 JoyCode 以应用更改。'),
	yes: localize('joycode.setting.language.yes', '是'),
	error: localize('joycode.setting.language.error', '更改显示语言时发生错误: {0}')
};
