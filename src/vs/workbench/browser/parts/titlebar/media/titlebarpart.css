/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Part Element */
.monaco-workbench .part.titlebar {
	display: flex;
	flex-direction: row;
}

.monaco-workbench.mac .part.titlebar {
	flex-direction: row-reverse;
}

/* Root Container */
.monaco-workbench .part.titlebar > .titlebar-container {
	box-sizing: border-box;
	overflow: hidden;
	flex-shrink: 1;
	flex-grow: 1;
	align-items: center;
	justify-content: space-between;
	user-select: none;
	-webkit-user-select: none;
	display: flex;
	height: 100%;
	width: 100%;
}

/* Account for zooming */
.monaco-workbench .part.titlebar > .titlebar-container.counter-zoom {
	zoom: calc(1.0 / var(--zoom-factor));
}

/* Platform specific root element */
.monaco-workbench.mac .part.titlebar > .titlebar-container {
	line-height: 22px;
}

.monaco-workbench.web .part.titlebar > .titlebar-container,
.monaco-workbench.windows .part.titlebar > .titlebar-container,
.monaco-workbench.linux .part.titlebar > .titlebar-container {
	line-height: 22px;
	justify-content: left;
}

.monaco-workbench.web.safari .part.titlebar,
.monaco-workbench.web.safari .part.titlebar > .titlebar-container {
	/* Must be scoped to safari due to #148851 */
	/* Is required in safari due to #149476 */
	overflow: visible;
}

/* Draggable region */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-drag-region {
	top: 0;
	left: 0;
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	-webkit-app-region: drag;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left,
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center,
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right {
	display: flex;
	height: 100%;
	align-items: center;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left {
	order: 0;
	width: 70px;
	min-width: 70px;
	/* flex-grow: 2; */
	justify-content: flex-start;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center {
	order: 1;
	/* width: calc(80% - 80px); */
	/* max-width: fit-content; */
	/* min-width: 0px; */
	margin: 0 10px;
	/* flex-shrink: 10; */
	justify-content: center;
	flex: 1;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right {
	order: 2;
	min-width:150px;
	/* min-width: min-content; */
	/* flex-grow: 2; */
	justify-content: flex-end;
}



/* Window title text */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title {
	flex: 0 1 auto;
	font-size: 12px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	margin-left: auto;
	margin-right: auto;
}

.monaco-workbench.web .part.titlebar > .titlebar-container > .titlebar-center > .window-title,
.monaco-workbench.windows .part.titlebar > .titlebar-container > .titlebar-center > .window-title,
.monaco-workbench.linux .part.titlebar > .titlebar-container > .titlebar-center > .window-title {
	cursor: default;
}

.monaco-workbench.linux .part.titlebar > .titlebar-container > .titlebar-center > .window-title {
	font-size: inherit;
	/* see #55435 */
}

.monaco-workbench .part.titlebar > .titlebar-container .monaco-toolbar .actions-container {
	gap: 4px;
}

/* Window Title Menu */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center {
	z-index: 2500;
	-webkit-app-region: no-drag;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center.hide {
	visibility: hidden;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item > .action-label,
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item.monaco-dropdown-with-primary .action-label {
	color: var(--vscode-titleBar-activeForeground);
}

.monaco-workbench .part.titlebar.inactive > .titlebar-container > .titlebar-center > .window-title > .command-center > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item > .action-label,
.monaco-workbench .part.titlebar.inactive > .titlebar-container > .titlebar-center > .window-title > .command-center > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item.monaco-dropdown-with-primary .action-label {
	color: var(--vscode-titleBar-inactiveForeground);
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center > .monaco-toolbar > .monaco-action-bar > .actions-container > .action-item > .action-label {
	color: inherit;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center {
	display: flex;
	align-items: stretch;
	color: var(--vscode-commandCenter-foreground);
	background-color: var(--vscode-commandCenter-background);
	border: 1px solid var(--vscode-commandCenter-border);
	overflow: hidden;
	margin: 0 6px;
	border-top-left-radius: 6px;
	border-bottom-left-radius: 6px;
	border-top-right-radius: 6px;
	border-bottom-right-radius: 6px;
	height: 22px;
	width: 38vw;
	max-width: 600px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center .action-item.command-center-quick-pick {
	display: flex;
	justify-content: start;
	overflow: hidden;
	margin: auto;
	max-width: 600px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center .action-item.command-center-quick-pick .search-icon {
	font-size: 14px;
	opacity: .8;
	margin: auto 3px;
	color: var(--vscode-commandCenter-foreground);
}

.monaco-workbench .part.titlebar.inactive > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center .action-item.command-center-quick-pick .search-icon {
	color: var(--vscode-titleBar-inactiveForeground);
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center .action-item.command-center-quick-pick .search-label {
	overflow: hidden;
	text-overflow: ellipsis;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center.multiple {
	justify-content: flex-start;
	padding: 0 12px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center.multiple.active .action-label {
	background-color: inherit;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center:only-child {
	margin-left: 0; /* no margin if there is only the command center, without nav buttons */
}

.monaco-workbench .part.titlebar.inactive > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center {
	color: var(--vscode-titleBar-inactiveForeground);
	border-color: var(--vscode-commandCenter-inactiveBorder) !important;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-center > .window-title > .command-center .action-item.command-center-center:HOVER {
	color: var(--vscode-commandCenter-activeForeground);
	background-color: var(--vscode-commandCenter-activeBackground);
	border-color: var(--vscode-commandCenter-activeBorder);
}

/* Menubar */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left > .menubar {
	/* move menubar above drag region as negative z-index on drag region cause greyscale AA */
	z-index: 2500;
	min-width: 36px;
	flex-wrap: nowrap;
	order: 2;
}

.monaco-workbench.web .part.titlebar > .titlebar-container > .titlebar-left > .menubar {
	margin-left: 4px;
}

.monaco-workbench .part.titlebar > .titlebar-container.counter-zoom .menubar .menubar-menu-button > .menubar-menu-items-holder.monaco-menu-container,
.monaco-workbench .part.titlebar > .titlebar-container.counter-zoom .monaco-toolbar .dropdown-action-container {
	zoom: var(--zoom-factor); /* helps to position the menu properly when counter zooming */
}

/* App Icon */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left > .window-appicon {
	width: 35px;
	height: 100%;
	position: relative;
	z-index: 2500;
	flex-shrink: 0;
	order: 1;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left > .window-appicon:not(.codicon) {
	background-image: url('../../../media/code-icon.svg');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 16px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-left > .window-appicon.codicon {
	line-height: 30px;
}

.monaco-workbench.fullscreen .part.titlebar > .titlebar-container > .titlebar-left > .window-appicon {
	display: none;
}

/* Window Controls Container */
.monaco-workbench .part.titlebar .window-controls-container {
	display: flex;
	flex-grow: 0;
	flex-shrink: 0;
	text-align: center;
	z-index: 3000;
	-webkit-app-region: no-drag;
	width: 0px;
	height: 100%;
}

.monaco-workbench.fullscreen .part.titlebar .window-controls-container {
	display: none;
	background-color: transparent;
}

/* Window Controls Container Web: Apply WCO environment variables (https://developer.mozilla.org/en-US/docs/Web/CSS/env#titlebar-area-x) */
.monaco-workbench.web .part.titlebar .titlebar-right .window-controls-container {
	width: calc(100vw - env(titlebar-area-width, 100vw) - env(titlebar-area-x, 0px));
	height: env(titlebar-area-height, 35px);
}

.monaco-workbench.web .part.titlebar .titlebar-left .window-controls-container {
	width: env(titlebar-area-x, 0px);
	height: env(titlebar-area-height, 35px);
}

.monaco-workbench.web.mac .part.titlebar .titlebar-left .window-controls-container {
	order: 0;
}

.monaco-workbench.web.mac .part.titlebar .titlebar-right .window-controls-container {
	order: 1;
}

/* Window Controls Container Desktop: apply zoom friendly size */
.monaco-workbench:not(.web):not(.mac) .part.titlebar .window-controls-container {
	width: calc(138px / var(--zoom-factor, 1));
}

.monaco-workbench:not(.web):not(.mac) .part.titlebar .titlebar-container.counter-zoom .window-controls-container {
	width: 138px;
}

.monaco-workbench.linux:not(.web) .part.titlebar .window-controls-container.wco-enabled {
	width: calc(100vw - env(titlebar-area-width, 100vw) - env(titlebar-area-x, 0px));
}

.monaco-workbench:not(.web):not(.mac) .part.titlebar .titlebar-container:not(.counter-zoom) .window-controls-container * {
	zoom: calc(1 / var(--zoom-factor, 1));
}

.monaco-workbench:not(.web).mac .part.titlebar .window-controls-container {
	width: 70px;
}

/* Action Tool Bar Controls */
.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container {
	display: none;
	padding-right: 4px;
	flex-grow: 0;
	flex-shrink: 0;
	text-align: center;
	position: relative;
	z-index: 2500;
	-webkit-app-region: no-drag;
	height: 100%;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container {
	margin-left: auto;
}

.monaco-workbench.mac:not(.web) .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container {
	right: 8px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container:not(.has-no-actions) {
	display: flex;
	justify-content: center;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container .codicon {
	color: inherit;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container .monaco-action-bar .action-item {
	display: flex;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container .monaco-action-bar .badge {
	margin-left: 8px;
	display: flex;
	align-items: center;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container .monaco-action-bar .action-item.icon .badge {
	margin-left: 0px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container .monaco-action-bar .badge .badge-content {
	padding: 3px 5px;
	border-radius: 11px;
	font-size: 9px;
	min-width: 11px;
	height: 16px;
	line-height: 11px;
	font-weight: normal;
	text-align: center;
	display: inline-block;
	box-sizing: border-box;
	position: relative;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container .monaco-action-bar .action-item.icon .badge.compact {
	position: absolute;
	top: 0;
	bottom: 0;
	margin: auto;
	left: 0;
	overflow: hidden;
	width: 100%;
	height: 100%;
	z-index: 2;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container .monaco-action-bar .action-item.icon .badge.compact .badge-content::before {
	mask-size: 12px;
	-webkit-mask-size: 12px;
	top: 2px;
}

.monaco-workbench .part.titlebar > .titlebar-container > .titlebar-right > .action-toolbar-container .monaco-action-bar .action-item.icon .badge.compact .badge-content {
	position: absolute;
	top: 10px;
	right: 0px;
	font-size: 9px;
	font-weight: 600;
	min-width: 12px;
	height: 12px;
	line-height: 12px;
	padding: 0 2px;
	border-radius: 16px;
	text-align: center;
}

.joycoder-update-btn {
	display: flex;
	align-items: center;
	background: #23272e;
	border-radius: 20px;
	margin-top: 5px;
	margin-right: 14px;
	padding: 0 12px 0 4px;
	height: 24px;
	cursor: pointer;
	font-size: 17px;
	color: #fff;
	margin-left: 12px;
	/*box-shadow: 0 1px 2px rgba(0,0,0,0.07);*/
	box-shadow: 0 1px 2px rgba(43,43,43,1);
	transition: background 0.2s;
}
.joycoder-update-btn:hover {
	/*background: #2c313a;*/
	cursor: pointer;
}
.joycoder-update-btn .icon {
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	width: 22px;
	height: 22px;
	margin-right: 6px;
}
.joycoder-update-btn .icon .circle {
	width: 16px;
	height: 16px;
	/*border: 2px solid #2EA043;*/
	border: 2px solid #3C3C3C;
	border-radius: 50%;
	box-sizing: border-box;
	display: block;
}
.joycoder-update-btn .icon .circle.done {
	border: 2px solid #2EA043;
}
.joycoder-update-btn .icon .arrow-down {
	position: absolute;
	left: 50%;
	top: 50%;
	width: 12px;
	height: 12px;
	transform: translate(-50%, -50%);
	background: url("./down-icon.png") no-repeat 50% 50%;
	background-size: contain;
}
.joycoder-update-btn .text {
	color: #F8F8F8;
	font-size: 12px;
	font-weight: 500;
	white-space: nowrap;
}
/*.joycoder-update-btn .arrow {*/
/*	margin-left: 8px;*/
/*	font-size: 20px;*/
/*	color: #bfc9d1;*/
/*}*/

.progress-ring {
	display: block;
}
.progress-ring__progress {
	transition: stroke-dashoffset 0.35s;
	transform: rotate(-90deg);
	transform-origin: 50% 50%;
}

.joycoder-settings-menu, .joycoder-menu-item .submenu {
	position: absolute;
	background: rgba(34,35,37,1);
	border: 1px solid rgba(48,48,53,1);
	border-radius: 6px;
	box-shadow: 0 8px 48px 0 rgba(0,0,0,1), 0 4px 8px 0 rgba(0,0,0,1);
	font-size: 13px;
	color: rgba(255,255,255,0.8);
}
.joycoder-settings-menu {
	padding: 8px;
	z-index: 100;
	top: 30px;
	right: 10px;
	width: 244px;
}

.joycoder-menu-item {
	/* margin: 2px 8px 0; */
	margin-top: 2px;
	height: 28px;
	display: flex;
	align-items: center;
	position: relative;
	padding: 0 8px;
	cursor: pointer;
	min-width: 120px;
}

.joycoder-menu-item:hover {
	background-color: #303035;
	border-radius: 4px;
}
.joycoder-menu-item.user-profile  {
	display: block;
	/* height: 48px; */
}

.joycoder-menu-item .user-info {
	display: flex;
	align-items: center;
}

.joycoder-menu-item .user-details {
	text-indent: 24px;
	color: rgba(255,255,255,0.5);
}

.joycoder-menu-item .user-name {
	font-size: 13px;
	color: var(--vscode-menu-foreground);
}

.joycoder-menu-item .company-name {
	font-size: 12px;
	color: var(--vscode-descriptionForeground);
}

.joycoder-menu-item .codicon {
	margin-right: 8px;
	font-size: 16px;
	color: var(--vscode-menu-foreground);
}

.joycoder-menu-item .codicon-account {
	font-size: 15px;
}

.joycoder-menu-item .codicon-chevron-right {
	margin-left: auto;
	font-size: 14px;
	opacity: 0.8;
}

/* 子菜单样式 */
.joycoder-menu-item .submenu {
	padding: 8px;
	display: none;
	right: 255px;
	top: 0px;
}

/* .joycoder-menu-item:hover .submenu {
	display: block;
} */

/* 分隔线样式 */
.joycoder-menu-divider {
	height: 1px;
	background-color: rgba(48,48,53,1);
	margin: 6px 8px;
	border-radius: 4px;
}

/* 语言选择项样式 */
.language-menu {
	display: flex;
	align-items: center;
	justify-content: space-between;
	cursor: pointer;
}

.language-menu .selected-language {
	color: var(--vscode-descriptionForeground);
	font-size: 12px;
	margin-left: auto;
	margin-right: 8px;
}

/* 语言选择项active状态 */
.language-options .joycoder-menu-item.active {
	background-color: #303035;
}

.joycoder-settings-btn {
	height: 100%;
}
.joycoder-settings-btn .codicon-settings-gear{
	padding: 3px;
	border-radius: 5px;
}
.joycoder-settings-btn .codicon-settings-gear:hover, .joycoder-settings-btn .codicon-settings-gear.active{
	outline: 1px dashed var(--vscode-toolbar-hoverOutline) !important;
    outline-offset: -1px !important;
	background-color: var(--vscode-toolbar-hoverBackground) !important;
}




