import { NewProjectChannel } from "../common/newProjectIpc.js";
import { IServerChannel } from "../../../../base/parts/ipc/common/ipc.js";
import { NewProjectMainService } from "../common/newProjectMainService.js";
import { IProductService } from '../../../../platform/product/common/productService.js';
import { ILogService } from '../../../../platform/log/common/log.js';
// import { INotificationService } from '../../../../platform/notification/common/notification.js';
// import { ICommandService } from '../../../../platform/commands/common/commands.js';

/**
 * 注册新建项目主进程服务及通道
 * @param mainProcessElectronServer 主进程IPC server
 * @param disposables DisposableStore
 */
export function registerNewProjectMainService(
	mainProcessElectronServer: { registerChannel: (name: string, channel: IServerChannel) => void },
	productService: IProductService,
	logService: ILogService,
	disposables?: { add?: (d: any) => void }
) {
	const service = new NewProjectMainService(
		productService,
		logService,
	);
	const channel = new NewProjectChannel(service, logService);
	mainProcessElectronServer.registerChannel("joycoderNewProject", channel);
	// 可扩展 disposables 管理
}

