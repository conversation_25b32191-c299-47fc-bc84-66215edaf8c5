
import { CancellationToken } from '../../../../base/common/cancellation.js';
import { URI } from '../../../../base/common/uri.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';


export const INewProjectService = createDecorator<INewProjectService>('newProjectService');

/**
 * 新建项目服务接口
 * 提供获取模板列表、下载和解压模板等功能
 */


/**
 * 模板信息接口
 */
export interface ITemplateInfo {
	/**
	 * 模板ID
	 */
	id: string | number;

	/**
	 * 模板名称
	 */
	templateName: string;

	/**
	 * 模板英文名称
	 */
	templateNameEn?: string;

	/**
	 * 模板标签
	 */
	templateLabel?: string;

	/**
	 * 模板描述
	 */
	templateDesc?: string;

	/**
	 * 模板英文描述
	 */
	templateDescEn?: string;

	/**
	 * 模板下载地址
	 */
	templateAddr?: string | null;

	/**
	 * 模板图片地址
	 */
	imgUrl?: string;

	/**
	 * 模板类别ID
	 */
	templateClassId?: number;

	/**
	 * 模板类别名称
	 */
	templateClassName?: string;

	/**
	 * 模板状态
	 */
	templateStatus?: string;

	/**
	 * 模板配置列表
	 */
	templateConfigList?: any | null;

	/**
	 * 版本配置（JSON字符串）
	 */
	versionConfig?: string;

	/**
	 * 版本配置列表
	 */
	versionConfigList?: Array<{
		/**
		 * 组件ID
		 */
		componentId: string;

		/**
		 * 组件名称
		 */
		componentName: string;

		/**
		 * 版本列表
		 */
		versionList: Array<{
			/**
			 * 版本名称
			 */
			versionName: string;
		}>;

		/**
		 * 长度
		 */
		length?: number;
	}>;

	/**
	 * 提示信息
	 */
	prompt?: string | null;

	/**
	 * 备注信息
	 */
	remark?: string | null;

	/**
	 * 创建时间（时间戳）
	 */
	createTime?: number | string;

	/**
	 * 更新时间
	 */
	updateTime?: string;

	/**
	 * 长度
	 */
	length?: number;
}

/**
 * 模板列表请求参数
 */
export interface ITemplateListParams {
	/**
	 * 模板名称（用于搜索）
	 */
	templateName: string;

	/**
	 * 模板英文名称（用于搜索）
	 */
	templateNameEn?: string;
}

/**
 * 模板下载信息
 */
export interface ITemplateDownloadInfo {

	type: 'local' | 'remote';

	/**
	 * 模板下载地址
	 */
	url: string;

	/**
	 * 模板名称
	 */
	name: string;

	/**
	 * 目标路径
	 */
	targetPath: string;
}

/**
 * 新建项目服务参数
 */
export interface INewProjectParams {
	name: string;
	devMode: number;
	description: string;
	localLocation?: string;
	projectTemplateId?: number;
	projectTemplateName?: string;
	projectTemplateVersion?: string;
	projectTemplateUrl?: string;
	projectTemplateEnvs?: Array<{
		name: string;
		version: string;
	}>;
	databaseType?: number;
	databaseId?: number;
	databaseCustomHost?: string;
	databaseCustomPort?: number;
	databaseCustomName?: string;
	databaseCustomUsername?: string;
	databaseCustomPassword?: string;
}

/**
 * 新建项目返回参数
 */
export interface INewProjectResult {
	id: number;
	name: string;
	description: string;
	devMode: number;
	status?: 'Pending' | 'Running';
	url: string;
	databaseHost?: string;
	databasePort?: string;
	databaseName?: string;
	databaseUsername?: string;
	databasePassword?: string;
}

/**
 * 新建项目服务接口
 */
export interface INewProjectService {
	readonly _serviceBrand: undefined;

	/**
	 * 获取模板列表
	 * @param params 请求参数
	 * @param token 取消令牌
	 * @returns 模板列表
	 */
	getTemplateList(params?: ITemplateListParams): Promise<ITemplateInfo[]>;

	/**
	 * 新建项目
	 * @param params 请求参数
	 * @param token 取消令牌
	 * @returns 模板列表
	 */
	createProject(params: INewProjectParams): Promise<IRes | null>;


	getProjectList(): Promise<any[]>;

	getProjectSSHConfig(projectId: number): Promise<IRes<ISSHInfo> | null>;
	getProjectDatabaseList(): Promise<any[]>;
	setProjectInfo(params: any): Promise<any>;
	setProjectAppliedTem(params: any): Promise<any>;

	/**
	 * 获取项目信息
	 * @param data 请求参数
	 * @returns 项目信息
	 */
	getProjectInfo(projectId: number): Promise<IRes | null>;

	/**
	 * 下载并解压模板
	 * @param downloadUrl 下载地址
	 * @param targetPath 目标路径
	 * @param templateName 模板名称
	 * @param token 取消令牌
	 * @returns 是否成功
	 */
	downloadAndExtractTemplate(downloadUrl: string, targetPath: string, templateName?: string, token?: CancellationToken): Promise<boolean>;

	/**
	 * 检查项目路径是否存在
	 * @param projectPath 项目路径
	 * @returns 是否存在
	 */
	checkProjectExists(projectPath: string | URI): Promise<boolean>;

	/**
	 * 保存模板下载信息到本地存储
	 * @param info 模板下载信息
	 */
	saveTemplateDownloadInfo(info: ITemplateDownloadInfo): void;

	/**
	 * 从本地存储获取模板下载信息
	 * @returns 模板下载信息，如果不存在则返回undefined
	 */
	getTemplateDownloadInfo(): ITemplateDownloadInfo | undefined;

	/**
	 * 清除本地存储中的模板下载信息
	 */
	clearTemplateDownloadInfo(): void;
}

export interface ISSHInfo {
	projectId: number;
	host: string;
	user: string,
	port: number;
	privateKey: string;
}


export interface IRes<T = any> {
	code: number;
	data: T;
	message?: string;
	msg?: string;
}


export interface ISaveTemplateDownloadInfo {
	type: "local" | "remote";
	url: string;
	name: string;
	targetPath: string;
	projectInfo: INewProjectResult | null;
	remoteHost?: string;
	sshConfigRes?: any;
	sshHostLabel?: string;
	remotePath?: string;
	templateInfo?: ITemplateInfo;
}
