import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { Event } from '../../../../base/common/event.js';
import { IRes } from './newProject.js';
// import { ILogService } from '../../../../platform/log/common/log.js';

// 用于依赖注入的 service 标识符
export const INewProjectMainService = createDecorator<INewProjectMainService>('newProjectMainService');

export interface INewProjectMainService {
	readonly _serviceBrand: undefined;
	readonly onDidChangeLoginStatus: Event<boolean>;


	createProject(params: any): Promise<IRes>;
	getTemplateList(params: any): Promise<IRes>;
	getProjectList(ptKey: string): Promise<IRes>;
	getProjectInfo(data: { projectId: number, ptKey: string }): Promise<IRes>;
	getProjectSSHConfig(data: { projectId: number, ptKey: string }): Promise<IRes>;
	getProjectDatabaseList(ptKey: string): Promise<IRes>;
	setProjectInfo(data: { params: any, ptKey: string }): Promise<IRes>;
	setProjectAppliedTem(data: { params: any, ptKey: string }): Promise<IRes>;
	// 可拓展如 downloadTemplate、checkProjectExists 等
}

export class NewProjectChannelClient implements INewProjectMainService {
	declare readonly _serviceBrand: undefined;
	readonly onDidChangeLoginStatus: Event<boolean> = Event.None;


	constructor(
		private readonly channel: IChannel,
		// private readonly logService?: ILogService
	) { }

	async createProject(params: any): Promise<IRes> {
		return this.channel.call('createProject', params);
	}

	async getTemplateList(params: any): Promise<IRes> {
		return this.channel.call('getTemplateList', params);
	}
	async getProjectList(ptKey: string): Promise<IRes> {
		return this.channel.call('getProjectList', ptKey);
	}

	async getProjectInfo(data: { projectId: number, ptKey: string }): Promise<IRes> {
		return this.channel.call('getProjectInfo', data);
	}
	async getProjectSSHConfig(data: { projectId: number, ptKey: string }): Promise<IRes> {
		return this.channel.call('getProjectSSHConfig', data);
	}

	async getProjectDatabaseList(ptKey: string): Promise<IRes> {
		return this.channel.call('getProjectDatabaseList', ptKey);
	}
	async setProjectInfo(data: { params: any, ptKey: string }): Promise<IRes> {   // 异步方法，接收包含params和ptKey的对象，返回IRes或null的Promise
		return this.channel.call('setProjectInfo', data);   // 通过channel对象调用'call'方法，传递'setProjectInfo'字符串和data参数，执行设置项目信息操作
	}
	async setProjectAppliedTem(data: { params: any, ptKey: string }): Promise<IRes> {
		return this.channel.call('setProjectAppliedTem', data);
	}

}
