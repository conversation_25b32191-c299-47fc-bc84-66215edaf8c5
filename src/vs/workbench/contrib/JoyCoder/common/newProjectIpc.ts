import { INewProjectMainService } from "./INewProjectMainService.js";
import { IChannel, IServerChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { Event } from '../../../../base/common/event.js';
import { IRes } from './newProject.js';
import { ILogService } from '../../../../platform/log/common/log.js';


export class NewProjectChannel implements IServerChannel {
	constructor(
		private readonly service: INewProjectMainService,
		private readonly logService: ILogService
	) { }

	listen(_: unknown, event: string): Event<any> {
		switch (event) {
			case 'onDidChangeLoginStatus': return this.service.onDidChangeLoginStatus;
			default: throw new Error(`未知事件: ${event}`);
		}
	}


	async call(_: unknown, command: string, ...args: any[]): Promise<any> {
		switch (command) {
			case "createProject":
				this.logService.info('createP3', args);
				return this.service.createProject(args[0]);
			case "getTemplateList":
				return this.service.getTemplateList(args[0]);
			case "getProjectList":
				return this.service.getProjectList(args[0]);
			case "getProjectInfo":
				return this.service.getProjectInfo(args[0]);
			case "getProjectSSHConfig":
				return this.service.getProjectSSHConfig(args[0]);
			case "getProjectDatabaseList":
				return this.service.getProjectDatabaseList(args[0]);
			case "setProjectInfo":
				return this.service.setProjectInfo(args[0]);
			case "setProjectAppliedTem":
				return this.service.setProjectAppliedTem(args[0]);
			// 可拓展
		}
		throw new Error("Invalid command: " + command);
	}
}



export class NewProjectChannelClient implements INewProjectMainService {
	declare readonly _serviceBrand: undefined;

	private readonly _onDidChangeLoginStatus: Event<boolean>;

	constructor(
		private readonly channel: IChannel,
		private readonly logService: ILogService
	) {
		this._onDidChangeLoginStatus = this.channel.listen('onDidChangeLoginStatus');
	}

	get onDidChangeLoginStatus(): Event<boolean> {
		return this._onDidChangeLoginStatus;
	}

	async createProject(params: any): Promise<IRes> {
		this.logService.info('createP4', params);
		return this.channel.call('createProject', params);
	}

	async getTemplateList(params: any): Promise<IRes> {
		return this.channel.call('getTemplateList', params);
	}
	async getProjectInfo(data: { projectId: number, ptKey: string }): Promise<IRes> {
		return this.channel.call('getProjectInfo', data);
	}
	async getProjectSSHConfig(data: { projectId: number, ptKey: string }): Promise<IRes> {
		return this.channel.call('getProjectSSHConfig', data);
	}

	async getProjectList(ptKey: string): Promise<IRes> {
		return this.channel.call('getProjectList', ptKey);
	}
	async getProjectDatabaseList(ptKey: string): Promise<IRes> {
		return this.channel.call('getProjectDatabaseList', ptKey);
	}
	async setProjectInfo(data: { params: any, ptKey: string }): Promise<IRes> {
		return this.channel.call('setProjectInfo', data);
	}
	async setProjectAppliedTem(data: { params: any, ptKey: string }): Promise<IRes> {
		return this.channel.call('setProjectAppliedTem', data);
	}
}

