import fetch from "node-fetch";
import { INewProjectMainService } from "./INewProjectMainService.js";
import { Emitter } from "../../../../base/common/event.js";
import { IProductService } from '../../../../platform/product/common/productService.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { IRes } from "./newProject.js";
import { ILogService } from '../../../../platform/log/common/log.js';


export class NewProjectMainService implements INewProjectMainService {
	readonly _serviceBrand: undefined;
	private readonly _onDidChangeLoginStatus = new Emitter<boolean>();
	public readonly onDidChangeLoginStatus = this._onDidChangeLoginStatus.event;

	// 项目创建
	private readonly baseProject = '/api/v1/projects';
	private readonly projecDatabasetUrl = '/api/v1/databases';
	private readonly templateUrl = '/api/saas/templateApp/v1/list';

	constructor(
		@IProductService private readonly productService: IProductService,
		@ILogService private readonly logService: ILogService,
	) { }
	async createProject(data: { params: any, ptKey: string }): Promise<IRes> {
		const { params, ptKey } = data;
		const baseUrl = this.productService?.joyCoderCloudBaseUrl || '';
		const uri = baseUrl + this.baseProject;
		this.logService.info('createP', uri, params);
		this.logService.info('ptKey', ptKey);
		const response = await fetch(uri, {
			method: "POST",
			headers: {
				"accept": "*/*",
				"Content-Type": "application/json",
				"ptKey": ptKey || "",
			},
			body: JSON.stringify(params),
			// mode: 'cors',
			// credentials: 'include'
		});
		const res = await response.json();
		return res || null;
	}

	async getTemplateList(data: { params: any, ptKey: string }): Promise<IRes> {
		const { params, ptKey } = data;
		const baseUrl = this.productService?.joyCoderBaseUrl || '';
		const uri = baseUrl + this.templateUrl;
		const response = await fetch(uri, {
			method: "POST",
			headers: {
				"Content-Type": "application/json",
				"ptKey": ptKey || "",
			},
			body: JSON.stringify(params),
		});
		const res = await response.json();
		return res;
	}

	async getProjectList(ptKey: string): Promise<IRes> {
		const baseUrl = this.productService?.joyCoderCloudBaseUrl || '';
		const uri = baseUrl + this.baseProject + '/my';
		const response = await fetch(uri, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				"ptKey": ptKey || "",
			}
		});
		const data = await response.json();
		return data;
	}

	async getProjectInfo(data: { projectId: number, ptKey: string }): Promise<IRes> {
		const { projectId, ptKey } = data;
		const baseUrl = this.productService?.joyCoderCloudBaseUrl || '';
		const uri = baseUrl + this.baseProject + '/' + projectId;
		const response = await fetch(uri, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				"ptKey": ptKey || "",
			}
		});
		const res = await response.json();
		return res;
	}

	async getProjectDatabaseList(ptKey: string): Promise<IRes> {
		const baseUrl = this.productService?.joyCoderCloudBaseUrl || '';
		const uri = baseUrl + this.projecDatabasetUrl + '/my';
		const response = await fetch(uri, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				"ptKey": ptKey || "",
			}
		});
		const data = await response.json();
		return data;
	}

	async getProjectSSHConfig(data: { projectId: number, ptKey: string }): Promise<IRes> {
		const { projectId, ptKey } = data;
		const baseUrl = this.productService?.joyCoderCloudBaseUrl || '';
		const uri = baseUrl + this.baseProject + '/' + projectId + '/ssh-config';
		const response = await fetch(uri, {
			method: "GET",
			headers: {
				"Content-Type": "application/json",
				"ptKey": ptKey || "",
			}
		});
		const res = await response.json();
		return res;
	}
	async setProjectInfo(data: { params: any, ptKey: string }): Promise<IRes> {
		const { params, ptKey } = data;
		const projectId = params.projectId;
		delete params.projectId;
		this.logService.info('setProjectInfo:projectId', projectId);
		const baseUrl = this.productService?.joyCoderCloudBaseUrl || '';
		const uri = baseUrl + this.baseProject + '/' + projectId;
		const response = await fetch(uri, {
			method: "PUT",
			headers: {
				"Content-Type": "application/json",
				"ptKey": ptKey || "",
			},
			body: JSON.stringify(params),
		});
		const res = await response.json();
		return res;
	}

	async setProjectAppliedTem(data: { params: any, ptKey: string }): Promise<IRes> {
		const { params, ptKey } = data;
		const projectId = params.projectId;
		// const applied = params.applied;
		delete params.projectId;
		this.logService.info('setProjectAppliedTem:projectId', projectId);
		const baseUrl = this.productService?.joyCoderCloudBaseUrl || '';
		const uri = baseUrl + this.baseProject + '/' + projectId + '/template';
		const response = await fetch(uri, {
			method: "PUT",
			headers: {
				"Content-Type": "application/json",
				"ptKey": ptKey || "",
			},
			body: JSON.stringify(params),
		});
		const res = await response.json();
		return res;
	}
}

registerSingleton(INewProjectMainService, NewProjectMainService, InstantiationType.Delayed);
