/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ExtensionType } from '../../../../platform/extensions/common/extensions.js';
import { IProgressService } from '../../../../platform/progress/common/progress.js';
import { INotificationService, Severity } from '../../../../platform/notification/common/notification.js';
import { ProgressLocation } from '../../../../platform/progress/common/progress.js';
import { INativeEnvironmentService } from '../../../../platform/environment/common/environment.js';
import { IUserDataProfileService } from '../../../services/userDataProfile/common/userDataProfile.js';
import { dirname } from '../../../../base/common/path.js';
import { VSBuffer } from '../../../../base/common/buffer.js';
import { isWindows, isMacintosh, isLinux } from '../../../../base/common/platform.js';

/**
 * 扩展导入服务接口
 */
export interface IExtensionImportService {
	readonly _serviceBrand: undefined;

	/**
	 * 获取VS Code的扩展目录
	 */
	getVSCodeExtensionsPath(): Promise<string>;

	/**
	 * 从指定目录获取已安装的VS Code扩展
	 * @param extensionsPath VS Code扩展目录路径
	 */
	getInstalledVSCodeExtensions(extensionsPath: string): Promise<IVSCodeExtension[]>;

	/**
	 * 导入VS Code扩展到JoyCoder
	 * @param extensions 要导入的扩展列表
	 */
	importExtensions(): Promise<IImportResult>;

	/**
	 * 一键导入扩展
	 * 自动检测用户安装的VS Code扩展并导入
	 * @returns 导入结果
	 */
	oneClickImport(): Promise<IImportResult>;

	/**
	 * 从上传的文件导入扩展
	 * @param files 上传的文件列表
	 */
	importFromFiles(files: File[]): Promise<IImportResult>;

	/**
	 * 导入VS Code的设置文件（settings.json）
	 * 如果JoyCoder已有设置，则覆盖现有设置
	 * @param silent 是否不显示通知
	 * @returns 导入结果，成功返回true，失败返回false
	 */
	importSettings(silent?: boolean): Promise<boolean>;

	/**
	 * 导入VS Code的键盘绑定文件（keybindings.json）
	 * 如果JoyCoder已有键盘绑定，则覆盖现有绑定
	 * @param silent 是否不显示通知
	 * @returns 导入结果，成功返回true，失败返回false
	 */
	importKeybindings(silent?: boolean): Promise<boolean>;

	/**
	 * 一键导入VS Code的配置文件
	 * 包括settings.json和keybindings.json
	 * @returns 包含每个配置文件导入结果的对象
	 */
	importAllConfigurations(): Promise<{ settings: boolean; keybindings: boolean }>;

	/**
	 * 修复extensions.json中的路径问题
	 * 确保每个扩展的location.path和relativeLocation与实际目录名称一致
	 * @returns 修复的扩展数量
	 */
	fixExtensionsJsonPaths(): Promise<number>;

	/**
	 * 验证和清理extensions.json，确保其中的每个扩展在extensions目录下都存在
	 * 如果扩展不存在，则从配置中删除该扩展
	 * @returns 清理后的扩展数量
	 */
	validateAndCleanExtensionsJson(): Promise<number>;

	/**
	 * 过滤掉extensions.json中路径不正确的扩展
	 * 如果扩展的location.path不存在或不正确，则从配置中删除该扩展
	 * @returns 过滤掉的扩展数量
	 */
	filterInvalidExtensionPaths(): Promise<number>;
}

/**
 * 导入结果接口
 */
export interface IImportResult {
	/**
	 * 成功导入的扩展ID
	 */
	succeeded: string[];

	/**
	 * 导入失败的扩展ID
	 */
	failed: string[];

	/**
	 * 未在市场上找到的扩展ID
	 */
	notFound: string[];
}

/**
 * VS Code扩展信息
 */
export interface IVSCodeExtension {
	/** 扩展ID，格式为：publisher.name */
	id: string;
	/** 扩展在本地的路径 */
	path: string;
	/** 扩展类型 */
	joyCoderExtensionType: JoyCoderExtensionType;
	/** package.json内容 */
	packageJson: any;
	/** 扩展显示名称，可选 */
	displayName?: string;
	/** 扩展发布者，可选 */
	publisher?: string;
	/** 扩展版本，可选 */
	version?: string;
	/** 是否是内置扩展，可选 */
	isBuiltIn?: boolean;
	/** 是否与当前环境兼容，可选 */
	isCompatible?: boolean;
	/** 是否已安装，可选 */
	isInstalled?: boolean;
	/** 扩展类型，可选 */
	type?: ExtensionType;
}

// 扩展类型枚举（扩展我们的内部分类）
export enum JoyCoderExtensionType {
	// 用户安装的扩展
	User = 'user',
	// 内置扩展（在product.json中定义）
	BuiltIn = 'built-in',
	// VS Code核心模块（在app/extensions中但不在product.json中）
	CoreModule = 'core-module'
}

// 服务ID
export const IExtensionImportService = createDecorator<IExtensionImportService>('extensionImportService');

/**
 * 导入配置文件的结果接口
 */
export interface IConfigurationImportResult {
	/**
	 * 是否成功
	 */
	success: boolean;

	/**
	 * 文件名
	 */
	fileName: string;

	/**
	 * 源文件路径
	 */
	sourcePath: string;

	/**
	 * 目标文件路径
	 */
	targetPath: string;

	/**
	 * 错误信息（如果失败）
	 */
	error?: string;
}

/**
 * 扩展导入服务实现
 */
export class ExtensionImportService extends Disposable implements IExtensionImportService {
	_serviceBrand: undefined;

	constructor(
		@ILogService private readonly logService: ILogService,
		@IProgressService private readonly progressService: IProgressService,
		@INotificationService private readonly notificationService: INotificationService,
		@IFileService private readonly fileService: IFileService,
		@INativeEnvironmentService private readonly environmentService: INativeEnvironmentService,
		@IUserDataProfileService private readonly userDataProfileService: IUserDataProfileService
	) {
		super();
	}

	/**
	 * 获取VS Code的扩展目录路径
	 * 使用环境服务获取用户主目录
	 * @returns VS Code扩展目录路径
	 */
	public async getVSCodeExtensionsPath(): Promise<string> {
		// 使用环境服务获取用户主目录，这是VS Code推荐的方式
		const homePath = this.environmentService.userHome.fsPath;
		this.logService.info(`获取OS: isWindows: ${isWindows} , isMacintosh: ${isMacintosh} , isLinux: ${isLinux}`);
		// 使用platform.ts中导入的常量来判断操作系统类型
		if (isWindows) {
			return `${homePath}\\.vscode\\extensions`;
		} else if (isMacintosh) {
			return `${homePath}/.vscode/extensions`;
		} else if (isLinux) {
			return `${homePath}/.vscode/extensions`;
		}
		return `${homePath}/.vscode/extensions`;
	}

	/**
	 * 获取已安装的VS Code扩展
	 * 使用fileService读取目录内容
	 * @param extensionsPath VS Code扩展目录路径
	 * @returns VS Code扩展信息列表
	 */
	public async getInstalledVSCodeExtensions(extensionsPath: string): Promise<IVSCodeExtension[]> {
		this.logService.info(`正在获取已安装的VS Code扩展`);

		try {
			// 使用fileService读取目录
			const extensionsUri = URI.file(extensionsPath);

			// 检查目录是否存在
			const exists = await this.fileService.exists(extensionsUri);
			if (!exists) {
				this.logService.warn(`扩展目录不存在: ${extensionsPath}`);
				return [];
			}

			// 获取目录内容
			const result = await this.fileService.resolve(extensionsUri, { resolveMetadata: true });

			if (!result.children || result.children.length === 0) {
				this.logService.warn(`未在${extensionsPath}中找到任何扩展`);
				return [];
			}

			// 过滤出目录（扩展都是目录）并且不以点开头（排除隐藏文件夹）
			const dirEntries = result.children.filter(entry => entry.isDirectory && !entry.name.startsWith('.'));
			const dirNames = dirEntries.map(entry => entry.name);

			this.logService.info(`在${extensionsPath}中找到${dirNames.length}个扩展目录`);

			const extensions: IVSCodeExtension[] = [];

			// 遍历每个扩展目录，解析信息
			for (const dirName of dirNames) {
				// 解析扩展ID、发布者、名称和版本
				let extensionId = dirName;
				let publisher = '';
				let name = '';
				let version = '';

				// 分离版本号部分
				const versionSeparatorIndex = dirName.lastIndexOf('-');
				if (versionSeparatorIndex > 0) {
					extensionId = dirName.substring(0, versionSeparatorIndex);
					version = dirName.substring(versionSeparatorIndex + 1);
				}

				// 分离发布者和名称
				const publisherSeparatorIndex = extensionId.indexOf('.');
				if (publisherSeparatorIndex > 0) {
					publisher = extensionId.substring(0, publisherSeparatorIndex);
					name = extensionId.substring(publisherSeparatorIndex + 1);
				} else {
					// 如果没有点号，则整个是名称
					name = extensionId;
				}

				// 确保扩展ID的格式是 publisher.name
				if (publisher && name) {
					extensionId = `${publisher}.${name}`;

					const extensionPath = `${extensionsPath}/${dirName}`;
					const extension: IVSCodeExtension = {
						id: extensionId,
						path: extensionPath,
						publisher,
						displayName: name,
						version,
						// 简化：不读取package.json，直接使用从目录名解析的信息
						packageJson: {
							name,
							publisher,
							version
						},
						joyCoderExtensionType: JoyCoderExtensionType.User,
					};

					extensions.push(extension);
					this.logService.info(`发现扩展: ${extensionId}, 版本: ${version || '未知'}`);
				}
			}

			this.logService.info(`在${extensionsPath}中找到${extensions.length}个有效扩展`);
			return extensions;
		} catch (error) {
			this.logService.error('获取VS Code扩展列表失败', error);
			throw new Error(`无法获取扩展列表: ${error instanceof Error ? error.message : '未知错误'}`);
		}
	}

	/**
	 * 导入扩展
	 * @param progressReport 进度报告回调
	 * @returns 导入结果
	 */
	public async importExtensions(
		progressReport?: (report: { message?: string; increment?: number }) => void
	): Promise<IImportResult> {
		this.logService.info('🚀 开始导入VS Code扩展...');

		// 先过滤掉现有的无效扩展配置
		this.logService.info('🛢️ 先清理现有的无效扩展配置...');
		const initialFilteredCount = await this.filterInvalidExtensionPaths();
		if (initialFilteredCount > 0) {
			this.logService.info(`已清理${initialFilteredCount}个无效扩展配置`);
		}

		// 结果统计
		const succeeded: string[] = [];
		const failed: string[] = [];
		const notFound: string[] = [];

		try {
			// 1. 读取VS Code的extensions.json文件
			const homePath = this.environmentService.userHome.fsPath;
			const vscodeExtensionsJsonPath = `${homePath}/.vscode/extensions/extensions.json`;
			const vscodeExtensionsJsonUri = URI.file(vscodeExtensionsJsonPath);

			// 检查文件是否存在
			const exists = await this.fileService.exists(vscodeExtensionsJsonUri);
			if (!exists) {
				this.logService.warn(`extensions.json文件不存在: ${vscodeExtensionsJsonPath}`);
				return { succeeded, failed, notFound: [] };
			}

			// 读取文件内容并解析JSON
			this.logService.info('正在读取VS Code extensions.json文件...');
			const fileContent = await this.fileService.readFile(vscodeExtensionsJsonUri);
			const extensionsData = JSON.parse(fileContent.value.toString());
			console.log('extensionsData JSON', extensionsData);
			if (!Array.isArray(extensionsData)) {
				this.logService.error('extensions.json格式不正确，应为数组');
				return { succeeded, failed, notFound: [] };
			}

			// 2. 筛选扩展
			this.logService.info('筛选符合条件的扩展...');
			// 筛选"source": "gallery"的扩展，排除isBuiltin为true的扩展
			const galleryExtensions = extensionsData.filter(ext =>
				ext?.metadata?.source === 'gallery' &&
				(!ext?.metadata?.isBuiltin || ext?.metadata?.isBuiltin !== true)
			);

			this.logService.info(`找到${galleryExtensions.length}个符合条件的gallery扩展`);

			// 3. 排除名字中包含"joycoder"的扩展
			const filteredExtensions = galleryExtensions.filter(ext => {
				if (!ext.identifier || !ext.identifier.id) {
					return false;
				}

				const extensionId = ext.identifier.id.toLowerCase();
				if (extensionId.includes('joycoder')) {
					this.logService.info(`跳过扩展: ${ext.identifier.id}，因为名称中包含joycoder`);
					return false;
				}

				return true;
			});

			this.logService.info(`筛选后剩余${filteredExtensions.length}个扩展需要处理`);

			// 4. 读取当前IDE的extensions.json
			const currentExtensionsJsonPath = this.environmentService.extensionsPath;
			const currentExtensionsJsonUri = URI.file(`${currentExtensionsJsonPath}/extensions.json`);

			let currentExtensionsData: any[] = [];
			if (await this.fileService.exists(currentExtensionsJsonUri)) {
				const currentFileContent = await this.fileService.readFile(currentExtensionsJsonUri);
				try {
					currentExtensionsData = JSON.parse(currentFileContent.value.toString());
					if (!Array.isArray(currentExtensionsData)) {
						currentExtensionsData = [];
					}
				} catch (error) {
					this.logService.error('解析当前IDE extensions.json失败', error);
					currentExtensionsData = [];
				}
			}

			// 创建当前IDE扩展的ID和UUID映射，用于快速查找
			const currentExtensionIds = new Set(currentExtensionsData
				.filter(ext => ext.identifier && ext.identifier.id)
				.map(ext => ext.identifier.id.toLowerCase()));

			const currentExtensionUuids = new Set(currentExtensionsData
				.filter(ext => ext.identifier && ext.identifier.uuid)
				.map(ext => ext.identifier.uuid));

			// 5. 处理扩展
			const totalCount = filteredExtensions.length;
			let processedCount = 0;
			const newExtensionsConfig: any[] = [];

			for (const extension of filteredExtensions) {
				processedCount++;

				// 更新进度
				if (progressReport) {
					progressReport({
						message: ``,
						increment: 100 / totalCount
					});
				}

				try {
					const extensionId = extension.identifier.id;
					const extensionUuid = extension.identifier.uuid;

					// 检查是否已存在
					if (currentExtensionIds.has(extensionId.toLowerCase()) ||
						(extensionUuid && currentExtensionUuids.has(extensionUuid))) {
						this.logService.info(`跳过扩展: ${extensionId}，已在当前IDE中安装`);
						notFound.push(extensionId);
						continue;
					}

					// 获取扩展目录路径
					let extensionPath = '';
					if (extension.path) {
						extensionPath = extension.path;
					} else if (extension.location && extension.location.path) {
						extensionPath = extension.location.path;
					}

					if (!extensionPath) {
						this.logService.warn(`跳过扩展: ${extensionId}，无法确定扩展路径`);
						notFound.push(extensionId);
						continue;
					}

					// 确保扩展路径存在
					const extensionUri = URI.file(extensionPath);
					if (!(await this.fileService.exists(extensionUri))) {
						this.logService.warn(`跳过扩展: ${extensionId}，路径不存在: ${extensionPath}`);
						notFound.push(extensionId);
						continue;
					}

					// 读取package.json检查许可证
					const packageJsonUri = URI.file(`${extensionPath}/package.json`);
					if (!(await this.fileService.exists(packageJsonUri))) {
						this.logService.warn(`跳过扩展: ${extensionId}，package.json不存在`);
						notFound.push(extensionId);
						continue;
					}

					const packageJsonContent = await this.fileService.readFile(packageJsonUri);
					let packageJson;
					try {
						packageJson = JSON.parse(packageJsonContent.value.toString());
					} catch (error) {
						this.logService.error(`解析扩展package.json失败: ${extensionId}`, error);
						failed.push(extensionId);
						continue;
					}

					// 检查许可证是否是开源的
					const license = packageJson.license;
					if (!this.isOpenSourceLicense(license)) {
						// 检查LICENSE文件是否包含开源许可证信息
						const isOpenSourceByLicenseMd = await this.checkLicenseMdForOpenSource(extensionPath, extensionId);

						// 如果LICENSE文件中没有开源许可证信息，则跳过该扩展
						if (!isOpenSourceByLicenseMd) {
							this.logService.warn(`跳过扩展: ${extensionId}，许可证不是开源的: ${license}`);
							notFound.push(extensionId);
							continue;
						}
					}

					// 复制扩展到当前IDE
					const targetExtensionDir = URI.file(`${currentExtensionsJsonPath}/${extensionId}-${packageJson.version || 'unknown'}`);

					// 确保目标目录不存在
					if (await this.fileService.exists(targetExtensionDir)) {
						await this.fileService.del(targetExtensionDir, { recursive: true });
					}

					// 复制扩展目录
					await this.copyDirectory(extensionUri, targetExtensionDir);

					// 添加到新扩展配置 - 复制原始扩展对象而不是创建新对象
					// 创建原始扩展对象的深拷贝
					const extensionConfig = JSON.parse(JSON.stringify(extension));

					// 更新路径相关的属性
					extensionConfig.path = targetExtensionDir.fsPath;
					if (extensionConfig.location) {
						extensionConfig.location.path = targetExtensionDir.fsPath;
					}

					// 确保关键属性存在
					if (!extensionConfig.identifier) {
						extensionConfig.identifier = {
							id: extensionId
						};
					}

					if (extensionUuid && !extensionConfig.identifier.uuid) {
						extensionConfig.identifier.uuid = extensionUuid;
					}

					// 确保版本信息存在
					if (!extensionConfig.version) {
						extensionConfig.version = packageJson.version || 'unknown';
					}

					// 确保源信息存在
					if (!extensionConfig.source) {
						extensionConfig.source = 'gallery';
					}

					// 添加到新的扩展配置列表
					newExtensionsConfig.push(extensionConfig);

					this.logService.info(`成功导入扩展: ${extensionId}`);
					succeeded.push(extensionId);

				} catch (error) {
					this.logService.error(`导入扩展失败`, error);
					if (extension && extension.identifier && extension.identifier.id) {
						failed.push(extension.identifier.id);
					}
				}
			}

			// 6. 更新当前IDE的extensions.json
			if (newExtensionsConfig.length > 0) {
				const updatedExtensionsData = [...currentExtensionsData, ...newExtensionsConfig];
				await this.fileService.writeFile(
					currentExtensionsJsonUri,
					VSBuffer.fromString(JSON.stringify(updatedExtensionsData, null, 2))
				);
				this.logService.info(`已更新当前IDE的extensions.json，添加了${newExtensionsConfig.length}个扩展配置`);
			}

			// 7. 修复extensions.json中的路径问题
			const fixedCount = await this.fixExtensionsJsonPaths();
			if (fixedCount > 0) {
				this.logService.info(`修复了${fixedCount}个扩展的路径问题`);
			}

			// 8. 过滤掉路径不正确的扩展
			const filteredCount = await this.filterInvalidExtensionPaths();
			if (filteredCount > 0) {
				this.logService.info(`过滤了${filteredCount}个路径不正确的扩展`);
			}

			return { succeeded, failed, notFound };

		} catch (error) {
			this.logService.error('导入扩展过程中发生错误', error);
			return { succeeded, failed, notFound };
		}
	}

	/**
	 * 检查许可证是否是开源许可证
	 * @param license 许可证字符串
	 * @returns 是否是开源许可证
	 */
	private isOpenSourceLicense(license: string): boolean {
		if (!license) {
			// 如果没有许可证信息，默认认为是开源的
			return true;
		}

		// 常见开源许可证列表
		const openSourceLicenses = [
			'mit', 'apache', 'bsd', 'gpl', 'lgpl', 'mozilla', 'eclipse', 'isc',
			'artistic', 'wtfpl', 'unlicense', 'cc0', 'cc-by', 'zlib', 'bsl',
			'0bsd', 'epl', 'ms-pl', 'mpl', 'osl', 'artistic-2.0',
			'agpl', 'cddl', 'cpal', 'lppl', 'cecill', 'json', 'upl',
			'sil-ofl', 'w3c', 'sspl', 'ncsa', 'qpl'
		];

		const lowerCaseLicense = license.toLowerCase();
		return openSourceLicenses.some(osl => lowerCaseLicense.includes(osl));
	}

	/**
	 * 递归复制目录
	 * @param source 源目录URI
	 * @param target 目标目录URI
	 */
	private async copyDirectory(source: URI, target: URI): Promise<void> {
		try {
			// 直接使用fileService的copy方法，可以高效地复制整个目录及其内容
			// copy方法内部实现对不同的文件系统提供程序有优化
			await this.fileService.copy(source, target, true);
			this.logService.info(`成功复制目录: ${source.toString()} -> ${target.toString()}`);
		} catch (error) {
			this.logService.error(`复制目录失败: ${source.toString()} -> ${target.toString()}`, error);
			throw error;
		}
	}

	/**
	 * 一键导入扩展
	 * 自动检测用户安装的VS Code扩展并导入
	 * @returns 导入结果
	 */
	public async oneClickImport(): Promise<IImportResult> {
		try {
			this.logService.info('🚀 开始一键导入VS Code扩展...');

			// 先过滤掉现有的无效扩展配置
			this.logService.info('🛢️ 先清理现有的无效扩展配置...');
			const filteredCount = await this.filterInvalidExtensionPaths();
			if (filteredCount > 0) {
				this.logService.info(`已清理${filteredCount}个无效扩展配置`);
			}

			// 使用进度条显示导入进度
			const result = await this.progressService.withProgress(
				{
					location: ProgressLocation.Notification,
					title: '正在导入配置',
					cancellable: false
				},
				async (progress) => {
					// 使用进度回调来更新进度条
					return this.importExtensions((report) => {
						if (report.increment) {
							progress.report({ increment: report.increment });
						}
						if (report.message) {
							progress.report({ message: report.message });
						}
					});
				}
			);

			// 显示结果
			if (result.succeeded.length > 0) {
				this.logService.info(`✅ 导入成功: ${result.succeeded.length}个扩展`);
			}
			if (result.failed.length > 0) {
				this.logService.error(`❌ 导入失败: ${result.failed.length}个扩展`);

				// 添加失败详情到日志
				if (result.failed.length > 0) {
					this.logService.error(`导入失败的扩展列表: ${result.failed.join(', ')}`);
				}
			}
			if (result.notFound.length > 0) {
				this.logService.warn(`⚠️ 未找到或跳过: ${result.notFound.length}个扩展`);

				// 添加未找到的扩展详情到日志
				if (result.notFound.length > 0 && result.notFound.length < 10) {
					this.logService.warn(`未找到的扩展列表: ${result.notFound.join(', ')}`);
				}
			}

			// 修复extensions.json中的路径问题
			const fixedCount = await this.fixExtensionsJsonPaths();
			if (fixedCount > 0) {
				this.logService.info(`修复了${fixedCount}个扩展的路径问题`);
			}

			// 过滤掉路径不正确的扩展
			const finalFilteredCount = await this.filterInvalidExtensionPaths();
			if (finalFilteredCount > 0) {
				this.logService.info(`过滤了${finalFilteredCount}个路径不正确的扩展`);
			}

			return result;
		} catch (error) {
			this.logService.error('⛔ 一键导入过程中出错', error);
			this.notificationService.notify({
				severity: Severity.Error,
				message: `导入VS Code扩展失败: ${error instanceof Error ? error.message : '未知错误'}`
			});
			return { succeeded: [], failed: [], notFound: [] };
		}
	}

	/**
	 * 从上传的文件导入扩展
	 * @param files 上传的文件列表
	 */
	public async importFromFiles(_files: File[]): Promise<IImportResult> {
		// 暂时不使用
		this.logService.info('🚀 开始从文件导入VS Code扩展...');
		return { succeeded: [], failed: [], notFound: [] };
	}

	/**
	 * 获取VS Code的用户配置目录路径
	 * @returns VS Code用户配置目录路径
	 */
	private async getVSCodeUserConfigPath(): Promise<string> {
		this.logService.info(`获取VS Code用户配置目录路径`);
		this.logService.info(`获取OS: isWindows: ${isWindows} , isMacintosh: ${isMacintosh} , isLinux: ${isLinux}`);
		const homePath = this.environmentService.userHome.fsPath;
		if (isWindows) {
			return `${homePath}\\AppData\\Roaming\\Code\\User`;
		} else if (isMacintosh) {
			return `${homePath}/Library/Application Support/Code/User`;
		} else if (isLinux) {
			return `${homePath}/.config/Code/User`;
		}

		return `${homePath}/Library/Application Support/Code/User`;
	}

	/**
	 * 导入VS Code的设置文件（settings.json）
	 * 如果JoyCoder已有设置，则覆盖现有设置
	 * @param silent 是否不显示通知
	 * @returns 导入结果，成功返回true，失败返回false
	 */
	public async importSettings(silent?: boolean): Promise<boolean> {
		try {
			this.logService.info('🚀 开始导入VS Code设置文件...');

			// 获取VS Code用户配置目录
			const vscodeUserConfigPath = await this.getVSCodeUserConfigPath();
			const vscodeSettingsPath = `${vscodeUserConfigPath}/settings.json`;

			// 获取JoyCoder用户设置文件路径
			const joycoderSettingsPath = this.userDataProfileService.currentProfile.settingsResource;

			this.logService.info(`📂 源文件路径: ${vscodeSettingsPath}`);
			this.logService.info(`📂 目标文件路径: ${joycoderSettingsPath}`);

			// 导入配置文件
			const result = await this.importConfigurationFile(vscodeSettingsPath, joycoderSettingsPath, 'settings.json');

			// 显示结果通知
			if (result.success) {
				if (!silent) {
					this.notificationService.notify({
						severity: Severity.Info,
						message: `成功导入VS Code设置文件`,
						source: 'JoyCode 设置导入'
					});
				}
				this.logService.info('✅ VS Code设置文件导入成功');
			} else {
				if (!silent) {
					this.notificationService.notify({
						severity: Severity.Error,
						message: `导入VS Code设置文件失败: ${result.error}`,
						source: 'JoyCode 设置导入'
					});
				}
				this.logService.error(`❌ VS Code设置文件导入失败: ${result.error}`);
			}

			return result.success;
		} catch (error) {
			this.logService.error('导入VS Code设置文件失败', error);
			if (!silent) {
				this.notificationService.notify({
					severity: Severity.Error,
					message: `导入VS Code设置文件失败: ${error instanceof Error ? error.message : '未知错误'}`,
					source: 'JoyCode 设置导入'
				});
			}
			return false;
		}
	}

	/**
	 * 导入配置文件的通用方法
	 * @param sourcePath 源文件路径
	 * @param targetPath 目标文件路径
	 * @param fileName 文件名，用于日志和通知
	 * @returns 导入结果
	 */
	private async importConfigurationFile(sourcePath: string, targetPath: URI, fileName: string): Promise<IConfigurationImportResult> {
		try {
			// 检查源文件是否存在
			const sourceUri = URI.file(sourcePath);
			const exists = await this.fileService.exists(sourceUri);

			if (!exists) {
				this.logService.warn(`源文件不存在: ${sourcePath}`);
				return {
					success: false,
					fileName,
					sourcePath,
					targetPath: targetPath.toString(),
					error: `找不到源文件: ${sourcePath}`
				};
			}

			// 读取源文件内容
			this.logService.info(`正在读取源文件: ${sourcePath}`);
			const sourceContent = await this.fileService.readFile(sourceUri);
			const content = sourceContent.value.toString();

			const processedContent = content;

			// 创建目标目录（如果不存在）
			const targetDir = dirname(targetPath.fsPath);
			const targetDirUri = URI.file(targetDir);
			const targetDirExists = await this.fileService.exists(targetDirUri);

			if (!targetDirExists) {
				this.logService.info(`创建目标目录: ${targetDir}`);
				await this.fileService.createFolder(targetDirUri);
			}

			// 检查目标文件是否存在，如果存在则先备份
			const targetExists = await this.fileService.exists(targetPath);
			if (targetExists) {
				this.logService.info(`目标文件已存在，创建备份: ${targetPath}`);
				await this.backupConfigurationFile(targetPath, fileName);
			}

			// 写入处理后的内容到目标文件（注意：写入处理后的内容，而不是原始内容）
			this.logService.info(`正在写入目标文件: ${targetPath}`);
			await this.fileService.writeFile(targetPath, VSBuffer.fromString(processedContent));

			this.logService.info(`成功导入${fileName}`);
			return {
				success: true,
				fileName,
				sourcePath,
				targetPath: targetPath.toString()
			};
		} catch (error) {
			this.logService.error(`导入${fileName}失败`, error);
			return {
				success: false,
				fileName,
				sourcePath,
				targetPath: targetPath.toString(),
				error: error instanceof Error ? error.message : '未知错误'
			};
		}
	}

	/**
	 * 从JSON字符串中移除注释行
	 * @param jsonString 原始JSON字符串
	 * @returns 处理后的JSON字符串
	 */
	// private removeCommentsFromJson(jsonString: string): string {
	// 	// 按行分割
	// 	const lines = jsonString.split('\n');
	// 	// 过滤掉以'//'开头的注释行
	// 	const filteredLines = lines.filter(line => !line.trim().startsWith('//'));
	// 	// 重新组合成字符串
	// 	return filteredLines.join('\n');
	// }

	/**
	 * 备份配置文件
	 * @param filePath 要备份的文件路径
	 * @param fileName 文件名，用于日志
	 * @returns 备份文件的路径
	 */
	private async backupConfigurationFile(filePath: URI, fileName: string): Promise<URI | null> {
		try {
			// 读取原文件内容
			const fileContent = await this.fileService.readFile(filePath);

			// 创建备份文件路径
			const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
			const backupFileName = `${fileName}.${timestamp}.backup`;
			const backupPath = URI.file(dirname(filePath.fsPath)).with({
				path: `${dirname(filePath.path)}/${backupFileName}`
			});

			// 写入备份文件
			this.logService.info(`正在备份${fileName}到${backupPath}`);
			await this.fileService.writeFile(backupPath, fileContent.value);

			return backupPath;
		} catch (error) {
			this.logService.error(`备份${fileName}失败`, error);
			return null;
		}
	}

	/**
	 * 导入VS Code的键盘绑定文件（keybindings.json）
	 * 如果JoyCoder已有键盘绑定，则覆盖现有绑定
	 * @param silent 是否不显示通知
	 * @returns 导入结果，成功返回true，失败返回false
	 */
	public async importKeybindings(silent?: boolean): Promise<boolean> {
		try {
			this.logService.info('🚀 开始导入VS Code键盘绑定文件...');

			// 获取VS Code用户配置目录
			const vscodeUserConfigPath = await this.getVSCodeUserConfigPath();
			const vscodeKeybindingsPath = `${vscodeUserConfigPath}/keybindings.json`;

			// 检查源文件是否存在
			const sourceUri = URI.file(vscodeKeybindingsPath);
			const exists = await this.fileService.exists(sourceUri);
			if (!exists) {
				this.logService.warn(`源文件不存在: ${vscodeKeybindingsPath}`);
				return true;
			}

			// 获取JoyCoder键盘绑定文件路径
			const joycoderKeybindingsPath = this.userDataProfileService.currentProfile.keybindingsResource;

			this.logService.info(`📂 源文件路径: ${vscodeKeybindingsPath}`);
			this.logService.info(`📂 目标文件路径: ${joycoderKeybindingsPath}`);

			// 导入配置文件
			const result = await this.importConfigurationFile(vscodeKeybindingsPath, joycoderKeybindingsPath, 'keybindings.json');

			// 显示结果通知
			if (result.success) {
				if (!silent) {
					this.notificationService.notify({
						severity: Severity.Info,
						message: `成功导入VS Code键盘绑定文件`,
						source: 'JoyCode 设置导入'
					});
				}
				this.logService.info('✅ VS Code键盘绑定文件导入成功');
			} else {
				if (!silent) {
					this.notificationService.notify({
						severity: Severity.Error,
						message: `导入VS Code键盘绑定文件失败: ${result.error}`,
						source: 'JoyCode 设置导入'
					});
				}
				this.logService.error(`❌ VS Code键盘绑定文件导入失败: ${result.error}`);
			}

			return result.success;
		} catch (error) {
			this.logService.error('导入VS Code键盘绑定文件失败', error);
			if (!silent) {
				this.notificationService.notify({
					severity: Severity.Error,
					message: `导入VS Code键盘绑定文件失败: ${error instanceof Error ? error.message : '未知错误'}`,
					source: 'JoyCode 设置导入'
				});
			}
			return false;
		}
	}

	/**
	 * 一键导入VS Code的配置文件
	 * 包括settings.json和keybindings.json
	 * @returns 包含每个配置文件导入结果的对象
	 */
	public async importAllConfigurations(): Promise<{ settings: boolean; keybindings: boolean }> {
		this.logService.info('🚀 开始一键导入VS Code配置文件...');

		// 使用进度条显示导入进度
		const result = await this.progressService.withProgress(
			{
				location: ProgressLocation.Notification,
				title: '正在导入VS Code配置文件...',
				source: 'JoyCode 设置导入',
				cancellable: false
			},
			async (progress) => {
				// 设置进度条步骤
				progress.report({ message: '正在导入设置文件...', increment: 0 });

				// 导入设置文件，使用silent=true避免显示单独通知
				const settingsResult = await this.importSettings(true);

				// 更新进度
				progress.report({ message: '正在导入键盘绑定文件...', increment: 50 });

				// 导入键盘绑定文件，使用silent=true避免显示单独通知
				const keybindingsResult = await this.importKeybindings(true);

				// 完成导入
				progress.report({ message: '导入完成', increment: 50 });

				return {
					settings: settingsResult,
					keybindings: keybindingsResult
				};
			}
		);

		// 显示导入结果
		const successCount = (result.settings ? 1 : 0) + (result.keybindings ? 1 : 0);
		const totalCount = 2;

		// 简化通知消息，使用更简洁的格式
		if (successCount === totalCount) {
			this.logService.info('✅ 所有配置文件导入成功');
			this.notificationService.notify({
				severity: Severity.Info,
				message: '成功导入所有VS Code配置',
				source: 'JoyCode'
			});
		} else if (successCount > 0) {
			this.logService.info(`⚠️ 部分配置文件导入成功 (${successCount}/${totalCount})`);

			// 创建简洁的状态消息
			const settingsStatus = result.settings ? '成功' : '失败';
			const keybindingsStatus = result.keybindings ? '成功' : '失败';

			this.notificationService.notify({
				severity: Severity.Warning,
				message: `部分配置导入成功: 设置(${settingsStatus}) 键盘绑定(${keybindingsStatus})`,
				source: 'JoyCode'
			});
		} else {
			this.logService.error('❌ 所有配置文件导入失败');
			this.notificationService.notify({
				severity: Severity.Error,
				message: '导入配置失败',
				source: 'JoyCode'
			});
		}

		return result;
	}

	/**
	 * 检查LICENSE文件是否包含开源许可证信息
	 * @param extensionPath 扩展路径
	 * @param extensionId 扩展ID，用于日志记录
	 * @returns 如果LICENSE文件包含开源许可证信息返回true，否则返回false
	 */
	private async checkLicenseMdForOpenSource(extensionPath: string, extensionId: string): Promise<boolean> {
		// 可能的许可证文件名列表
		const possibleLicenseFiles = [
			`${extensionPath}/LICENSE.md`,
			`${extensionPath}/LICENSE.txt`,
			`${extensionPath}/LICENSE`
		];

		// 遍历可能的许可证文件
		for (const licenseFilePath of possibleLicenseFiles) {
			const licenseFileUri = URI.file(licenseFilePath);

			// 检查文件是否存在
			if (await this.fileService.exists(licenseFileUri)) {
				try {
					// 读取文件内容
					const licenseContent = await this.fileService.readFile(licenseFileUri);
					const content = licenseContent.value.toString();

					// 检查是否含有开源许可证信息
					const licenseName = this.isLicenseFileOpenSource(content);
					if (licenseName) {
						this.logService.info(`扩展: ${extensionId} 的${licenseFilePath.split('/').pop()}文件包含${licenseName}许可证信息，允许导入`);
						return true;
					}
				} catch (error) {
					this.logService.warn(`读取扩展: ${extensionId} 的${licenseFilePath.split('/').pop()}文件失败`, error);
				}
			}
		}

		// 如果没有找到任何包含开源许可证信息的文件
		return false;
	}

	/**
	 * 检查许可证文件内容是否包含常见的开源许可证声明
	 * @param content 许可证文件内容
	 * @returns 如果包含开源许可证返回许可证名称，否则返回null
	 */
	private isLicenseFileOpenSource(content: string): string | null {
		// 获取前几行内容进行检查（取前3行即可，大多数许可证文件的许可证类型都在开头几行声明）
		// const lines = content.split('\n').slice(0, 30);
		const lines = content.split('\n');

		// 逐行检查，避免合并多行可能导致的误判
		for (const line of lines) {
			const trimmedLine = line.trim().toLowerCase();

			// 检查MIT许可证
			if (trimmedLine.includes('mit license')) {
				return 'MIT';
			}

			// 检查Apache许可证
			if (trimmedLine.includes('apache license')) {
				return 'Apache';
			}

			// 检查GPL许可证
			if (trimmedLine.includes('gnu general public license')) {
				return 'GPL';
			}

			// 检查BSD许可证
			if (trimmedLine.includes('bsd license') ||
				trimmedLine.includes('3-clause bsd') ||
				trimmedLine.includes('2-clause bsd')) {
				return 'BSD';
			}
		}

		// 没有找到常见的开源许可证
		return null;
	}

	/**
	 * 移除扩展ID中的平台后缀（如-darwin-arm64、-win32-x64等）
	 * @param extensionId 原始扩展ID
	 * @returns 处理后的扩展ID
	 */
	private removePlatformSuffix(extensionId: string): string {
		// 常见的平台后缀模式
		const platformSuffixes = [
			'-darwin-arm64',
			'-darwin-x64',
			'-darwin',
			'-win32-x64',
			'-win32-ia32',
			'-win32-arm64',
			'-linux-x64',
			'-linux-arm64',
			'-linux-armhf',
			'-alpine',
			'-web'
		];

		// 检查并移除平台后缀
		for (const suffix of platformSuffixes) {
			if (extensionId.endsWith(suffix)) {
				this.logService.info(`从扩展ID中移除平台后缀: ${extensionId} -> ${extensionId.substring(0, extensionId.length - suffix.length)}`);
				return extensionId.substring(0, extensionId.length - suffix.length);
			}
		}

		return extensionId;
	}

	/**
	 * 验证和清理extensions.json，确保其中的每个扩展在extensions目录下都存在
	 * 如果扩展不存在，则从配置中删除该扩展
	 * @returns 清理后的扩展数量
	 */
	public async validateAndCleanExtensionsJson(): Promise<number> {
		try {
			this.logService.info('🔍 开始验证和清理extensions.json...');

			// 1. 读取当前IDE的extensions.json
			const extensionsJsonPath = this.environmentService.extensionsPath;
			const extensionsJsonUri = URI.file(`${extensionsJsonPath}/extensions.json`);

			// 检查文件是否存在
			if (!(await this.fileService.exists(extensionsJsonUri))) {
				this.logService.info('extensions.json文件不存在，无需清理');
				return 0;
			}

			// 2. 读取文件内容并解析JSON
			let extensionsData: any[] = [];
			try {
				const fileContent = await this.fileService.readFile(extensionsJsonUri);
				extensionsData = JSON.parse(fileContent.value.toString());
				if (!Array.isArray(extensionsData)) {
					extensionsData = [];
				}
			} catch (error) {
				this.logService.error('解析extensions.json失败', error);
				return 0;
			}

			if (extensionsData.length === 0) {
				this.logService.info('extensions.json为空，无需清理');
				return 0;
			}

			// 3. 验证每个扩展是否存在
			const validExtensions: any[] = [];
			const invalidExtensions: any[] = [];

			for (const extension of extensionsData) {
				if (!extension || !extension.identifier || !extension.identifier.id) {
					continue;
				}

				const extensionId = extension.identifier.id;
				let extensionExists = false;

				// 检查扩展目录是否存在
				if (extension.path) {
					const extensionPathUri = URI.file(extension.path);
					extensionExists = await this.fileService.exists(extensionPathUri);
				} else {
					// 如果没有path属性，尝试从扩展ID和版本号构建路径
					const version = extension.version || 'unknown';
					const possibleExtensionPath = `${extensionsJsonPath}/${extensionId}-${version}`;
					const possibleExtensionUri = URI.file(possibleExtensionPath);
					extensionExists = await this.fileService.exists(possibleExtensionUri);

					// 如果还是找不到，尝试在目录中查找匹配的扩展文件夹
					if (!extensionExists) {
						try {
							const extensionsDirUri = URI.file(extensionsJsonPath);
							const dirEntries = await this.fileService.resolve(extensionsDirUri, { resolveMetadata: true });

							if (dirEntries.children) {
								for (const entry of dirEntries.children) {
									if (entry.isDirectory && entry.name.startsWith(`${extensionId}-`)) {
										extensionExists = true;
										// 更新扩展路径
										extension.path = entry.resource.fsPath;
										break;
									}
								}
							}
						} catch (error) {
							this.logService.error(`查找扩展目录失败: ${extensionId}`, error);
						}
					}
				}

				if (extensionExists) {
					validExtensions.push(extension);
				} else {
					this.logService.warn(`扩展不存在，将从配置中删除: ${extensionId}`);
					invalidExtensions.push(extension);
				}
			}

			// 4. 如果有无效扩展，更新extensions.json
			if (invalidExtensions.length > 0) {
				this.logService.info(`发现${invalidExtensions.length}个无效扩展，正在更新extensions.json...`);

				// 写入更新后的数据
				await this.fileService.writeFile(
					extensionsJsonUri,
					VSBuffer.fromString(JSON.stringify(validExtensions, null, 2))
				);

				this.logService.info(`成功清理${invalidExtensions.length}个无效扩展`);
				return invalidExtensions.length;
			} else {
				this.logService.info('所有扩展都有效，无需清理');
				return 0;
			}
		} catch (error) {
			this.logService.error('验证和清理extensions.json时出错', error);
			return 0;
		}
	}

	/**
	 * 过滤掉extensions.json中路径不正确的扩展
	 * 如果扩展的location.path不存在或不正确，则从配置中删除该扩展
	 * @returns 过滤掉的扩展数量
	 */
	public async filterInvalidExtensionPaths(): Promise<number> {
		try {
			this.logService.info('🛢️ 开始过滤extensions.json中路径不正确的扩展...');

			// 1. 读取当前IDE的extensions.json
			const extensionsJsonPath = this.environmentService.extensionsPath;
			const extensionsJsonUri = URI.file(`${extensionsJsonPath}/extensions.json`);

			// 检查文件是否存在
			if (!(await this.fileService.exists(extensionsJsonUri))) {
				this.logService.info('extensions.json文件不存在，无需过滤');
				return 0;
			}

			// 2. 读取文件内容并解析JSON
			let extensionsData: any[] = [];
			try {
				const fileContent = await this.fileService.readFile(extensionsJsonUri);
				extensionsData = JSON.parse(fileContent.value.toString());
				if (!Array.isArray(extensionsData)) {
					extensionsData = [];
				}
			} catch (error) {
				this.logService.error('解析extensions.json失败', error);
				return 0;
			}

			if (extensionsData.length === 0) {
				this.logService.info('extensions.json为空，无需过滤');
				return 0;
			}

			// 3. 过滤掉路径不正确的扩展
			const validExtensions: any[] = [];
			const invalidExtensions: any[] = [];

			for (const extension of extensionsData) {
				if (!extension || !extension.identifier || !extension.identifier.id) {
					continue;
				}

				const extensionId = extension.identifier.id;
				let isValid = true;

				// 检查location.path是否存在且有效
				if (extension.location && extension.location.path) {
					const locationPathUri = URI.file(extension.location.path);
					const locationPathExists = await this.fileService.exists(locationPathUri);

					if (!locationPathExists) {
						this.logService.warn(`扩展 ${extensionId} 的location.path不存在: ${extension.location.path}`);
						isValid = false;
					} else {
						// 检查路径是否指向有效的扩展目录
						const packageJsonUri = URI.file(`${extension.location.path}/package.json`);
						const packageJsonExists = await this.fileService.exists(packageJsonUri);

						if (!packageJsonExists) {
							this.logService.warn(`扩展 ${extensionId} 的location.path不是有效的扩展目录，缺少package.json`);
							isValid = false;
						}
					}
				} else {
					// 如果没有location.path，检查path属性
					if (extension.path) {
						const pathUri = URI.file(extension.path);
						const pathExists = await this.fileService.exists(pathUri);

						if (!pathExists) {
							this.logService.warn(`扩展 ${extensionId} 的path不存在: ${extension.path}`);
							isValid = false;
						} else {
							// 检查路径是否指向有效的扩展目录
							const packageJsonUri = URI.file(`${extension.path}/package.json`);
							const packageJsonExists = await this.fileService.exists(packageJsonUri);

							if (!packageJsonExists) {
								this.logService.warn(`扩展 ${extensionId} 的path不是有效的扩展目录，缺少package.json`);
								isValid = false;
							}
						}
					} else {
						// 如果没有path和location.path，则认为无效
						this.logService.warn(`扩展 ${extensionId} 没有有效的路径属性`);
						isValid = false;
					}
				}

				if (isValid) {
					validExtensions.push(extension);
				} else {
					invalidExtensions.push(extension);
				}
			}

			// 4. 如果有无效扩展，更新extensions.json
			if (invalidExtensions.length > 0) {
				this.logService.info(`发现${invalidExtensions.length}个路径不正确的扩展，正在更新extensions.json...`);

				// 写入更新后的数据
				await this.fileService.writeFile(
					extensionsJsonUri,
					VSBuffer.fromString(JSON.stringify(validExtensions, null, 2))
				);

				this.logService.info(`成功过滤${invalidExtensions.length}个路径不正确的扩展`);
				return invalidExtensions.length;
			} else {
				this.logService.info('所有扩展路径都正确，无需过滤');
				return 0;
			}
		} catch (error) {
			this.logService.error('过滤扩展路径时出错', error);
			return 0;
		}
	}

	/**
	 * 修复extensions.json中的路径问题
	 * 确保每个扩展的location.path和relativeLocation与实际目录名称一致
	 * @returns 修复的扩展数量
	 */
	public async fixExtensionsJsonPaths(): Promise<number> {
		try {
			this.logService.info('🔧 开始修复extensions.json中的路径问题...');

			// 1. 读取当前IDE的extensions.json
			const extensionsJsonPath = this.environmentService.extensionsPath;
			const extensionsJsonUri = URI.file(`${extensionsJsonPath}/extensions.json`);

			// 检查文件是否存在
			if (!(await this.fileService.exists(extensionsJsonUri))) {
				this.logService.info('extensions.json文件不存在，无需修复');
				return 0;
			}

			// 2. 读取文件内容并解析JSON
			let extensionsData: any[] = [];
			try {
				const fileContent = await this.fileService.readFile(extensionsJsonUri);
				extensionsData = JSON.parse(fileContent.value.toString());
				if (!Array.isArray(extensionsData)) {
					extensionsData = [];
				}
			} catch (error) {
				this.logService.error('解析extensions.json失败', error);
				return 0;
			}

			if (extensionsData.length === 0) {
				this.logService.info('extensions.json为空，无需修复');
				return 0;
			}

			// 3. 获取扩展目录中的所有扩展文件夹
			const extensionsDirUri = URI.file(extensionsJsonPath);
			let dirEntries: any[] = [];
			try {
				const result = await this.fileService.resolve(extensionsDirUri, { resolveMetadata: true });
				if (result.children) {
					dirEntries = result.children.filter(entry => entry.isDirectory && !entry.name.startsWith('.'));
				}
			} catch (error) {
				this.logService.error('获取扩展目录内容失败', error);
				return 0;
			}

			// 4. 修复每个扩展的路径
			let fixedCount = 0;
			const fixedExtensionsData = extensionsData.map(extension => {
				if (!extension || !extension.identifier || !extension.identifier.id) {
					return extension;
				}

				const extensionId = extension.identifier.id;
				const cleanExtensionId = this.removePlatformSuffix(extensionId);

				// 在目录中查找匹配的扩展文件夹
				const matchingEntry = dirEntries.find(entry =>
					entry.name.startsWith(`${cleanExtensionId}-`) &&
					!entry.name.endsWith('.installing')
				);

				if (matchingEntry) {
					const actualPath = matchingEntry.resource.fsPath;
					const actualDirName = matchingEntry.name;
					let needsFix = false;

					// 检查path属性
					if (extension.path !== actualPath) {
						this.logService.info(`修复扩展 ${extensionId} 的path: ${extension.path || '无'} -> ${actualPath}`);
						extension.path = actualPath;
						needsFix = true;
					}

					// 检查location属性
					if (extension.location) {
						if (extension.location.path !== actualPath) {
							this.logService.info(`修复扩展 ${extensionId} 的location.path: ${extension.location.path || '无'} -> ${actualPath}`);
							extension.location.path = actualPath;
							needsFix = true;
						}
					} else {
						this.logService.info(`为扩展 ${extensionId} 添加location属性`);
						extension.location = {
							$mid: 1,
							path: actualPath,
							scheme: 'file'
						};
						needsFix = true;
					}

					// 检查relativeLocation属性
					if (extension.relativeLocation !== actualDirName) {
						this.logService.info(`修复扩展 ${extensionId} 的relativeLocation: ${extension.relativeLocation || '无'} -> ${actualDirName}`);
						extension.relativeLocation = actualDirName;
						needsFix = true;
					}

					if (needsFix) {
						fixedCount++;
					}
				}

				return extension;
			});

			// 5. 如果有修复，写回文件
			if (fixedCount > 0) {
				await this.fileService.writeFile(
					extensionsJsonUri,
					VSBuffer.fromString(JSON.stringify(fixedExtensionsData, null, 2))
				);
				this.logService.info(`成功修复${fixedCount}个扩展的路径问题`);
			} else {
				this.logService.info('所有扩展路径都正确，无需修复');
			}

			return fixedCount;
		} catch (error) {
			this.logService.error('修复extensions.json路径时出错', error);
			return 0;
		}
	}
}
// 注册扩展导入服务
registerSingleton(IExtensionImportService, ExtensionImportService, InstantiationType.Eager);

