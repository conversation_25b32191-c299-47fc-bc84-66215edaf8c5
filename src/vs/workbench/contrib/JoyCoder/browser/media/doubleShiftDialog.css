/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.double-shift-dialog-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 30vh; /* 距离顶部30% */
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 2500;
}

.double-shift-dialog-container {
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    overflow: hidden;
    max-width: 90%;
    max-height: 90%;
    box-sizing: border-box;
    padding: 16px;
}

.double-shift-dialog-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(128, 128, 128, 0.3);
}

.double-shift-dialog-content {
    flex: 1;
    overflow: auto;
    margin-bottom: 16px;
    line-height: 1.5;
}

.double-shift-dialog-search-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.double-shift-dialog-search-input-container {
    display: flex;
    margin-bottom: 10px;
    width: 100%;
}

.double-shift-dialog-search-results {
    flex: 1;
    overflow: auto;
    border: 1px solid var(--vscode-input-border, rgba(128, 128, 128, 0.5));
    background-color: var(--vscode-editor-background);
    border-radius: 2px;
    padding: 8px;
}

.search-loading {
    padding: 8px;
    color: var(--vscode-foreground);
    font-style: italic;
}

.no-search-results {
    padding: 8px;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
}

/* 全局 loading 动画和按钮样式，供 globalLoadingOverlay 复用 */
.joycoder-global-loading-spinner {
    width: 48px;
    height: 48px;
    border: 4px solid var(--vscode-progressBar-background,#0078d4);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: joycoder-spin 1s linear infinite;
    margin-bottom: 18px;
}
@keyframes joycoder-spin {
    0% { transform: rotate(0deg);}
    100% { transform: rotate(360deg);}
}
.joycoder-loading-close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: none;
    border: none;
    color: var(--vscode-editor-foreground,#555);
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    opacity: 0.7;
    transition: opacity 0.2s;
}
.joycoder-loading-close-btn:hover {
    opacity: 1;
}
.search-error {
    padding: 8px;
    color: var(--vscode-errorForeground);
}

.search-results-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.search-result-item {
    padding: 6px 8px;
    cursor: pointer;
    border-radius: 2px;
    display: flex;
    align-items: center;
}

.search-result-item:hover {
    background-color: var(--vscode-list-hoverBackground);
}

.search-result-item:before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 6px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path fill="%23C5C5C5" d="M13.71 4.29l-3-3L10 2H4L3 3v10l1 1h9l1-1V5l-.29-.71zM13 13H4V3h5v3h4v7z"/></svg>');
    background-repeat: no-repeat;
    background-position: center;
}

.double-shift-dialog-search-input {
    flex: 1;
    height: 30px;
    padding: 4px 8px;
    border: 1px solid var(--vscode-input-border, rgba(128, 128, 128, 0.5));
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    border-radius: 2px;
    outline: none;
    font-size: 13px;
}

.double-shift-dialog-search-input:focus {
    border-color: var(--vscode-focusBorder);
}

.double-shift-dialog-search-hint {
    margin-left: 8px;
    color: var(--vscode-descriptionForeground);
    font-size: 12px;
    align-self: center;
    font-style: italic;
}

/* 按钮容器和按钮已移除 */
