/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/
/* 移除所有自定义色变量，全部用 VSCode 官方主题变量 */
@font-face {
	font-family: "iconfontwork";
	font-display: block;
	src: url("./iconfont.ttf?sdfs2sd98f7932h4iusy823hskdhf8") format("truetype");
}
.joyicon[class*='joyicon-'] {
	font: normal normal normal 16px/1 iconfontwork;
	display: inline-block;
	text-decoration: none;
	text-rendering: auto;
	text-align: center;
	text-transform: none;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	user-select: none;
	-webkit-user-select: none;
}
.joyicon-xiajiantoutianchong:before {
	content: "\e725";
}

.joyicon-file:before {
	content: "\e712";
}
.joyicon-file-add:before {
	content: "\e72b";
}
.joyicon-file-manage:before {
	content: "\e765";
}

.joyicon-git-branch:before {
	content: "\e749";
}
.joyicon-remote-explorer:before {
	content: "\e756";
}


/**/
/* 项目选择器容器 */
.project-selector-container {
    display: flex;
    align-items: center;
    height: 100%;
    margin-right: 10px;
    position: relative; /* 确保定位上下文 */
    z-index: 2500; /* 提高z-index，确保在拖动区域之上 */
    -webkit-app-region: no-drag; /* 关键属性：告诉浏览器这个区域不用于拖动窗口 */
}

/* 项目选择器 */
.project-selector {
    display: flex;
    align-items: center;
    height: 22px;
    padding: 0 8px;
    margin-right: 8px;
    margin-left: 0; /* 重置左边距 */
    cursor: pointer !important; /* 强制使用指针光标 */
    border-radius: 3px;
    /*color: var(--vscode-titleBar-activeForeground);*/
    color: var(--vscode-titleBar-activeForeground);
    background-color: var(--vscode-titleBar-activeBackground);
    transition: background-color 0.1s ease;
    user-select: none;
    white-space: nowrap;
    max-width: 200px;
    position: relative; /* 确保定位上下文 */
    z-index: 2500; /* 提高z-index，确保在拖动区域之上 */
    pointer-events: auto !important; /* 强制启用指针事件 */
    -webkit-app-region: no-drag; /* 关键属性：告诉浏览器这个区域不用于拖动窗口 */
    outline-width: 0;
}
.project-selector .color-icon {
    font-size: 10px;
}

/* 自定义添加的标题栏容器 */
.titlebar-container.custom-added {
    background-color: var(--vscode-titleBar-activeBackground);
    color: var(--vscode-titleBar-activeForeground);
    /* border-bottom: 1px solid var(--vscode-titleBar-border, transparent); */
}

/* 自定义添加的标题栏左侧容器 */
.titlebar-left.custom-added {
    display: flex;
    align-items: center;
    height: 100%;
    padding-left: 10px;
}

/* 确保在全屏模式下也能显示项目选择器 */
.monaco-workbench.fullscreen .part.titlebar > .titlebar-container > .titlebar-left {
    width: 0px !important;
    min-width: 0px !important;
}

/* 确保在全屏模式下项目选择器也能正常显示 */
.monaco-workbench.fullscreen .project-selector {
    display: flex !important;
    visibility: visible !important;
}

/* 确保在全屏模式下项目选择器容器也能正常显示 */
.monaco-workbench.fullscreen .project-selector-container {
    display: flex !important;
    visibility: visible !important;
}

.monaco-workbench div.project-selector:hover {
    background-color: var(--vscode-menu-selectionBackground, #202023) !important;
    color: var(--vscode-menu-selectionForeground, #fff) !important;
}
.monaco-workbench div.project-selector:focus {
    outline-width: 0;
    background-color: var(--vscode-menu-selectionBackground, #202023) !important;
    color: var(--vscode-menu-selectionForeground, #fff) !important;
}

.project-selector .project-icon {
    display: flex;
    align-items: center;
    margin-right: 4px;
    color: var(--vscode-titleBar-activeForeground);
}

.project-selector .project-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px;
}

.project-selector .dropdown-icon {
    display: flex;
    align-items: center;
    margin-left: 4px;
    /*color: var(--vscode-titleBar-activeForeground);*/
    color: var(--vscode-titleBar-activeForeground);
}

/* 非活动窗口样式 */
.monaco-workbench .part.titlebar.inactive .project-selector {
    color: var(--vscode-titleBar-inactiveForeground);
}

.monaco-workbench .part.titlebar.inactive .project-selector .project-icon,
.monaco-workbench .part.titlebar.inactive .project-selector .dropdown-icon {
    color: var(--vscode-titleBar-inactiveForeground);
}

/* 项目选择器菜单 */
.project-selector-menu {
    position: absolute;
    z-index: 9999 !important;
    pointer-events: auto !important;
    background-color: var(--vscode-editorWidget-background);
    color: var(--vscode-editorWidget-foreground);
    box-shadow: 0 2px 8px var(--vscode-widget-shadow, rgba(0, 0, 0, 0.36));
    border-radius: 6px;
    padding: 6px 0;
    min-width: 300px;
    max-width: 500px;
    overflow-y: auto;
    max-height: 500px;
    -webkit-app-region: no-drag;
    font-size: 12px;
    border: 1px solid var(--vscode-widget-border);
}

/* 菜单项 */
.project-selector-menu .menu-item {
    display: flex;
    align-items: center;
    padding: 0 8px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
		border-radius: 4px;
    transition: background-color 0.1s ease, color 0.1s ease;
}

/* 悬停状态 */
.project-selector-menu .menu-item:hover,
.project-selector-menu .menu-item.hover {
    background-color: var(--vscode-menu-selectionBackground, #202023) !important;
    color: var(--vscode-menu-selectionForeground, #fff) !important;
}

/* 确保图标颜色也会改变 */
.project-selector-menu .menu-item:hover .codicon,
.project-selector-menu .menu-item.hover .codicon {
    color: var(--vscode-menu-selectionForeground, #ffffff) !important;
}

/* 菜单分隔符 */
.project-selector-menu .menu-separator {
    height: 1px;
    margin: 4px 0;
    background-color: var(--vscode-widget-border);
}

/* 菜单标题 */
.project-selector-menu .menu-section-title {
    padding: 0 8px;
    font-size: 12px;
    font-weight: 600;
    color: var(--vscode-descriptionForeground, #888);
		line-height: 25px;
}

/* 操作菜单项 */
.project-selector-menu .menu-item.action-item {
    height: 24px;
}

/* 最近项目菜单项 */
.project-selector-menu .menu-item.recent-item {
    height: 52px;
    padding: 8px;
		box-sizing: border-box;
}

/* 彩色图标 */
.project-selector-menu .color-icon {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--vscode-editorWidget-foreground, #333);
    font-size: 10px;
}

/* 确保路径颜色也会改变 */
/*.project-selector-menu .menu-item:hover .item-middle div:last-child,*/
/*.project-selector-menu .menu-item.hover .item-middle div:last-child {*/
/*    color: var(--vscode-menu-selectionForeground, #ffffff) !important;*/
/*}*/

/* 滚动条样式处理 */
.project-selector-menu::-webkit-scrollbar {
	background: transparent;
	width: 6px;
}
.project-selector-menu::-webkit-scrollbar-thumb:hover {
	background: transparent;
}
.project-selector-menu:hover::-webkit-scrollbar-thumb {
	background: var(--vscode-list-hoverBackground);
}
.project-selector-menu::-webkit-scrollbar-thumb {
	background: transparent;
	border-radius: 50px;
}
