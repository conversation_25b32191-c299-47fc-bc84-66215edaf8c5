/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { URI } from '../../../../base/common/uri.js';
import { EditorInput } from '../../../../workbench/common/editor/editorInput.js';
import { localize } from '../../../../nls.js';

/**
 * 欢迎界面的编辑器输入类
 * 用于标识和配置欢迎界面在编辑器中的显示
 */
export class JoyCoderWelcomeInput extends EditorInput {

	static readonly ID = 'workbench.input.joyCoderWelcome';

	// 定义欢迎界面的资源URI
	private readonly _resource = URI.from({
		scheme: 'joycoder',
		path: 'welcome'
	});

	/**
	 * 获取输入类型标识
	 */
	override get typeId(): string {
		return JoyCoderWelcomeInput.ID;
	}

	/**
	 * 获取资源URI
	 */
	override get resource(): URI {
		return this._resource;
	}

	/**
	 * 获取显示名称
	 */
	override getName(): string {
		return localize('welcomeInputName', "JoyCode 欢迎指南");
	}

	/**
	 * 检查是否匹配另一个输入
	 */
	override matches(otherInput: unknown): boolean {
		return otherInput instanceof JoyCoderWelcomeInput;
	}

	/**
	 * 创建输入的副本
	 */
	override copy(): JoyCoderWelcomeInput {
		return this;
	}
}
