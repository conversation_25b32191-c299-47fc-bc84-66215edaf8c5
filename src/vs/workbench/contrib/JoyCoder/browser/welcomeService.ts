/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable, IDisposable, toDisposable } from '../../../../base/common/lifecycle.js';
import { IWorkbenchContribution } from '../../../common/contributions.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { LifecyclePhase } from '../../../../workbench/services/lifecycle/common/lifecycle.js';
import { ILifecycleService } from '../../../services/lifecycle/common/lifecycle.js';
import { IWorkbenchLayoutService } from '../../../services/layout/browser/layoutService.js';
import { mountWelcomePage } from './react/out/welcome-tsx/index.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';

// 存储键，用于跟踪欢迎界面是否已经显示过
const WELCOME_SHOWN_KEY = 'JoyCode.welcomeGuideShown';

// 配置键，用于控制欢迎页面的行为
const CONFIG_WELCOME_MODE = 'joycoder.welcome.mode'; // 可能的值: 'always', 'firstLaunch', 'never'

// 静态初始化器 - 使用模块级别的常量和闭包来隔离全局状态
const WelcomeInitializer = new class {
	// 跟踪是否已初始化
	private initialized = false;
	// 存储DOM元素引用
	private backdropElement: HTMLElement | null = null;
	private containerElement: HTMLElement | null = null;

	constructor() {
		this.initialize();
	}

	/**
	 * 获取已初始化的元素引用
	 */
	getElements(): { backdrop: HTMLElement | null, container: HTMLElement | null } {
		return {
			backdrop: this.backdropElement,
			container: this.containerElement
		};
	}

	/**
	 * 清理元素引用
	 */
	clearElements(): void {
		this.backdropElement = null;
		this.containerElement = null;
	}

	/**
	 * 初始化欢迎页面元素
	 */
	private initialize(): void {
		if (this.initialized) {
			return;
		}

		this.initialized = true;
		this.injectStyles();
		this.setupDOMListeners();
	}

	/**
	 * 注入必要的CSS样式
	 */
	private injectStyles(): void {
		try {
			const style = document.createElement('style');
			style.setAttribute('id', 'joycoder-welcome-styles');
			style.textContent = `
				.joycoder-welcome-backdrop {
					position: fixed;
					top: 0;
					left: 0;
					width: 100vw;
					height: 100vh;
					background-color: rgba(0, 0, 0, 0.95);
					backdrop-filter: blur(12px);
					z-index: 99999;
					opacity: 1;
					transition: opacity 0.3s ease;
				}
				.joycoder-welcome-fullscreen-container {
					position: fixed;
					top: 0;
					left: 0;
					width: 100vw;
					height: 100vh;
					z-index: 100000;
					opacity: 1;
					transition: opacity 0.3s ease;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			`;

			document.head.appendChild(style);
		} catch (error) {
			console.error('注入欢迎页面样式失败', error);
		}
	}

	/**
	 * 设置DOM事件监听器
	 */
	private setupDOMListeners(): void {
		const createElements = () => {
			// 避免重复创建
			if (document.querySelector('.joycoder-welcome-backdrop')) {
				return;
			}

			try {
				// 创建背景遮罩
				const backdrop = document.createElement('div');
				backdrop.className = 'joycoder-welcome-backdrop';
				document.body.appendChild(backdrop);
				this.backdropElement = backdrop;

				// 创建容器
				const container = document.createElement('div');
				container.className = 'joycoder-welcome-fullscreen-container';
				document.body.appendChild(container);
				this.containerElement = container;
			} catch (error) {
				console.error('创建欢迎页面元素失败', error);
			}
		};

		// 尽早创建元素
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', createElements, { once: true });
		} else {
			createElements();
		}
	}
}();

// 欢迎界面服务接口
export interface IJoyCoderWelcomeService {
	readonly _serviceBrand: undefined;

	// 判断是否应该显示欢迎界面
	shouldShowWelcome(): boolean;

	// 手动打开欢迎界面
	openWelcomePage(): Promise<void>;

	// 关闭欢迎界面
	closeWelcomePage(): Promise<void>;

	// 标记欢迎界面已经显示过
	markWelcomeAsShown(): void;
}

// 服务ID
const WELCOME_SERVICE_ID = 'joyCoderWelcomeService';
export const IJoyCoderWelcomeService = createDecorator<IJoyCoderWelcomeService>(WELCOME_SERVICE_ID);

// 欢迎界面服务实现
export class JoyCoderWelcomeService extends Disposable implements IJoyCoderWelcomeService {
	_serviceBrand: undefined;

	// 欢迎页面容器
	private welcomeContainer: HTMLElement | undefined;
	// 背景遮罩容器
	private backdropContainer: HTMLElement | undefined;
	// 资源处理
	private welcomeComponentDisposables: IDisposable[] = [];
	// 页面是否正在加载中
	private isLoading: boolean = false;
	// 控制是否已经显示过欢迎页面的标志
	public static hasDisplayedWelcome: boolean = false;

	constructor(
		@IStorageService private readonly storageService: IStorageService,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
		@IWorkbenchLayoutService private readonly layoutService: IWorkbenchLayoutService,
		@IConfigurationService private readonly configurationService: IConfigurationService
	) {
		super();

		// 获取静态初始化器中的元素
		const elements = WelcomeInitializer.getElements();

		if (elements.backdrop && elements.container) {
			this.backdropContainer = elements.backdrop;
			this.welcomeContainer = elements.container;

			// 设置初始化标记
			JoyCoderWelcomeService.hasDisplayedWelcome = true;

			// 处理欢迎页内容
			this.initializeWelcomeContent();
		}

		// 注册服务销毁时的清理逻辑
		this._register(toDisposable(() => {
			this.cleanupWelcomePage();
		}));
	}

	/**
	 * 初始化欢迎页内容
	 */
	private initializeWelcomeContent(forceShow: boolean = false): void {
		// 如果不应该显示欢迎页且不是强制显示，直接关闭并返回
		if (!forceShow && !this.shouldShowWelcome()) {
			this.closeWelcomePage().catch(error => {
				console.error('关闭欢迎页面失败', error);
			});
			return;
		}

		// 确保有容器
		if (!this.welcomeContainer) {
			return;
		}

		try {
			// 先清理之前的组件资源
			this.disposeWelcomeComponents();

			// 使用instantiationService挂载React组件
			this.instantiationService.invokeFunction(accessor => {
				// 挂载React组件并传递关闭回调
				const componentDisposables = mountWelcomePage(this.welcomeContainer!, accessor, () => {
					this.closeWelcomePage().catch(error => {
						console.error('关闭欢迎页面失败', error);
					});
				});

				// 收集disposables
				if (componentDisposables && Array.isArray(componentDisposables)) {
					this.welcomeComponentDisposables.push(...componentDisposables);
				}
			});
		} catch (error) {
			console.error('初始化欢迎页面内容失败', error);
			// 失败时尝试清理
			this.closeWelcomePage().catch(() => {
				// 忽略关闭错误
			});
		}
	}

	/**
	 * 清理欢迎组件资源
	 */
	private disposeWelcomeComponents(): void {
		if (this.welcomeComponentDisposables.length > 0) {
			this.welcomeComponentDisposables.forEach(d => d.dispose());
			this.welcomeComponentDisposables = [];
		}
	}

	shouldShowWelcome(): boolean {
		// 首先检查是否有开发模式标志
		// 使用全局变量或安全检测方式，避免直接使用process.env
		const devMode = typeof (window as any).__JOYCODER_DEV_MODE__ !== 'undefined' ?
			(window as any).__JOYCODER_DEV_MODE__ === true : false;

		if (devMode) {
			return true; // 开发模式下始终显示
		}

		// 获取用户配置
		// TODO 目前开发模式下，默认显示欢迎页面
		const welcomeMode = this.configurationService.getValue<string>(CONFIG_WELCOME_MODE) || 'firstLaunch';
		// 如果配置为 'never'，则永不显示
		if (welcomeMode === 'never') {
			return false;
		}

		// 如果配置为 'always'，则总是显示
		if (welcomeMode === 'always') {
			return true;
		}

		// 默认是 'firstLaunch' 模式，只在首次启动显示
		return !this.storageService.getBoolean(WELCOME_SHOWN_KEY, StorageScope.PROFILE, false);
	}

	async openWelcomePage(): Promise<void> {
		// 避免重复打开或在加载中重复尝试
		if (this.isLoading || this.welcomeContainer) {
			return;
		}

		this.isLoading = true;

		try {
			// 创建欢迎页面
			this.createFullScreenWelcomePage();
		} catch (error) {
			console.error('打开欢迎页面失败', error);
			await this.cleanupWelcomePage();
		} finally {
			this.isLoading = false;
		}
	}

	/**
	 * 创建全屏欢迎页面
	 */
	private createFullScreenWelcomePage(): void {
		// 已有容器则直接返回
		if (this.welcomeContainer) {
			return;
		}

		// 创建背景遮罩
		this.backdropContainer = document.createElement('div');
		this.backdropContainer.className = 'joycoder-welcome-backdrop';
		document.body.appendChild(this.backdropContainer);

		// 创建欢迎页容器
		this.welcomeContainer = document.createElement('div');
		this.welcomeContainer.className = 'joycoder-welcome-fullscreen-container';
		document.body.appendChild(this.welcomeContainer);

		// 通过手动调用openWelcomePage时，无条件初始化内容
		this.welcomeContainer.dataset.forcedOpen = 'true';
		this.initializeWelcomeContent(true);
	}

	async closeWelcomePage(): Promise<void> {
		// 先淡出内容
		if (this.welcomeContainer) {
			this.welcomeContainer.style.opacity = '0';
		}
		if (this.backdropContainer) {
			this.backdropContainer.style.opacity = '0';
		}

		try {
			// 等待动画完成
			await new Promise(resolve => setTimeout(resolve, 300));
			await this.cleanupWelcomePage();
		} catch (error) {
			console.error('关闭欢迎页面过程中出错', error);
			// 强制清理
			this.cleanupWelcomePage();
		}
	}

	/**
	 * 清理欢迎页面资源
	 */
	private async cleanupWelcomePage(): Promise<void> {
		try {
			// 先清理组件资源
			this.disposeWelcomeComponents();

			// 移除DOM元素
			if (this.welcomeContainer && this.welcomeContainer.parentNode) {
				this.welcomeContainer.parentNode.removeChild(this.welcomeContainer);
			}

			if (this.backdropContainer && this.backdropContainer.parentNode) {
				this.backdropContainer.parentNode.removeChild(this.backdropContainer);
			}

			// 重置引用
			this.welcomeContainer = undefined;
			this.backdropContainer = undefined;

			// 清理静态引用
			WelcomeInitializer.clearElements();

			// 通知布局服务重新布局
			this.layoutService.layout();

			// 标记已显示
			this.markWelcomeAsShown();
		} catch (error) {
			console.error('清理欢迎页面资源失败', error);
		}
	}

	markWelcomeAsShown(): void {
		this.storageService.store(WELCOME_SHOWN_KEY, true, StorageScope.PROFILE, StorageTarget.USER);
	}
}

// 注册欢迎服务为单例服务
registerSingleton(IJoyCoderWelcomeService, JoyCoderWelcomeService, InstantiationType.Eager);

// 欢迎界面贡献，作为备份机制
export class JoyCoderWelcomeContribution extends Disposable implements IWorkbenchContribution {
	constructor(
		@IJoyCoderWelcomeService private readonly welcomeService: IJoyCoderWelcomeService,
		@ILifecycleService lifecycleService: ILifecycleService
	) {
		super();

		// 作为备份机制，在Starting阶段检查是否需要显示欢迎页
		lifecycleService.when(LifecyclePhase.Starting).then(() => {
			this.checkAndShowWelcomePage();
		});

		// 如果Starting阶段未成功，尝试在Ready阶段再次显示
		// lifecycleService.when(LifecyclePhase.Ready).then(() => {
		// 	// 检查欢迎页是否已经显示
		// 	if (!JoyCoderWelcomeService.hasDisplayedWelcome && this.welcomeService.shouldShowWelcome()) {
		// 		this.welcomeService.openWelcomePage().catch(error => {
		// 			console.error('在Ready阶段打开欢迎页面失败:', error);
		// 		});
		// 	}
		// });
	}

	/**
	 * 检查并显示欢迎页面
	 */
	private checkAndShowWelcomePage(): void {
		// 检查是否已经显示以及是否应该显示
		if (!JoyCoderWelcomeService.hasDisplayedWelcome && this.welcomeService.shouldShowWelcome()) {
			// 如果静态初始化器没有创建元素，尝试通过服务创建
			const elements = WelcomeInitializer.getElements();
			if (!elements.backdrop || !elements.container) {
				// this.welcomeService.openWelcomePage().catch(error => {
				// 	console.error('在Starting阶段打开欢迎页面失败:', error);
				// });
			}
		}
	}
}

// 添加默认设置贡献
export const DEFAULT_SETTINGS = {
	[CONFIG_WELCOME_MODE]: {
		type: 'string',
		enum: ['firstLaunch', 'always', 'never'],
		default: 'firstLaunch',
		description: '欢迎引导页显示模式。可选值: firstLaunch (仅首次启动显示)、always (每次都显示)、never (从不显示)'
	}
};

/**
 * 开发工具：为浏览器设置开发模式标志
 * 在开发环境中，可以在控制台调用此函数来强制显示欢迎页面
 */
export function setDevelopmentMode(enabled: boolean = true): void {
	try {
		(window as any).__JOYCODER_DEV_MODE__ = enabled;
		console.log(`JoyCoder开发模式${enabled ? '已启用' : '已禁用'}，重启IDE后生效`);
	} catch (e) {
		console.error('设置开发模式失败', e);
	}
}
