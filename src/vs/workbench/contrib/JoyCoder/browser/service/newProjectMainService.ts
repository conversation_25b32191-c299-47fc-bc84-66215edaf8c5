import { INewProjectMainService } from '../../common/INewProjectMainService.js';
import { Event } from '../../../../../base/common/event.js';
import { registerSingleton, InstantiationType } from '../../../../../platform/instantiation/common/extensions.js';
import { IChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { NewProjectChannelClient } from '../../common/newProjectIpc.js';
import { IMainProcessService } from '../../../../../platform/ipc/common/mainProcessService.js';
import { IRes } from '../../common/newProject.js';
import { ILogService } from '../../../../../platform/log/common/log.js';

class NewProjectMainServiceImpl implements INewProjectMainService {
	readonly _serviceBrand: undefined; // 声明一个只读属性，用于标识服务品牌
	private readonly client: INewProjectMainService; // 声明一个只读的客户端属性，类型为INewProjectMainService

	constructor(
		@IMainProcessService mainProcessService: IMainProcessService,// 构造函数参数，主进程服务实例
		@ILogService private readonly logService: ILogService
	) {
		const channel: IChannel = mainProcessService.getChannel('joycoderNewProject'); // 通过主进程服务获取名为'joycoderNewProject'的通信通道
		this.client = new NewProjectChannelClient(channel, logService); // 使用获取到的通道实例化NewProjectChannelClient并赋值给client
	}

	get onDidChangeLoginStatus(): Event<boolean> { // 获取登录状态变化事件
		return this.client.onDidChangeLoginStatus; // 返回客户端的登录状态变化事件
	}

	async createProject(params: any): Promise<IRes> { // 异步方法，用于创建新项目
		this.logService.info('createP2', params);
		return this.client.createProject(params); // 调用客户端的createProject方法，并返回其结果
	}

	async getTemplateList(params: any): Promise<IRes> {
		return this.client.getTemplateList(params);
	}
	async getProjectInfo(data: { projectId: number, ptKey: string }): Promise<IRes> {
		return this.client.getProjectInfo(data);
	}

	async getProjectSSHConfig(data: { projectId: number, ptKey: string }): Promise<IRes> {
		return this.client.getProjectSSHConfig(data);
	}

	async getProjectList(ptKey: string): Promise<IRes> {
		return this.client.getProjectList(ptKey);
	}

	async getProjectDatabaseList(ptKey: string): Promise<IRes> {
		return this.client.getProjectDatabaseList(ptKey);
	}
	async setProjectInfo(data: { params: any, ptKey: string }): Promise<IRes> {
		return this.client.setProjectInfo(data);
	}
	async setProjectAppliedTem(data: { params: any, ptKey: string }): Promise<IRes> {
		return this.client.setProjectAppliedTem(data);
	}
}

registerSingleton(INewProjectMainService, NewProjectMainServiceImpl, InstantiationType.Delayed);
