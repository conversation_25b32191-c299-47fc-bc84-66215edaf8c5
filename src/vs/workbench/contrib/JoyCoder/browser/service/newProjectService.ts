/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

// import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { IProgressService, ProgressLocation } from '../../../../../platform/progress/common/progress.js';
import { URI } from '../../../../../base/common/uri.js';
import { CancellationToken } from '../../../../../base/common/cancellation.js';
import { localize } from '../../../../../nls.js';
import { GET_LOGIN_INFO_COMMAND_ID } from '../loginAction.js';
import { ITerminalService, ITerminalInstance } from '../../../terminal/browser/terminal.js';
import * as path from '../../../../../base/common/path.js';
import { registerSingleton, InstantiationType } from '../../../../../platform/instantiation/common/extensions.js';
import { IConfigurationService } from '../../../../../platform/configuration/common/configuration.js';
import { FILES_EXCLUDE_CONFIG } from '../../../../../platform/files/common/files.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../../platform/storage/common/storage.js';
import { IWorkbenchEnvironmentService } from '../../../../../workbench/services/environment/common/environmentService.js';
import { IPathService } from '../../../../../workbench/services/path/common/pathService.js';
import { isWindows, isMacintosh } from '../../../../../base/common/platform.js';
import { downloadWithCommandLine, extractWithTerminal } from '../project/templateDownloaderUtils.js';
import { getFileExtensionFromUrl, isValidDownloadUrl } from '../project/templateDownloaderHelpers.js'
import { INewProjectMainService } from '../../common/INewProjectMainService.js';
import { INewProjectService, ITemplateListParams, ITemplateInfo, INewProjectParams, IRes, ITemplateDownloadInfo, ISSHInfo } from '../../common/newProject.js';

/**
 * 新建项目服务实现
 */
class WebNewProjectService implements INewProjectService {
    readonly _serviceBrand: undefined;

    // 存储创建的终端实例，以便后续使用
    private _terminal!: ITerminalInstance;

    constructor(
        @IFileService private readonly fileService: IFileService,
        @INotificationService private readonly notificationService: INotificationService,
        @ILogService private readonly logService: ILogService,
        @ICommandService private readonly commandService: ICommandService,
        @IProgressService private readonly progressService: IProgressService,
        @ITerminalService private readonly terminalService: ITerminalService,
        @IConfigurationService private readonly configurationService: IConfigurationService,
        @IStorageService private readonly storageService: IStorageService,
        @IWorkbenchEnvironmentService private readonly environmentService: IWorkbenchEnvironmentService,
        @IPathService private readonly pathService: IPathService,
        @INewProjectMainService private readonly newProjectMainService: INewProjectMainService,
    ) { }

    /**
     * 获取ptKey（登录态）
     */
    private async getPtKey(): Promise<string> {
        let ptKey = '';
        try {
            // 使用命令服务获取登录信息
            const loginInfo = await this.commandService.executeCommand(GET_LOGIN_INFO_COMMAND_ID);
            if (loginInfo && loginInfo.pt_key) {
                ptKey = loginInfo.pt_key;
                this.logService.debug('通过命令获取到ptKey: 成功');
            } else {
                this.logService.warn('通过命令获取ptKey失败: 未登录或ptKey为空');
            }
        } catch (error) {
            this.logService.error('通过命令获取ptKey失败:', error);

            // 作为备选方案，尝试从localStorage获取
            try {
                const userInfoStr = localStorage.getItem('userInfo');
                if (userInfoStr) {
                    const userInfo = JSON.parse(userInfoStr);
                    ptKey = userInfo.ptKey || '';
                    this.logService.debug('从localStorage获取到ptKey:', ptKey ? '成功' : '失败');
                }
            } catch (localStorageError) {
                this.logService.error('从localStorage获取ptKey失败:', localStorageError);
            }
        }
        if (!ptKey) {
            this.notificationService.error(localize('noLogin', "请先登录"));
        }
        return ptKey;
    }

    /**
     * 获取模板列表
     * @param params 请求参数
     * @param token 取消令牌
     * @returns 模板列表
     */
    async getTemplateList(params: ITemplateListParams = { templateName: '' }): Promise<ITemplateInfo[]> {
        try {
            // 获取ptKey
            const ptKey = await this.getPtKey();
            // 检查是否取消
            if (!ptKey) {
                return [];
            }
            const res = await this.newProjectMainService.getTemplateList({ params, ptKey });
            console.log('getTemplateList', res)
            if (res.code === 0) {
                return res?.data || []
            } else {
                this.notificationService.error(localize('templateFetchError', "获取模板数据失败: {0}", res.msg || '未知错误'));
            }
            return [];
        } catch (error) {
            this.logService.error('获取模板数据失败:', error);
            this.notificationService.error(localize('templateFetchError', "获取模板数据失败: {0}", error.message || error));
            return [];
        }
    }

    async getProjectInfo(projectId: number): Promise<IRes> {
        try {
            // 获取ptKey
            const ptKey = await this.getPtKey();
            // 检查是否取消
            if (!ptKey) {
                throw new Error('未获取到ptKey');
            }
            const res = await this.newProjectMainService.getProjectInfo({ projectId, ptKey });
            return res;
        } catch (error) {
            this.logService.error('获取项目详情失败:', error);
            throw new Error(error.message || '获取项目详情失败');
        }
    }

    async getProjectSSHConfig(projectId: number): Promise<IRes<ISSHInfo> | null> {
        try {
            // 获取ptKey
            const ptKey = await this.getPtKey();
            // 检查是否取消
            if (!ptKey) {
                throw new Error('未获取到ptKey');
            }
            const res = await this.newProjectMainService.getProjectSSHConfig({ projectId, ptKey });
            if (res.code === 200) {
                return res || null
            } else {
                this.notificationService.error(localize('templateFetchError', " 获取ssh配置错误: {0}", res.message || '未知错误'));
            }
            return res
        } catch (error) {
            this.logService.error('获取ssh配置错误:', error);
            throw new Error(error.message || '获取ssh配置错误');
        }
    }

    async createProject(params: INewProjectParams): Promise<IRes | null> {
        try {
            // 获取ptKey
            const ptKey = await this.getPtKey();
            // 检查是否取消
            if (!ptKey) {
                return null;
            }
            // 发送请求获取模板列表
            const res = await this.newProjectMainService.createProject({ params, ptKey });
            console.log(res, 'create01')
            if (res.code === 200) {
                return res || []
            } else {
                this.notificationService.error(localize('templateFetchError', "创建项目失败: {0}", res.message || '未知错误'));
            }
            return res;
        } catch (error) {
            this.logService.error('创建项目失败:', error);
            return null;
        }
    }

    async getProjectList(): Promise<any[]> {
        try {
            // 获取ptKey
            const ptKey = await this.getPtKey();
            // 检查是否取消
            if (!ptKey) {
                return [];
            }

            // 发送请求获取模板列表
            const res = await this.newProjectMainService.getProjectList(ptKey);
            if (res?.code === 200) {
                return res?.data || []
            } else {
                this.notificationService.error(localize('templateFetchError', "getProjectList Error: {0}", res.message || '未知错误'));
            }
            return []
        } catch (error) {
            this.logService.error('getProjectList Error:', error);
            return [];
        }
    }

    async getProjectDatabaseList(): Promise<any[]> {
        console.log('getProjectDatabaseList02')
        try {
            // 获取ptKey
            const ptKey = await this.getPtKey();
            // 检查是否取消
            if (!ptKey) {
                return [];
            }

            // 发送请求获取模板列表
            const res = await this.newProjectMainService.getProjectDatabaseList(ptKey);
            console.log('getProjectDatabaseList:', res);
            if (res.code === 200) {
                return res?.data || []
            } else {
                this.notificationService.error(localize('templateFetchError', "获数据库列表失败: {0}", res.msg || '未知错误'));
            }
            return [];
        } catch (error) {
            this.logService.error('获数据库列表失败:', error);
        }
        return [];
    }

    async setProjectInfo(params: any): Promise<IRes | null> {
        try {
            const ptKey = await this.getPtKey();
            if (!ptKey) return null;
            // 发送请求获取模板列表
            const res = await this.newProjectMainService.setProjectInfo({ params, ptKey });
            console.log('setProjectInfo:', res);
            return res
        } catch (error) {
            this.logService.error('报错设置:', error);
        }
        return null;
    }
    async setProjectAppliedTem(params: any): Promise<IRes | null> {
        console.log('setProjectAppliedTem001', params);
        try {
            const ptKey = await this.getPtKey();
            if (!ptKey) return null;

            // 发送请求获取模板列表
            const res = await this.newProjectMainService.setProjectAppliedTem({ params, ptKey });
            console.log('setProjectAppliedTem:', res);
            return res
        } catch (error) {
            this.logService.error('setProjectAppliedTem报错设置:', error);
        }
        return null;
    }

    /**
     * 等待终端准备就绪
     * 创建一个禁用Git扩展终端集成的终端实例
     */
    private async waitForTerminalReady(): Promise<void> {
        this.logService.info('等待终端准备就绪...');

        try {
            // 创建一个禁用Git扩展终端集成的终端
            const terminal = await this.createGitDisabledTerminal();

            // 检查终端的Shell类型和进程ID
            const shellType = terminal.shellType;
            const processId = terminal.processId;
            this.logService.info(`终端Shell类型: ${shellType || '未知'}, 进程ID: ${processId || '未知'}`);

            // 使用此终端执行命令
            this._terminal = terminal;
            this.logService.info('终端已准备就绪');
        } catch (error) {
            this.logService.error(`创建终端时出错: ${error}`);

            // 尝试使用活动的终端实例作为备用方案
            const activeTerminal = this.terminalService.activeInstance;
            if (activeTerminal) {
                this.logService.info('使用现有活动终端作为备用方案');
                this._terminal = activeTerminal;
            } else {
                this.logService.error('无法创建或获取终端实例');
                throw new Error('无法创建或获取终端实例');
            }
        }
    }


    /**
     * 创建一个禁用Git扩展终端集成的终端
     * 这种方法不需要完全禁用Git扩展，只是在终端环境中设置特定变量
     * @returns 创建的终端实例
     */
    private async createGitDisabledTerminal(): Promise<ITerminalInstance> {
        this.logService.info('创建禁用Git扩展终端集成的终端...');

        // 检查是否在远程环境中
        const isRemoteEnvironment = !!this.environmentService.remoteAuthority;
        this.logService.info(`当前环境: ${isRemoteEnvironment ? '远程' : '本地'}`);

        // 在远程环境中，尝试使用现有的终端
        if (isRemoteEnvironment) {
            // 尝试获取活动的终端实例
            const activeTerminal = this.terminalService.activeInstance;
            if (activeTerminal) {
                this.logService.info('远程环境: 使用现有活动终端');
                return activeTerminal;
            }

            // 如果没有活动终端，创建一个可见的终端
            this.logService.info('远程环境: 创建新的可见终端');
            const terminal = await this.terminalService.createTerminal({
                config: {
                    name: 'Download Terminal',
                    // 不指定 executable，让系统自动选择可用的 shell
                    executable: isWindows ? undefined : 'bash',
                    env: {
                        // 禁用 Git 扩展功能
                        'VSCODE_GIT_DISABLE': 'true',
                        'GIT_OPTIONAL_LOCKS': '0',
                    },
                    // 在远程环境中，不隐藏终端
                    hideFromUser: false,
                    isFeatureTerminal: true,
                    isTransient: false, // 不是临时终端，以便用户可以看到
                }
            });

            if (!terminal) {
                throw new Error('无法创建终端实例');
            }

            // 显示终端
            this.terminalService.setActiveInstance(terminal);

            // 等待终端进程准备就绪
            try {
                this.logService.info('等待终端进程准备就绪...');
                await terminal.processReady;
                await new Promise(resolve => setTimeout(resolve, 1000));
                this.logService.info('终端进程已准备就绪');
            } catch (error) {
                this.logService.error(`等待终端进程准备就绪时出错: ${error}`);
                // 出错时继续执行，不阻塞流程
                await new Promise<void>(resolve => setTimeout(resolve, 3000));
                this.logService.info('使用备用方案继续执行');
            }

            return terminal;
        } else {
            // 本地环境: 区分Windows和Mac/Linux环境
            // 使用导入的isWindows变量检查平台
            this.logService.info(`本地环境: ${isWindows ? 'Windows' : (isMacintosh ? 'Mac' : 'Linux')}`);

            // 在Windows平台上，创建可见的终端以确保命令能够正确执行
            const hideTerminal = !isWindows; // Windows平台不隐藏终端

            // 使用VS Code内置的终端配置文件创建终端
            const terminal = await this.terminalService.createTerminal({
                config: {
                    name: 'Download Terminal',
                    // 在Windows环境中，使用profileName指定PowerShell，不直接指定可执行文件路径
                    // 这样VS Code会使用内置的PowerShell配置，包括正确的参数和环境变量
                    // profileName: isWindows ? 'PowerShell' : undefined,
                    // // 在Mac/Linux环境中，使用默认的shell参数
                    // args: isWindows ? undefined : '-l',
                    executable: isWindows ? undefined : 'bash',
                    env: {
                        // Git 身份验证相关环境变量
                        'VSCODE_GIT_ASKPASS_NODE': 'none',
                        'GIT_ASKPASS': 'echo',
                        'VSCODE_GIT_ASKPASS_MAIN': 'none',
                        'VSCODE_GIT_ASKPASS_HANDLE': 'none',
                        'GIT_TERMINAL_PROMPT': '0',

                        // Git 编辑器相关环境变量
                        'GIT_EDITOR': 'echo',
                        'VSCODE_GIT_EDITOR_NODE': '',
                        'VSCODE_GIT_EDITOR_MAIN': '',

                        // Git IPC 相关环境变量
                        'VSCODE_GIT_IPC_HANDLE': '',

                        // VS Code 集成相关环境变量
                        'VSCODE_INJECTION': 'false',
                        'VSCODE_SHELL_INTEGRATION': 'false',
                        'TERM_PROGRAM': 'not_vscode',
                        'VSCODE_SHELL_INTEGRATION_ENABLED': 'false',
                        'VSCODE_SHELL_INTEGRATION_SUGGEST_ENABLED': 'false',

                        // 禁用 Git 扩展功能
                        'VSCODE_GIT_DISABLE': 'true',
                        'GIT_OPTIONAL_LOCKS': '0',

                        // 禁用 Git 命令提示
                        'GIT_CONFIG_NOSYSTEM': '1',
                        'GIT_CONFIG_GLOBAL': '/dev/null',

                        // 禁用 Git 自动完成
                        'GIT_COMPLETION_ENABLED': 'false',

                        // 禁用 Git 终端认证
                        'VSCODE_GIT_TERMINAL_AUTH': 'false',
                        'VSCODE_GIT_IPC_DISABLE': 'true',

                        // 禁用 Git 集成
                        'VSCODE_GIT_ENABLED': 'false',
                        'VSCODE_GIT_EXTENSION_ENABLED': 'false'
                    },
                    // 在Windows平台上不隐藏终端，以确保命令能够正确执行
                    hideFromUser: hideTerminal,
                    isFeatureTerminal: true, // 标记为特性终端
                    isTransient: !isWindows, // Windows平台不设为临时终端
                    // strictEnv: true, // 只使用我们提供的环境变量，不继承其他环境变量
                    useShellEnvironment: false // 不使用Shell环境变量
                }
            });

            // 记录终端类型
            this.logService.info(`创建的终端类型: ${isWindows ? 'PowerShell' : '默认shell'}`);


            if (!terminal) {
                throw new Error('无法创建终端实例');
            }

            // 等待终端进程准备就绪
            try {
                this.logService.info('等待终端进程准备就绪...');
                await terminal.processReady;
                await new Promise(resolve => setTimeout(resolve, 1000));
                this.logService.info('终端进程已准备就绪');
            } catch (error) {
                this.logService.error(`等待终端进程准备就绪时出错: ${error}`);
                // 出错时继续执行，不阻塞流程
                await new Promise<void>(resolve => setTimeout(resolve, 3000));
                this.logService.info('使用备用方案继续执行');
            }

            return terminal;
        }
    }

    /**
     * 下载并解压模板
     * @param downloadUrl 下载地址
     * @param targetPath 目标路径
     * @param templateName 模板名称
     * @param token 取消令牌
     * @returns 是否成功
     */
    async downloadAndExtractTemplate(downloadUrl: string, targetPath: string, templateName?: string, token?: CancellationToken): Promise<boolean> {
        // 记录模板名称，用于日志
        if (templateName) {
            this.logService.info(`开始下载模板: ${templateName}`);
        }

        // 注意：我们不再尝试禁用Git扩展，而是在创建终端时通过环境变量禁用Git扩展的终端集成
        this.logService.info('将使用特殊环境变量禁用Git扩展的终端集成，而不是禁用整个扩展', targetPath);

        try {
            // 验证目标路径
            if (!targetPath) {
                this.notificationService.error(localize('invalidTargetPath', "目标路径无效"));
                return false;
            }

            // 检查是否取消
            if (token?.isCancellationRequested) {
                return false;
            }

            // 确保目标目录存在
            // 判断是远程路径还是本地路径
            const isRemotePath = !!this.environmentService.remoteAuthority

            this.logService.info(`目标路径: ${targetPath}, 是否为远程路径: ${isRemotePath}`);

            // 创建适合当前环境的 URI
            const targetUri = this.createAppropriateUri(targetPath);
            this.logService.info(`目标 URI: ${targetUri}`);

            try {
                if (!await this.fileService.exists(targetUri)) {
                    try {
                        this.logService.info(`目录不存在，尝试创建: ${targetUri}`);
                        await this.fileService.createFolder(targetUri);
                        this.logService.info(`成功创建目录: ${targetUri}`);
                    } catch (createError: any) {
                        // 处理创建文件夹失败的情况
                        if (createError.fileOperationResult === 'FILE_PERMISSION_DENIED') {
                            this.notificationService.error(localize('permissionDenied', "没有权限创建目录: {0}", targetPath));
                        } else if (createError.fileOperationResult === 'FILE_INVALID_PATH') {
                            this.notificationService.error(localize('invalidPath', "路径无效: {0}", targetPath));
                        } else {
                            this.notificationService.error(localize('createDirFailed', "创建目录失败: {0}", createError.message || createError));
                        }
                        return false;
                    }
                }
            } catch (existsError: any) {
                // 处理检查目录是否存在失败的情况
                this.notificationService.error(localize('checkDirFailed', "检查目录是否存在失败: {0}", existsError.message || existsError));
                return false;
            }

            // 检查是否取消
            if (token?.isCancellationRequested) {
                return false;
            }

            // 准备下载
            // 使用进度条显示下载进度
            return await this.progressService.withProgress<boolean>(
                {
                    location: ProgressLocation.Notification,
                    // title: localize('downloadingTemplate', "正在下载项目模板..."),
                    cancellable: false // 设置为不可取消，不显示Cancel按钮
                },
                async (progress): Promise<boolean> => {
                    // 初始化进度报告
                    progress.report({
                        message: localize('preparingDownload', "准备下载..."),
                        increment: 5
                    });

                    try {
                        // 验证下载地址的有效性
                        if (!isValidDownloadUrl(downloadUrl)) {
                            throw new Error(localize('invalidDownloadUrl', "下载地址无效: {0}", downloadUrl));
                        }

                        // 检查是否取消
                        if (token?.isCancellationRequested) {
                            return false;
                        }

                        // 根据下载URL确定文件类型
                        const fileExtension = getFileExtensionFromUrl(downloadUrl);
                        const archiveFileName = `template-${Date.now()}${fileExtension}`;
                        const archiveFilePath = path.join(targetPath, archiveFileName);

                        // 创建适合当前环境的 URI
                        const archiveFileUri = this.createAppropriateUri(archiveFilePath);

                        this.logService.info(`开始使用命令行工具下载模板: ${downloadUrl} 到 ${archiveFilePath} (类型: ${fileExtension})`);
                        this.logService.info(`归档文件 URI: ${archiveFileUri}`);

                        try {
                            // // 等待终端准备就绪
                            await this.waitForTerminalReady();

                            // 检查是否取消
                            if (token?.isCancellationRequested) {
                                return false;
                            }

                            // 使用命令行工具下载文件，传递进度报告器
                            // 记录是否为远程路径，以便后续处理
                            this.logService.info(`下载文件，是否为远程路径: ${isRemotePath}`);

                            const ptKey = await this.getPtKey();

                            await downloadWithCommandLine(
                                downloadUrl,
                                isRemotePath ? targetUri.toString() : archiveFilePath, // 根据环境使用不同的路径表示
                                progress,
                                this.logService,
                                this.fileService,
                                this.commandService,
                                this._terminal,
                                !!isRemotePath,
                                archiveFileName, // 添加压缩文件名参数
                                ptKey
                            );

                            this.logService.info(`模板下载成功: ${archiveFilePath}`);
                            // 完成下载进度
                            progress.report({
                                message: localize('downloadComplete', "下载完成: 100%"),
                                increment: 10
                            });

                            // 检查是否取消
                            if (token?.isCancellationRequested) {
                                return false;
                            }

                            // 下载成功后，尝试解压文件
                            this.logService.info(`准备解压文件，目标路径: ${targetPath}, 是否为远程路径: ${isRemotePath}`);
                            this.logService.info(`targetUri.toString() : ${targetUri.toString()}`);

                            await extractWithTerminal(
                                isRemotePath ? targetUri.toString() : targetPath, // 根据环境使用不同的路径表示
                                archiveFileName,
                                this.logService,
                                this.fileService,
                                this.commandService,
                                this._terminal,
                                !!isRemotePath,
                                progress // 传递进度报告接口
                            );

                            // 检查是否有隐藏文件，如果有，则修改 VS Code 设置以显示隐藏文件
                            // 无论是本地还是远程路径，都使用相同的方法，因为 ensureHiddenFilesVisible 内部已经使用 createAppropriateUri
                            await this.ensureHiddenFilesVisible(targetPath);

                            this.logService.info('模板下载和解压成功');
                            // 解压完成后关闭 terminal
                            if (this._terminal && typeof this._terminal.dispose === 'function') {
                                this.logService.info('关闭终端实例');
                                this._terminal.dispose();
                            }
                            return true;
                        } catch (error: any) {
                            this.logService.error(`下载或解压失败: ${error.message || error}`);
                            throw error;
                        }
                    } catch (error: any) {
                        this.logService.error(`下载失败: ${error}`);

                        // 提供更详细的错误信息
                        let errorMessage = localize('downloadFailed', "下载失败");

                        if (error.message) {
                            if (error.message.includes('ENOTFOUND') || error.message.includes('getaddrinfo')) {
                                errorMessage += `：${localize('serverNotFound', "找不到服务器，请检查下载地址是否正确")}`;
                            } else if (error.message.includes('ETIMEDOUT') || error.message.includes('timeout')) {
                                errorMessage += `：${localize('connectionTimeout', "连接超时，请检查网络连接")}`;
                            } else if (error.message.includes('ECONNREFUSED')) {
                                errorMessage += `：${localize('connectionRefused', "连接被拒绝，服务器可能不可用")}`;
                            } else if (error.message.includes('certificate') || error.message.includes('SSL')) {
                                errorMessage += `：${localize('sslError', "SSL证书错误，请检查下载地址是否安全")}`;
                            } else if (error.message.includes('404')) {
                                errorMessage += `：${localize('fileNotFound', "文件不存在，请检查下载地址")}`;
                            } else if (error.message.includes('403')) {
                                errorMessage += `：${localize('accessDenied', "访问被拒绝，可能没有权限下载该文件")}`;
                            } else {
                                errorMessage += `：${error.message}`;
                            }
                        } else {
                            errorMessage += `：${localize('unknownError', "未知错误，请检查网络连接后重试")}`;
                        }

                        this.notificationService.error(errorMessage);

                        // 显示下载地址，方便用户手动下载
                        this.notificationService.info(localize('manualDownload', "您可以尝试手动下载模板：{0}", downloadUrl));

                        return false;
                    }
                }
            );
        } catch (error: any) {
            // 处理所有其他错误
            let errorMessage = error.message || String(error);

            // 尝试提供更具体的错误信息
            if (error.code === 'ENOENT') {
                errorMessage = localize('pathNotFound', "路径不存在: {0}", targetPath);
            } else if (error.code === 'EACCES') {
                errorMessage = localize('accessDenied', "访问被拒绝: {0}", targetPath);
            } else if (error.code === 'ENOTDIR') {
                errorMessage = localize('notDirectory', "不是一个目录: {0}", targetPath);
            }

            this.logService.error('处理模板下载失败:', error);
            this.notificationService.error(localize('templateProcessingFailed', "处理模板失败: {0}", errorMessage));
            return false;
        }
    }

    /**
     * 检查项目路径是否存在
     * @param projectPath 项目路径
     * @returns 是否存在
     */
    async checkProjectExists(projectPath: string | URI): Promise<boolean> {
        try {
            const uri = typeof projectPath === 'string' ? URI.file(projectPath) : projectPath;
            return await this.fileService.exists(uri);
        } catch (error) {
            this.logService.error('检查项目路径是否存在失败:', error);
            return false;
        }
    }

    /**
     * 保存模板下载信息到存储
     * @param info 模板下载信息
     */
    saveTemplateDownloadInfo(info: ITemplateDownloadInfo): void {
        this.logService.info('saveTemplateDownloadInfo', info);
        try {
            // 根据环境选择不同的存储方式
            if (info.type === 'remote') {
                // 远程环境：使用 IStorageService
                this.storageService.store(
                    'templateDownloadInfo',
                    JSON.stringify(info),
                    StorageScope.WORKSPACE, // 工作区级别的存储
                    StorageTarget.MACHINE    // 表示这是机器级别的设置
                );
                this.logService.debug('已保存模板下载信息到 IStorageService (WORKSPACE)');

                // 同时也存储到 APPLICATION 作用域，以防万一
                this.storageService.store(
                    'templateDownloadInfo',
                    JSON.stringify(info),
                    StorageScope.APPLICATION,
                    StorageTarget.MACHINE
                );
                this.logService.debug('已保存模板下载信息到 IStorageService (APPLICATION)');
            } else {
                // 本地环境：优先使用 IStorageService，失败时回退到 localStorage
                try {
                    this.storageService.store(
                        'templateDownloadInfo',
                        JSON.stringify(info),
                        StorageScope.APPLICATION,
                        StorageTarget.MACHINE
                    );
                    this.logService.info(`已保存模板下载信息到 IStorageService (本地)${StorageTarget.MACHINE}`);
                } catch (storageError) {
                    this.logService.error('保存模板下载信息到 IStorageService 失败:', storageError);

                    // 回退到 localStorage
                    localStorage.setItem('templateDownloadInfo', JSON.stringify(info));
                    this.logService.info('已回退保存模板下载信息到 localStorage');
                }
            }
        } catch (error) {
            this.logService.error('保存模板下载信息失败:', error);

            // 最后尝试使用 localStorage
            try {
                localStorage.setItem('templateDownloadInfo', JSON.stringify(info));
                this.logService.info('已使用 localStorage 作为最后手段保存模板下载信息');
            } catch (localStorageError) {
                this.logService.error('使用 localStorage 保存模板下载信息也失败:', localStorageError);
            }
        }
    }

    /**
     * 从存储获取模板下载信息
     * @returns 模板下载信息，如果不存在则返回undefined
     */
    getTemplateDownloadInfo(): ITemplateDownloadInfo | undefined {
        try {
            // 首先尝试从 IStorageService 的 WORKSPACE 作用域获取
            let infoStr = this.storageService.get('templateDownloadInfo', StorageScope.APPLICATION);

            // 如果没有找到，尝试从 APPLICATION 作用域获取
            if (!infoStr) {
                infoStr = this.storageService.get('templateDownloadInfo', StorageScope.WORKSPACE);
                if (infoStr) {
                    this.logService.info('从 IStorageService (APPLICATION) 获取到模板下载信息');
                }
            } else {
                this.logService.info('从 IStorageService (WORKSPACE) 获取到模板下载信息');
            }

            // 如果仍然没有找到，尝试从 localStorage 获取
            if (!infoStr) {
                try {
                    const localStorageItem = localStorage.getItem('templateDownloadInfo');
                    if (localStorageItem !== null) {
                        infoStr = localStorageItem;
                    }
                    if (infoStr) {
                        this.logService.debug('从 localStorage 获取到模板下载信息');
                    }
                } catch (localStorageError) {
                    this.logService.error('从 localStorage 获取模板下载信息失败:', localStorageError);
                }
            }

            // 解析并返回信息
            if (infoStr) {
                const info = JSON.parse(infoStr);
                return info;
            }
        } catch (error) {
            this.logService.error('获取模板下载信息失败:', error);
        }

        this.logService.info('未找到模板下载信息');
        return undefined;
    }

    /**
     * 清除存储中的模板下载信息
     */
    clearTemplateDownloadInfo(): void {
        try {
            // 清除 IStorageService 中的信息
            this.storageService.remove('templateDownloadInfo', StorageScope.WORKSPACE);
            this.storageService.remove('templateDownloadInfo', StorageScope.APPLICATION);
            this.logService.debug('已清除 IStorageService 中的模板下载信息');

            // 清除 localStorage 中的信息
            try {
                localStorage.removeItem('templateDownloadInfo');
                this.logService.debug('已清除 localStorage 中的模板下载信息');
            } catch (localStorageError) {
                this.logService.error('清除 localStorage 中的模板下载信息失败:', localStorageError);
            }
        } catch (error) {
            this.logService.error('清除模板下载信息失败:', error);
        }
    }

    /**
     * 创建适合当前环境的 URI（本地或远程）
     * @param filePath 文件路径
     * @returns 适合当前环境的 URI
     */
    private createAppropriateUri(filePath: string): URI {
        // 检查是否在远程环境中
        console.log('filePath:', filePath)
        if (this.environmentService.remoteAuthority) {
            return URI.from({
                scheme: this.pathService.defaultUriScheme,
                authority: this.environmentService.remoteAuthority,
                path: filePath
            });
        } else {
            // 在本地环境中，使用 file:// 方案
            return URI.file(filePath);
        }
    }

    /**
     * 确保隐藏文件可见
     * 检查目标路径中是否有隐藏文件，如果有，则修改 VS Code 设置以显示隐藏文件
     * @param targetPath 目标路径
     */
    private async ensureHiddenFilesVisible(targetPath: string): Promise<void> {
        try {
            // 获取目标目录下的所有文件
            const targetUri = this.createAppropriateUri(targetPath);
            const files = await this.fileService.resolve(targetUri, { resolveMetadata: true });

            // 检查是否有以 . 开头的文件或文件夹
            const hasHiddenFiles = files.children?.some(file => {
                const fileName = path.basename(file.resource.path);
                return fileName.startsWith('.') && fileName !== '.git' && fileName !== '.DS_Store';
            });

            if (hasHiddenFiles) {

                // 获取当前的 files.exclude 设置
                const currentExcludeConfig = this.configurationService.getValue<Record<string, boolean>>(FILES_EXCLUDE_CONFIG);

                // 创建新的配置，保留原有配置但禁用隐藏点文件的排除
                const newExcludeConfig = { ...currentExcludeConfig };

                // 修改配置，将以 . 开头的文件和文件夹的排除设置为 false
                if (newExcludeConfig['.*']) {
                    newExcludeConfig['.*'] = false;
                }
                if (newExcludeConfig['**/.*']) {
                    newExcludeConfig['**/.*'] = false;
                }

                // 更新配置
                await this.configurationService.updateValue(FILES_EXCLUDE_CONFIG, newExcludeConfig);
            }
        } catch (error) {
        }
    }
}

// 注册服务
registerSingleton(INewProjectService, WebNewProjectService, InstantiationType.Delayed);
