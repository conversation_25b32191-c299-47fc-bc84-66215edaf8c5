/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import * as path from '../../../../../base/common/path.js';

/**
 * 从URL中获取文件扩展名
 * @param url 下载地址
 * @returns 文件扩展名（包括点，如 .zip, .tar.gz）
 */
export function getFileExtensionFromUrl(url: string): string {
    try {
        // 尝试解析 URL
        const parsedUrl = new URL(url);

        // 获取路径部分
        const pathname = parsedUrl.pathname;

        // 处理查询参数和哈希
        const cleanPath = pathname.split('?')[0].split('#')[0];

        // 获取文件名
        const fileName = cleanPath.split('/').pop() || '';

        // 检查是否是 tar.gz
        if (fileName.toLowerCase().endsWith('.tar.gz')) {
            return '.tar.gz';
        }

        // 获取扩展名
        const ext = path.extname(fileName).toLowerCase();

        // 如果没有扩展名或扩展名不明确，默认为 .zip
        if (!ext || ext === '.') {
            return '.zip';
        }

        // 检查是否是支持的扩展名
        const supportedExtensions = ['.zip', '.tar', '.gz', '.tgz', '.rar', '.7z'];
        if (!supportedExtensions.includes(ext)) {
            console.warn(`URL ${url} 的文件扩展名 ${ext} 可能不受支持，尝试使用该扩展名`);
        }

        return ext;
    } catch (error) {
        console.error(`从URL获取文件扩展名失败: ${error}, 默认使用 .zip`);
        return '.zip';
    }
}

/**
 * 验证下载地址的有效性
 * @param url 下载地址
 * @returns 是否有效
 */
export function isValidDownloadUrl(url: string): boolean {
    if (!url) {
        return false;
    }

    try {
        // 尝试解析 URL
        const parsedUrl = new URL(url);

        // 检查协议是否为 http 或 https
        if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
            return false;
        }

        // 检查主机名是否存在
        if (!parsedUrl.hostname) {
            return false;
        }

        // 检查是否是 IP 地址
        const isIpAddress = /^(\d{1,3}\.){3}\d{1,3}$/.test(parsedUrl.hostname);

        // 如果是 IP 地址，检查是否是内网 IP
        if (isIpAddress) {
            // 检查是否是内网 IP
            if (parsedUrl.hostname.startsWith('10.') ||
                parsedUrl.hostname.startsWith('172.16.') ||
                parsedUrl.hostname.startsWith('192.168.') ||
                parsedUrl.hostname === '127.0.0.1') {
                // 内网 IP 可能不可访问
                console.warn('下载地址使用了内网 IP，可能不可访问:', url);
            }
        }

        return true;
    } catch (error) {
        console.error('解析下载地址失败:', error);
        return false;
    }
}
