/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { Action2, registerAction2 } from '../../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { localize } from '../../../../../nls.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { URI } from '../../../../../base/common/uri.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../loginAction.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { IWorkingCopyFileService } from '../../../../../workbench/services/workingCopy/common/workingCopyFileService.js';
import { CancellationToken } from '../../../../../base/common/cancellation.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { showCustomDeleteDialog } from './customDeleteDialog.js';
// import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';

// 命令ID
export const DELETE_PROJECT_COMMAND_ID = 'workbench.action.deleteProject';
export const TEST_DELETE_LOCAL_PROJECT_COMMAND_ID = 'workbench.action.file.testLocal';
export const TEST_DELETE_REMOTE_PROJECT_COMMAND_ID = 'workbench.action.joyCoderTestDeleteRemoteProject';


export const DELETE_PROJECT = 'clouddev.deleteProject';

// 项目数据接口
export interface IProjectData {
    id: number;
    name: string;
    projectPath?: string;
    isRemote?: boolean;
    localLocation?: string;
    devMode?: number; // 1为远程 2为本地
}

// 模拟项目数据
const MOCK_PROJECT_DATA: IProjectData = {
    id: 233,
    name: 'HelloWorld',
    projectPath: '/Users/<USER>/Projects/HelloWorld',
    isRemote: false
};

// 模拟远程项目数据
const MOCK_REMOTE_PROJECT_DATA: IProjectData = {
    id: 233,
    name: 'RemoteProject',
    projectPath: '/config/workspace/RemoteProject',
    isRemote: true
};

// 注册删除项目命令
registerAction2(class DeleteProjectAction extends Action2 {
    constructor() {
        super({
            id: DELETE_PROJECT_COMMAND_ID,
            title: {
                value: localize('deleteProject', "Delete Project"),
                original: 'Delete Project'
            },
            category: Categories.View,
            f1: true,
            // 添加前置条件：只有在已登录状态下才启用
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
        });
    }

    async run(accessor: ServicesAccessor, projectData?: IProjectData, callback?: Function): Promise<boolean> {
        // 如果没有提供项目数据，则无法继续
        if (!projectData || !projectData.name) {
            accessor.get(INotificationService).error(localize('noProjectData', "No project data provided"));
            return false;
        }

        console.log(projectData, 'projectData', callback)
        // 获取服务
        const notificationService = accessor.get(INotificationService);
        const fileService = accessor.get(IFileService);
        const logService = accessor.get(ILogService);
        const workingCopyFileService = accessor.get(IWorkingCopyFileService);
        // const workspaceContextService = accessor.get(IWorkspaceContextService);
        // const commandService = accessor.get(ICommandService);

        // 项目名称
        const projectName = projectData.name;
        // 项目路径
        const projectPath = projectData.localLocation;
        // 是否为远程项目
        const isRemote = projectData.devMode === 1;

        logService.info(`DeleteProjectAction: 准备删除项目 "${projectName}", 路径: ${projectPath || '未提供'}, 远程: ${isRemote}`);

        return await new Promise<boolean>(async (resolve) => {
            // 创建一个变量来存储是否删除本地文件的选择
            let deleteLocalFiles = projectData?.devMode === 2;
            showCustomDeleteDialog({
                projectName,
                isRemote,
                onConfirm: async () => {
                    logService.info(`DeleteProjectAction: 用户确认删除项目 "${projectName}", 同时删除本地文件: ${deleteLocalFiles}`);
                    try {
                        // 如果需要删除本地文件且提供了项目路径
                        if (!isRemote && deleteLocalFiles && projectPath) {
                            const projectUri = URI.file(projectPath);
                            // 检查项目路径是否存在
                            const exists = await fileService.exists(projectUri);
                            if (!exists) {
                                notificationService.warn(localize('projectNotFound', "Project folder not found: {0}", projectPath));
                                resolve(true);
                                return;
                            }

                            // 删除项目文件夹
                            logService.info(`DeleteProjectAction: 开始删除项目文件夹: ${projectPath}`);
                            await workingCopyFileService.delete([{
                                resource: projectUri,
                                recursive: true,
                                useTrash: true
                            }], CancellationToken.None);

                            notificationService.info(localize('projectDeleted', "Project \"{0}\" has been deleted", projectName));
                        }

                        // if (isRemote) {
                        //     const workspaceFolders = workspaceContextService.getWorkspace().folders;
                        //     const currentWorkspaceUri = workspaceFolders[0]?.uri.toString();
                        //     const label = `${projectData.name}_${projectData.id}`;
                        //     if (currentWorkspaceUri.includes(label)) {
                        //         await commandService.executeCommand('workbench.action.remote.close');
                        //         // await commandService.executeCommand('workbench.action.remote.close');
                        //     }
                        // }
                    } catch (error) {
                        logService.error(`DeleteProjectAction: 删除项目 "${projectName}" 时出错:`, error);
                        notificationService.error(localize('deleteError', "Failed to delete project: {0}", error.message));
                    }
                    resolve(true);

                },
                onCancel: () => {
                    logService.info(`DeleteProjectAction: 用户取消了删除项目 "${projectName}" 的操作`);
                    resolve(false);
                }
            });

        })
    }
});

// 注册测试删除本地项目命令
registerAction2(class TestDeleteLocalProjectAction extends Action2 {
    constructor() {
        super({
            id: TEST_DELETE_LOCAL_PROJECT_COMMAND_ID,
            title: {
                value: localize('testDeleteLocalProject', "Test Delete Local Project"),
                original: 'Test Delete Local Project'
            },
            category: Categories.View,
            f1: true
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        const commandService = accessor.get(ICommandService);

        // 使用本地项目模拟数据调用删除项目命令
        await commandService.executeCommand(DELETE_PROJECT_COMMAND_ID, MOCK_PROJECT_DATA);
    }
});

// 注册测试删除远程项目命令
registerAction2(class TestDeleteRemoteProjectAction extends Action2 {
    constructor() {
        super({
            id: TEST_DELETE_REMOTE_PROJECT_COMMAND_ID,
            title: {
                value: localize('testDeleteRemoteProject', "Test Delete Remote Project"),
                original: 'Test Delete Remote Project'
            },
            category: Categories.View,
            f1: true
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        const commandService = accessor.get(ICommandService);

        // 使用远程项目模拟数据调用删除项目命令
        await commandService.executeCommand(DELETE_PROJECT_COMMAND_ID, MOCK_REMOTE_PROJECT_DATA);
    }
});
