/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { localize } from '../../../../../nls.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { URI } from '../../../../../base/common/uri.js';
import * as path from '../../../../../base/common/path.js';
import { IProgress, IProgressStep } from '../../../../../platform/progress/common/progress.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { isWindows, isMacintosh, isLinux } from '../../../../../base/common/platform.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { ITerminalInstance } from '../../../terminal/browser/terminal.js';
import { MODES_CONFIG_FILE } from './templateDownloader.js';

// 临时目录
export const EXTRACT_TEMP = `extract_temp_${Date.now()}`

/**
 * 检查远程环境下的文件下载状态
 * @param targetPath 目标路径
 * @param logService 日志服务
 * @param progress 进度报告接口
 * @returns 是否下载成功
 */
export async function checkRemoteFileDownloadStatus(
    targetPath: string,
    logService: ILogService,
    progress?: IProgress<{ message?: string; increment?: number }>
): Promise<boolean> {
    // 远程环境：使用更智能的等待策略
    let waitTime = 0; // 总等待时间（毫秒）
    const maxWaitTime = 15000; // 最大等待时间（毫秒）
    const initialCheckInterval = 500; // 初始检查间隔（毫秒）
    let checkInterval = initialCheckInterval;
    let checkCount = 0;

    logService.info(`远程环境：开始检查文件下载状态，目标路径: ${targetPath}`);

    while (waitTime < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, checkInterval));
        waitTime += checkInterval;
        checkCount++;

        // 动态调整检查间隔，随着等待时间增加而增加
        if (waitTime > 5000) { // 5秒后
            checkInterval = 1000; // 增加到1秒
        }

        // 报告进度，但最多只到80%，预留20%给解压过程
        if (progress) {
            // 估算下载进度百分比
            const rawProgressPercent = Math.round((waitTime / maxWaitTime) * 100);
            // 将原始进度（0-100%）映射到0-80%范围
            const adjustedProgressPercent = Math.min(80, Math.round(rawProgressPercent * 0.8));
            progress.report({
                message: localize('downloadingSize', "下载中: {0}%", rawProgressPercent),
                increment: adjustedProgressPercent > 0 ? 5 : 0 // 增量进度
            });
        }

        // 每5秒记录一次日志
        if (checkCount % 5 === 0) {
            logService.info(`远程下载进行中，已等待 ${Math.round(waitTime / 1000)} 秒...`);
        }

        // 如果等待超过5秒，尝试提前结束等待
        if (waitTime >= 5000) {
            logService.info(`远程下载已等待${Math.round(waitTime / 1000)}秒，尝试提前结束等待`);
            break;
        }
    }

    // 在远程环境中，我们假设下载成功，因为无法直接检查文件状态
    logService.info(`远程环境下载完成，总等待时间: ${Math.round(waitTime / 1000)}秒，继续后续步骤`);
    return true;
}

/**
 * 检查本地环境下的文件下载状态
 * @param targetPath 目标路径
 * @param fileService 文件服务
 * @param logService 日志服务
 * @param progress 进度报告接口
 * @returns 是否下载成功
 */
export async function checkLocalFileDownloadStatus(
    targetPath: string,
    fileService: IFileService,
    logService: ILogService,
    progress?: IProgress<{ message?: string; increment?: number }>
): Promise<boolean> {
    // 本地环境：使用循环检查文件大小变化，更新进度条
    let lastSize = 0;
    let checkCount = 0;
    const maxChecks = 60; // 最多检查60次，相当于60秒
    let stableCount = 0;
    let lastCheckTime = Date.now();
    const fileUri = URI.file(targetPath);

    logService.info(`本地环境：开始检查文件下载状态，目标路径: ${targetPath}`);

    while (checkCount < maxChecks) {
        await new Promise(resolve => setTimeout(resolve, 500)); // 每0.5秒检查一次，提高响应速度
        checkCount++;

        // 计算自上次检查以来的时间（毫秒）
        const currentTime = Date.now();
        const timeSinceLastCheck = currentTime - lastCheckTime;
        lastCheckTime = currentTime;

        try {
            // 检查文件是否存在
            const exists = await fileService.exists(fileUri);
            if (!exists) {
                // 文件还不存在，继续等待
                if (progress) {
                    progress.report({
                        message: localize('waitingForDownload', "等待下载开始... ({0}s)", Math.round(checkCount / 2)),
                        increment: 0
                    });
                }
                // 重置稳定计数
                stableCount = 0;
                continue;
            }

            // 获取文件大小
            const stat = await fileService.stat(fileUri);
            const currentSize = stat.size;

            if (currentSize > lastSize) {
                // 文件大小增加，说明下载正在进行
                lastSize = currentSize;
                // 重置稳定计数
                stableCount = 0;

                // 报告进度，但最多只到80%，预留20%给解压过程
                if (progress) {
                    // 估算下载进度百分比，但最多只到80%
                    const rawProgressPercent = Math.round((checkCount / maxChecks) * 100);
                    // 将原始进度（0-100%）映射到0-80%范围
                    const adjustedProgressPercent = Math.min(80, Math.round(rawProgressPercent * 0.8));
                    progress.report({
                        message: localize('downloadingSize', "已下载: {0}%", rawProgressPercent),
                        increment: adjustedProgressPercent > 0 ? 1 : 0 // 增量进度
                    });
                }

                logService.info(`下载进度: ${Math.round(currentSize / 1024)} KB, 耗时: ${timeSinceLastCheck}ms`);
            } else if (currentSize === lastSize && currentSize > 0) {
                // 文件大小没有变化，但大于0，可能下载已完成或暂停
                stableCount++;

                // 连续2次大小不变，认为下载完成
                if (stableCount >= 2) {
                    logService.info(`文件大小连续${stableCount}次未变化，可能下载已完成: ${Math.round(currentSize / 1024)} KB`);
                    break;
                }

                // 如果文件大小超过1MB，只需要1次稳定就可以判断为完成
                if (currentSize > 1024 * 1024 && stableCount >= 1) {
                    logService.info(`文件大小超过1MB且未变化，可能下载已完成: ${Math.round(currentSize / 1024)} KB`);
                    break;
                }

                // 如果检查次数超过10次，且文件大小稳定，也认为下载完成
                if (checkCount > 10 && stableCount >= 1) {
                    logService.info(`检查次数超过10次且文件大小稳定，可能下载已完成: ${Math.round(currentSize / 1024)} KB`);
                    break;
                }
            }
        } catch (error) {
            // 检查文件时出错，继续等待
            logService.error(`检查文件大小时出错: ${error}`);
        }
    }

    logService.info(`等待结束，检查下载结果...`);

    // 最终检查文件是否存在
    try {
        // 使用正确的URI格式检查文件是否存在
        const exists = await fileService.exists(fileUri);

        if (!exists) {
            logService.error(`文件不存在: ${targetPath}`);
            throw new Error(`下载失败，文件不存在: ${targetPath}`);
        }

        // 获取文件大小
        try {
            const stat = await fileService.stat(fileUri);

            if (stat.size === 0) {
                logService.error(`文件大小为0: ${targetPath}`);
                throw new Error(`下载失败，文件大小为0: ${targetPath}`);
            }

            logService.info(`文件下载成功: ${targetPath}, 大小: ${stat.size} 字节`);
            return true;
        } catch (statError) {
            logService.error(`获取文件大小失败: ${statError}`);
            // 即使无法获取文件大小，如果文件存在，我们也认为下载成功
            logService.info(`文件下载可能成功，但无法获取大小: ${targetPath}`);
            return true;
        }
    } catch (error) {
        // 在本地环境中，如果检查失败，抛出错误
        logService.error(`检查下载结果失败: ${error}`);
        throw error;
    }
}

/**
 * 检查文件下载状态
 * @param targetPath 目标路径
 * @param fileService 文件服务
 * @param logService 日志服务
 * @param isRemotePath 是否是远程路径
 * @param progress 进度报告接口
 * @returns 是否下载成功
 */
export async function checkFileDownloadStatus(
    targetPath: string,
    fileService: IFileService,
    logService: ILogService,
    isRemotePath: boolean = false,
    progress?: IProgress<{ message?: string; increment?: number }>
): Promise<boolean> {
    // 根据是否是远程路径创建不同的 URI
    const fileUri = isRemotePath
        ? URI.parse(targetPath) // 远程环境中，targetPath 已经是 URI 字符串
        : URI.file(targetPath); // 本地环境中，需要转换为 file URI

    logService.info(`检查文件下载状态，URI: ${fileUri}, 是否远程: ${isRemotePath}`);

    // 根据环境调用不同的检查函数
    if (isRemotePath) {
        return await checkRemoteFileDownloadStatus(targetPath, logService, progress);
    } else {
        return await checkLocalFileDownloadStatus(targetPath, fileService, logService, progress);
    }
}

/**
 * 检查远程环境下的解压状态
 * @param targetPath 目标路径
 * @param logService 日志服务
 * @param progress 进度报告接口
 * @returns 是否解压成功
 */
export async function checkRemoteExtractStatus(
    targetPath: string,
    logService: ILogService,
    progress?: IProgress<{ message?: string; increment?: number }>
): Promise<boolean> {
    // 远程环境：简单等待一段时间，假设解压正在进行
    logService.info(`远程环境：等待解压完成，目标路径: ${targetPath}`);

    // 简单等待一段固定时间
    const waitTime = 10000; // 10秒
    await new Promise(resolve => setTimeout(resolve, waitTime));

    logService.info(`远程环境：等待结束，假设解压已完成`);

    // 在远程环境中，我们假设解压成功，因为无法直接检查文件状态
    logService.info(`远程环境：解压可能已完成，继续后续步骤`);

    // 报告下载完成，但整体进度只到80%，预留20%给解压过程
    if (progress) {
        progress.report({
            message: localize('downloadComplete', "下载完成，准备解压..."),
            increment: 0 // 不增加进度，保持在当前进度
        });
    }

    return true;
}

/**
 * 检查本地环境下的解压状态
 * @param targetPath 目标路径
 * @param fileService 文件服务
 * @param logService 日志服务
 * @returns 是否解压成功
 */
export async function checkLocalExtractStatus(
    targetPath: string,
    fileService: IFileService,
    logService: ILogService
): Promise<boolean> {
    // 本地环境：使用文件监控方式等待解压完成
    // 检查目标目录中的文件数量变化来判断解压是否完成
    let lastFileCount = 0;
    let stableCount = 0;
    const maxChecks = 30; // 最多检查30次

    // 创建目标目录的 URI
    const targetUri = URI.file(targetPath);

    await new Promise(resolve => setTimeout(resolve, 1000));

    for (let i = 0; i < maxChecks; i++) {
        await new Promise(resolve => setTimeout(resolve, 1000)); // 每秒检查一次

        try {
            // 获取目录中的文件列表
            const files = await fileService.resolve(targetUri, { resolveMetadata: false });
            const currentFileCount = files.children?.length || 0;

            logService.info(`检测到目标目录中有 ${currentFileCount} 个文件/目录`);

            if (currentFileCount > 0) {
                if (currentFileCount === lastFileCount) {
                    stableCount++;
                    if (stableCount >= 3) {
                        // 文件数量连续3次保持不变，认为解压完成
                        logService.info(`文件数量连续 ${stableCount} 次保持不变，解压可能已完成`);
                        break;
                    }
                } else {
                    // 文件数量变化，重置稳定计数
                    stableCount = 0;
                }
                lastFileCount = currentFileCount;
            }
        } catch (error) {
            logService.error(`检查目标目录文件数量失败: ${error}`);
        }
    }

    logService.info(`等待结束，检查解压结果...`);

    // 检查解压结果
    try {
        // 直接使用文件服务检查目标目录内容，不使用终端命令
        logService.info(`使用文件服务检查解压结果...`);

        // 获取目录中的文件列表
        const files = await fileService.resolve(targetUri, { resolveMetadata: false });
        const fileCount = files.children?.length || 0;

        // 记录文件列表
        if (fileCount > 0) {
            logService.info(`解压成功，目标目录中有 ${fileCount} 个文件/目录:`);
            if (files.children) {
                for (const file of files.children) {
                    logService.info(`- ${file.name} ${file.isDirectory ? '(目录)' : '(文件)'}`);
                }
            }
            return true;
        } else {
            logService.warn(`解压可能不成功，目标目录中没有文件`);
            return false;
        }
    } catch (error) {
        logService.error(`检查解压结果失败: ${error}`);
        return false;
    }
}

/**
 * 检查解压状态
 * @param targetPath 目标路径
 * @param fileService 文件服务
 * @param logService 日志服务
 * @param isRemotePath 是否是远程路径
 * @param progress 进度报告接口
 * @returns 是否解压成功
 */
export async function checkExtractStatus(
    targetPath: string,
    fileService: IFileService,
    logService: ILogService,
    isRemotePath: boolean = false,
    progress?: IProgress<{ message?: string; increment?: number }>
): Promise<boolean> {
    // 根据是否是远程路径创建不同的 URI
    const targetUri = isRemotePath
        ? URI.parse(targetPath) // 远程环境中，targetPath 已经是 URI 字符串
        : URI.file(targetPath); // 本地环境中，需要转换为 file URI

    logService.info(`检查解压状态，URI: ${targetUri}, 是否远程: ${isRemotePath}`);

    // 报告解压开始
    if (progress) {
        progress.report({
            message: localize('extracting', "正在解压文件..."),
            increment: 5 // 从80%开始，增加5%
        });
    }
    let result: boolean = false;
    try {
        // 远程路径建议用 URI.parse
        const targetUri = URI.parse(targetPath);
        let files;
        try {
            files = await fileService.resolve(targetUri, { resolveMetadata: false });
        } catch (e) {
            logService.warn(`目标目录不存在: ${targetPath}，跳过自动整理`);
            return false;
        }
        if (files.children) {
            const dirs = files.children.filter(f => f.isDirectory);
            // 查找是否存在 EXTRACT_TEMP 目录
            const extractTempDir = dirs.find(d => d.name === EXTRACT_TEMP);
            if (extractTempDir) {
                logService.info(`检测到 EXTRACT_TEMP 目录，自动移动其内容到目标目录`);
                const dirUri = extractTempDir.resource;
                const dirFiles = await fileService.resolve(dirUri, { resolveMetadata: false });
                if (dirFiles.children) {
                    for (const child of dirFiles.children) {
                        const destUri = targetUri.with({ path: path.posix.join(targetUri.path, child.name) });
                        try {
                            // 移动前先判断文件是否存在，避免 EntryNotFound
                            if (await fileService.exists(child.resource)) {
                                await fileService.move(child.resource, destUri, true);
                                logService.info(`已移动: ${child.name} 到 ${targetPath}`);
                            } else {
                                logService.warn(`跳过移动，源文件不存在: ${child.resource.toString()}`);
                            }
                        } catch (e) {
                            logService.warn(`移动 ${child.name} 到 ${targetPath} 时出错: ${e}`);
                        }
                    }
                }
                await new Promise<void>(resolve => setTimeout(resolve, 2000));
                // 移动完成后，校验所有 EXTRACT_TEMP 下的文件是否都已移动到目标目录
                let allMoved = true;
                try {
                    const targetFiles = await fileService.resolve(targetUri, { resolveMetadata: false });
                    const targetNames = targetFiles.children ? new Set(targetFiles.children.map(f => f.name)) : new Set<string>();
                    const movedNames = dirFiles.children ? dirFiles.children.map(f => f.name) : [];
                    for (const name of movedNames) {
                        if (!targetNames.has(name)) {
                            allMoved = false;
                            logService.warn(`文件 ${name} 未成功移动到目标目录，跳过删除 EXTRACT_TEMP`);
                            break;
                        }
                    }
                } catch (e) {
                    allMoved = false;
                    logService.warn(`校验移动结果时出错: ${e}`);
                }

                if (allMoved) {
                    await fileService.del(dirUri, { recursive: true });
                    logService.info(`已删除 EXTRACT_TEMP 目录`);
                } else {
                    logService.warn(`部分文件未移动成功，未删除 EXTRACT_TEMP 目录`);
                }
            }
        }
    } catch (e) {
        logService.warn(`自动整理目录结构时出错: ${e}`);
    }


    // 优先检查 .joycodermodes 文件是否存在
    let joycoderModesUri: URI;
    if (isRemotePath) {
        joycoderModesUri = URI.parse(targetPath + (targetPath.endsWith('/') ? '' : '/') + MODES_CONFIG_FILE);
    } else {
        // 不能用 require('path')，直接字符串拼接，确保无重复斜杠
        joycoderModesUri = URI.file(targetPath.replace(/\/$/, '') + '/' + MODES_CONFIG_FILE);
    }
    try {
        if (await fileService.exists(joycoderModesUri)) {
            logService.info(`检测到 ${MODES_CONFIG_FILE} 文件，解压状态判定为成功，可删除zip包`);
            if (progress) {
                progress.report({
                    message: localize('extractComplete', `检测到解压完成`),
                    increment: 15
                });
            }
            result = true;
        }
    } catch (e) {
        logService.warn(`检查 ${MODES_CONFIG_FILE}  文件时出错: ${e}`);
        result = false;
    }

    return result;
}

/**
 * 生成Windows平台解压命令
 * 尝试多种Windows原生解压方法，按优先级排序
 * @param archiveFilePath 压缩文件路径
 * @param targetPath 解压目标路径
 * @returns 解压命令字符串
 */
export function generateWindowsExtractCommand(archiveFilePath: string, targetPath: string): string {
    // 确保路径使用一致的反斜杠格式
    const normalizedTargetPath = targetPath.replace(/\//g, '\\');
    const normalizedArchivePath = archiveFilePath.replace(/\//g, '\\');

    return `
        # 解压完成标志变量
        $extractFinished = $false

        # 生成临时目录
        $TempDir = [System.IO.Path]::Combine($env:TEMP, "joycoder-unzip-" + [guid]::NewGuid().ToString())
        Write-Host "创建临时解压目录: $TempDir"
        if (!(Test-Path -Path $TempDir)) {
            New-Item -Path $TempDir -ItemType Directory -Force | Out-Null
        }

        # 解压到临时目录
        $extractSuccess = $false
        $extractMethod = "Unknown"
        try {
            Write-Host "尝试使用Shell.Application对象解压..."
            $shell = New-Object -ComObject Shell.Application

            # 检查压缩包路径和临时目录是否存在
            if (!(Test-Path -Path '${normalizedArchivePath}')) {
                Write-Host "错误：压缩包路径不存在：${normalizedArchivePath}"
            } else {
                Write-Host "压缩包路径存在：${normalizedArchivePath}"
            }
            if (!(Test-Path -Path $TempDir)) {
                Write-Host "错误：临时目录不存在：$TempDir"
            } else {
                Write-Host "临时目录存在：$TempDir"
            }

            $zip = $shell.NameSpace('${normalizedArchivePath}')
            $destination = $shell.NameSpace($TempDir)

            if ($null -eq $zip) {
                Write-Host "错误：无法打开压缩包，$zip 为 null，路径：${normalizedArchivePath}"
            }
            if ($null -eq $destination) {
                Write-Host "错误：无法打开目标目录，$destination 为 null，路径：$TempDir"
            }

            if ($null -ne $zip -and $null -ne $destination) {
                $zipItems = $zip.Items()
                $totalItems = $zipItems.Count
                Write-Host "压缩文件中包含 $totalItems 个项目"
                $destination.CopyHere($zipItems, 1556)
            } else {
                Write-Host "跳过 CopyHere 操作，因为 $zip 或 $destination 为 null"
            }
            # 等待解压完成
            $waitCount = 0
            $maxWait = 60
            $stableCount = 0
            $lastFileCount = 0
            $startTime = Get-Date
            while ($waitCount -lt $maxWait) {
                Start-Sleep -Seconds 1
                $waitCount++

                    # 获取当前文件数量
                    $zipFileName = [System.IO.Path]::GetFileName($ArchivePath)
                    $currentItems = @(Get-ChildItem -Path $DestinationPath -Recurse -Force -ErrorAction SilentlyContinue |
                        Where-Object {
                            $_.Name -ne $zipFileName -and
                            -not $_.Name.EndsWith('.tmp') -and
                            -not $_.Name.EndsWith('.temp') -and
                            -not $_.Name.StartsWith('~') -and
                            -not $_.Name.StartsWith('._')
                        })

                    $currentFileCount = $currentItems.Count
                    $elapsedSeconds = (New-TimeSpan -Start $startTime -End (Get-Date)).TotalSeconds

                    Write-Host "等待中... 当前有效文件数: $currentFileCount (已等待 $([Math]::Round($elapsedSeconds, 1)) 秒)"

                    # 检查是否有足够的文件
                    if ($currentFileCount -gt 0 -and $totalItems -gt 0) {
                        if ($currentFileCount -ge $totalItems) {
                            Write-Host "已解压文件数($currentFileCount)已达到或超过压缩包项目数($totalItems)，可能已完成"
                            $stableCount += 2
                        }
                    }

                    # 检查文件数量稳定性
                    if ($currentFileCount -gt 0) {
                        if ($currentFileCount -eq $lastFileCount) {
                            $stableCount++

                            if ($stableCount -ge 3) {
                                Write-Host "文件数量连续 $stableCount 次保持不变，解压可能已完成"
                                break
                            }
                        } else {
                            $stableCount = [Math]::Max(0, $stableCount - 1)
                        }
                        $lastFileCount = $currentFileCount
                    }

                    # 提前结束条件
                    if ($elapsedSeconds -gt 30 -and $currentFileCount -gt 0) {
                        Write-Host "已等待超过30秒且有文件存在，提前结束等待"
                        break
                        $extractFinished = $true
                    }
                }

                # 检查文件锁定状态
                Start-Sleep -Seconds 2
                $allFiles = @(Get-ChildItem -Path $DestinationPath -Recurse -File -Force -ErrorAction SilentlyContinue)
                $lockedFiles = 0

                foreach ($file in $allFiles) {
                    try {
                        $stream = [System.IO.File]::Open($file.FullName, 'Open', 'ReadWrite', 'None')
                        $stream.Close()
                        $stream.Dispose()
                    } catch {
                        $lockedFiles++
                    }
                    $extractFinished = $true
                }

                if ($lockedFiles -eq 0 -or $waitCount -ge $maxWait) {
                    $extractSuccess = $true
                    $extractMethod = "ShellApplication"
                    Write-Host "Shell.Application解压成功"
                }
            } catch {
                Write-Host "Shell.Application解压失败: $_"
            }

            # 如果Shell.Application失败，尝试其他方法
            if (-not $extractSuccess) {
                # 尝试Expand-Archive
                try {
                    Write-Host "尝试使用Expand-Archive命令解压..."
                    Expand-Archive -Path $ArchivePath -DestinationPath $DestinationPath -Force -ErrorAction SilentlyContinue
                    Start-Sleep -Seconds 2

                    # 检查是否有文件被解压
                    $files = @(Get-ChildItem -Path $DestinationPath -Recurse -Force -ErrorAction SilentlyContinue)
                    if ($files.Count -gt 0) {
                        $extractSuccess = $true
                        $extractMethod = "ExpandArchive"
                        Write-Host "Expand-Archive解压成功"
                    }
                } catch {
                    Write-Host "Expand-Archive解压失败: $_"
                }
            }

            # 如果前两种方法都失败，尝试其他方法
            # ...其他解压方法代码...

            # 删除__MACOSX目录
            if (Test-Path -Path "$DestinationPath\\__MACOSX") {
                Remove-Item -Path "$DestinationPath\\__MACOSX" -Recurse -Force -ErrorAction SilentlyContinue
            }

            # 只有解压完成后才处理内容移动
            if ($extractFinished) {
                Write-Host "解压完成，开始处理内容移动..."

                # 检查是否只有一个顶级目录
                Write-Host "检查是否有单一顶级目录..."

                # 等待一段时间确保文件系统操作完成
                Start-Sleep -Seconds 2

                # 获取所有目录
                $allDirs = @(Get-ChildItem -Path $DestinationPath -Directory)

                # 获取所有文件
                $zipFileName = [System.IO.Path]::GetFileName($ArchivePath)
                $allFiles = @(Get-ChildItem -Path $DestinationPath -File)

                Write-Host "原始文件列表:"
                foreach ($file in $allFiles) {
                    Write-Host "  - $($file.Name) (文件, $($file.Length) 字节)"
                }

                Write-Host "原始目录列表:"
                foreach ($dir in $allDirs) {
                    Write-Host "  - $($dir.Name) (目录)"
                }

                Write-Host "原始zip文件名: $zipFileName"

                # 排除原始zip文件和临时文件
                $validFiles = @($allFiles | Where-Object {
                    $isZipFile = $_.Name -eq $zipFileName
                    $isTempFile = $_.Name.EndsWith('.tmp') -or
                                  $_.Name.EndsWith('.temp') -or
                                  $_.Name.StartsWith('~') -or
                                  $_.Name.StartsWith('._')

                    if ($isZipFile) {
                        Write-Host "排除原始zip文件: $($_.Name)"
                    }
                    if ($isTempFile) {
                        Write-Host "排除临时文件: $($_.Name)"
                    }

                    -not $isZipFile -and -not $isTempFile
                })

                # 排除特殊目录（如__MACOSX）
                $validDirs = @($allDirs | Where-Object {
                    $isSpecialDir = $_.Name -eq "__MACOSX"

                    if ($isSpecialDir) {
                        Write-Host "排除特殊目录: $($_.Name)"
                    }

                    -not $isSpecialDir
                })

                Write-Host "发现 $($validDirs.Count) 个有效目录和 $($validFiles.Count) 个有效文件（已排除zip文件和临时文件）"

                # 详细记录目录和文件信息，便于调试
                if ($validDirs.Count -gt 0) {
                    Write-Host "目录列表:"
                    foreach ($dir in $validDirs) {
                        Write-Host "  - $($dir.Name) (目录)"
                    }
                }

                if ($validFiles.Count -gt 0) {
                    Write-Host "文件列表:"
                    foreach ($file in $validFiles) {
                        Write-Host "  - $($file.Name) (文件)"
                    }
                }

                # 如果只有一个顶级目录且没有有效文件，移动其内容
                if ($validDirs.Count -eq 1 -and $validFiles.Count -eq 0) {
                    $topDir = $validDirs[0].FullName
                    Write-Host "发现单一顶级目录: $topDir，移动其内容到目标目录"

                    # 添加详细日志
                    Write-Host "准备移动顶级目录 '$topDir' 的内容到目标目录 '$DestinationPath'"
                    Write-Host "顶级目录内容:"
                    Get-ChildItem -Path $topDir -Force | ForEach-Object {
                        Write-Host "  - $($_.Name) ($(if($_.PSIsContainer){'目录'}else{'文件'}))"
                    }

                    # 确保目标目录存在
                    if (!(Test-Path -Path $DestinationPath)) {
                        Write-Host "目标目录不存在，创建目录: $DestinationPath"
                        New-Item -Path $DestinationPath -ItemType Directory -Force | Out-Null
                    }

                    # 等待一段时间确保文件系统操作完成
                    Start-Sleep -Seconds 1

                    # 移动所有文件和目录到目标目录
                    try {
                        # 使用更可靠的复制然后删除的方式，而不是直接移动
                        Write-Host "开始复制顶级目录内容..."
                        Get-ChildItem -Path $topDir -Force | ForEach-Object {
                            $sourcePath = $_.FullName
                            $destPath = Join-Path -Path $DestinationPath -ChildPath $_.Name

                            Write-Host "处理: $($_.Name) -> $destPath"

                            # 如果目标已存在，先删除
                            if (Test-Path -Path $destPath) {
                                Write-Host "目标已存在，删除: $destPath"
                                Remove-Item -Path $destPath -Recurse -Force -ErrorAction SilentlyContinue

                                # 确认删除成功
                                if (Test-Path -Path $destPath) {
                                    Write-Host "警告: 无法删除已存在的目标: $destPath"
                                }
                            }

                            # 复制文件或目录
                            if ($_.PSIsContainer) {
                                # 是目录，使用Copy-Item递归复制
                                Write-Host "复制目录: $sourcePath -> $destPath"
                                Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                            } else {
                                # 是文件，直接复制
                                Write-Host "复制文件: $sourcePath -> $destPath"
                                Copy-Item -Path $sourcePath -Destination $destPath -Force
                            }

                            # 确认复制成功
                            if (!(Test-Path -Path $destPath)) {
                                Write-Host "警告: 复制失败: $destPath 不存在"
                            }
                        }

                        # 等待文件系统操作完成
                        Start-Sleep -Seconds 1

                        # 删除源目录中的内容
                        Write-Host "删除源目录中的内容..."
                        Get-ChildItem -Path $topDir -Force | ForEach-Object {
                            Remove-Item -Path $_.FullName -Recurse -Force -ErrorAction SilentlyContinue
                        }

                        # 删除现在为空的顶级目录
                        Write-Host "删除空的顶级目录: $topDir"
                        Remove-Item -Path $topDir -Force -ErrorAction SilentlyContinue

                        # 确认删除成功
                        if (Test-Path -Path $topDir) {
                            Write-Host "警告: 无法删除顶级目录: $topDir"
                        } else {
                            Write-Host "成功删除顶级目录: $topDir"
                        }
                    } catch {
                        Write-Host "移动文件时出错: $_"
                    }

                    Write-Host "移动完成，目标目录内容:"
                    Get-ChildItem -Path $DestinationPath -Force | Format-Table -Property Name, Length, LastWriteTime
                } else {
                    Write-Host "没有单一顶级目录或有多个文件/目录，保持原样"
                    Get-ChildItem -Path $DestinationPath -Force | Format-Table -Property Name, Length, LastWriteTime
                }
            } else {
                Write-Host "解压未完成，跳过内容移动"
            }

            return $extractSuccess
        }

        # 检查文件锁定状态
        Start-Sleep -Seconds 2
        $allFiles = @(Get-ChildItem -Path $TempDir -Recurse -File -Force -ErrorAction SilentlyContinue)
        $lockedFiles = 0
        foreach ($file in $allFiles) {
            try {
                $stream = [System.IO.File]::Open($file.FullName, 'Open', 'ReadWrite', 'None')
                $stream.Close()
                $stream.Dispose()
            } catch {
                $lockedFiles++
            }
        }
        if ($lockedFiles -eq 0 -or $waitCount -ge $maxWait) {
            $extractSuccess = $true
            $extractMethod = "ShellApplication"
            Write-Host "Shell.Application解压成功"
        }
        } catch {
            Write-Host "Shell.Application解压失败: $_"
        }

        if (-not $extractSuccess) {
            try {
                Write-Host "尝试使用Expand-Archive命令解压..."
                Expand-Archive -Path '${normalizedArchivePath}' -DestinationPath $TempDir -Force -ErrorAction SilentlyContinue
                Start-Sleep -Seconds 2
                $files = @(Get-ChildItem -Path $TempDir -Recurse -Force -ErrorAction SilentlyContinue)
                if ($files.Count -gt 0) {
                    $extractSuccess = $true
                    $extractMethod = "ExpandArchive"
                    Write-Host "Expand-Archive解压成功"
                }
            } catch {
                Write-Host "Expand-Archive解压失败: $_"
            }
        }

        # 删除__MACOSX目录
        if (Test-Path -Path "$TempDir\\__MACOSX") {
            Remove-Item -Path "$TempDir\\__MACOSX" -Recurse -Force -ErrorAction SilentlyContinue
        }

        # 检查是否只有一个顶级目录且无文件
        Start-Sleep -Seconds 2
        $allDirs = @(Get-ChildItem -Path $TempDir -Directory)
        $allFiles = @(Get-ChildItem -Path $TempDir -File)
        $validDirs = @($allDirs | Where-Object { $_.Name -ne "__MACOSX" })
        $validFiles = @($allFiles | Where-Object {
            -not $_.Name.EndsWith('.tmp') -and
            -not $_.Name.EndsWith('.temp') -and
            -not $_.Name.StartsWith('~') -and
            -not $_.Name.StartsWith('._')
        })

        if ($validDirs.Count -eq 1 -and $validFiles.Count -eq 0) {
            $topDir = $validDirs[0].FullName
            Write-Host "发现单一顶级目录: $topDir，移动其内容到目标目录"
            if (!(Test-Path -Path '${normalizedTargetPath}')) {
                New-Item -Path '${normalizedTargetPath}' -ItemType Directory -Force | Out-Null
            }
            Get-ChildItem -Path $topDir -Force | ForEach-Object {
                $sourcePath = $_.FullName
                $destPath = Join-Path -Path '${normalizedTargetPath}' -ChildPath $_.Name
                if (Test-Path -Path $destPath) {
                    Remove-Item -Path $destPath -Recurse -Force -ErrorAction SilentlyContinue
                }
                if ($_.PSIsContainer) {
                    Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                } else {
                    Copy-Item -Path $sourcePath -Destination $destPath -Force
                }
            }
        } else {
            Write-Host "没有单一顶级目录或有多个文件/目录，移动所有内容到目标目录"
            if (!(Test-Path -Path '${normalizedTargetPath}')) {
                New-Item -Path '${normalizedTargetPath}' -ItemType Directory -Force | Out-Null
            }
            Get-ChildItem -Path $TempDir -Force | ForEach-Object {
                $sourcePath = $_.FullName
                $destPath = Join-Path -Path '${normalizedTargetPath}' -ChildPath $_.Name
                if (Test-Path -Path $destPath) {
                    Remove-Item -Path $destPath -Recurse -Force -ErrorAction SilentlyContinue
                }
                if ($_.PSIsContainer) {
                    Copy-Item -Path $sourcePath -Destination $destPath -Recurse -Force
                } else {
                    Copy-Item -Path $sourcePath -Destination $destPath -Force
                }
            }
        }

        # 删除临时目录
        if (Test-Path -Path $TempDir) {
            Write-Host "删除临时解压目录: $TempDir"
            Remove-Item -Path $TempDir -Recurse -Force -ErrorAction SilentlyContinue
        }
        # 删除zip包
        if (Test-Path -Path '${normalizedArchivePath}') {
            Write-Host "删除原始zip包: ${normalizedArchivePath}"
            Remove-Item -Path '${normalizedArchivePath}' -Force -ErrorAction SilentlyContinue
        }

        Write-Host "解压和目录处理完成"
        Write-Host "解压完成标志变量 extractFinished = $extractFinished"
        # 脚本自删除
        Remove-Item -Path $MyInvocation.MyCommand.Path -Force
    `;
}


/**
 * 获取终端实例的内部实现
 * @param terminal 终端实例
 * @param logService 日志服务
 * @returns 内部终端实例或null
 */
export async function getTerminalInstance(terminal: ITerminalInstance, logService: ILogService): Promise<any> {
    try {
        // 尝试获取内部终端实例
        // @ts-ignore - 访问内部属性
        const internalInstance = (terminal as any)._instance;
        if (internalInstance) {
            logService.info('成功获取终端内部实例');
            return internalInstance;
        }
    } catch (error) {
        logService.error('获取终端内部实例失败:', error);
    }
    return null;
}

/**
 * 直接使用终端API执行命令
 * @param terminal 终端实例
 * @param command 要执行的命令
 * @param logService 日志服务
 * @param commandService 命令服务
 * @returns 命令是否成功执行
 */
export async function executeCommandInTerminal(
    terminal: ITerminalInstance,
    command: string,
    logService: ILogService,
    commandService?: ICommandService
): Promise<boolean> {

    // 尝试多种方法执行命令
    const methods = [
        // 方法1: 使用runCommand方法
        async () => {
            try {
                terminal.runCommand(command, true);
                return true;
            } catch (error) {
                logService.error(`使用terminal.runCommand执行命令失败:`, error);
                return false;
            }
        },

        // 方法2: 使用sendText方法
        async () => {
            try {
                terminal.sendText(command, true);
                return true;
            } catch (error) {
                logService.error(`使用terminal.sendText执行命令失败:`, error);
                return false;
            }
        },

        // 方法3: 使用sendSequence命令
        async () => {
            if (!commandService) return false;
            try {
                await commandService.executeCommand('workbench.action.terminal.sendSequence', { text: `${command}\n` }, terminal);
                return true;
            } catch (error) {
                logService.error(`使用workbench.action.terminal.sendSequence执行命令失败:`, error);
                return false;
            }
        },

        // 方法4: 使用内部实例的runCommand方法
        async () => {
            try {
                const instance = await getTerminalInstance(terminal, logService);
                if (instance && typeof instance.runCommand === 'function') {
                    await instance.runCommand(command, true);
                    return true;
                }
                return false;
            } catch (error) {
                logService.error(`使用terminal._instance.runCommand执行命令失败:`, error);
                return false;
            }
        }
    ];

    // 依次尝试所有方法
    for (const method of methods) {
        if (await method()) {
            // 命令执行成功，等待一段时间确保命令开始执行
            return true;
        }
    }

    logService.error('所有命令执行方法都失败');
    return false;
}

/**
 * 使用命令行工具下载文件
 * @param url 下载地址
 * @param targetPath 目标文件路径
 * @param progress 进度报告器
 * @param logService 日志服务
 * @param fileService 文件服务
 * @param commandService 命令服务
 * @param terminal 终端实例
 * @returns 是否成功
 */
export async function downloadWithCommandLine(
    url: string,
    targetPath: string,
    progress: IProgress<IProgressStep> | undefined,
    logService: ILogService,
    fileService: IFileService,
    commandService: ICommandService,
    terminal: ITerminalInstance | undefined,
    isRemotePath: boolean,
    archiveFileName: string,
    ptKey: string
): Promise<boolean> {
    return new Promise<boolean>(async (resolve, reject) => {
        try {
            logService.info(`使用命令行工具下载文件: ${url} 到 ${targetPath}`);

            // 根据不同平台使用不同的命令
            let downloadCommand = '';

            if (isWindows) {
                logService.info(`Windows平台下载开始`);

                // 创建目录的PowerShell命令 - 使用完整路径和正确的路径格式
                // 处理Windows路径，确保路径格式正确
                const targetDir = path.dirname(targetPath);
                // 使用PowerShell的New-Item命令创建目录，使用最简单的命令格式
                const mkdirCommand = `New-Item -Path "${targetDir}" -ItemType Directory -Force -ErrorAction SilentlyContinue`;

                logService.info(`创建目录命令: ${mkdirCommand}`);

                // 尝试先创建目录
                if (terminal) {
                    try {
                        await executeCommandInTerminal(terminal, mkdirCommand, logService, commandService);
                        logService.info(`创建目录命令执行完成`);
                    } catch (error) {
                        logService.error(`创建目录失败: ${error}`);
                    }
                } else {
                    logService.info(`没有可用的终端实例，跳过创建目录`);
                }
                downloadCommand = `curl "${url}" -o "${targetPath.replace(/\\/g, '\\\\')}"`;
                // 备用命令1：使用PowerShell的Invoke-WebRequest
                // 使用简单的wget命令下载文件
                const invokeWebRequestCommand = `wget "${url}" -O "${targetPath.replace(/\\/g, '\\\\')}" -q`;

                // 备用命令2：使用Start-BitsTransfer（PowerShell的BITS传输）
                // 使用最简单的BITS传输命令下载文件
                const bitsTransferCommand = `Import-Module BitsTransfer -ErrorAction SilentlyContinue; Start-BitsTransfer -Source '${url}' -Destination '${targetPath.replace(/\\/g, '\\\\')}' -Priority High; Write-Host "下载完成"`;

                // 存储备用命令
                (terminal as any)._backupDownloadCommands = [invokeWebRequestCommand, bitsTransferCommand];
            } else if (isMacintosh || isLinux) {
                // macOS 和 Linux 平台使用 curl 命令
                // 添加 -v 参数以获取详细输出，并使用 --fail 参数在 HTTP 错误时失败
                // 首先确保目标目录存在
                // 在远程环境中，targetPath 已经是 URI 字符串，需要提取路径部分
                const targetPathForCommand = isRemotePath
                    ? URI.parse(targetPath).path // 从 URI 中提取路径部分
                    : targetPath;


                // 确保目标目录存在，然后下载文件
                // 在远程环境中，需要确保下载路径是一个文件路径，而不是目录路径
                if (isRemotePath) {
                    // 构建下载目录和文件路径
                    const downloadDir = targetPathForCommand; // 在远程环境中，targetPathForCommand 已经是目录路径
                    const downloadFilePath = path.posix.join(downloadDir, archiveFileName);

                    // 构建下载命令，确保目标目录存在，然后下载文件到指定的文件路径
                    downloadCommand = `mkdir -p '${downloadDir}' && curl -L '${url}' -o '${downloadFilePath}' -v --fail`;

                    logService.info(`远程环境下载命令: mkdir -p '${downloadDir}' && curl -L '${url}' -o '${downloadFilePath}' -v --fail`);
                } else {
                    // 本地环境，保持原有逻辑
                    downloadCommand = `mkdir -p '${path.dirname(targetPathForCommand)}' && curl -L '${url}' -o '${targetPathForCommand}' -v --fail`;
                }
            } else {
                // 不支持的平台
                throw new Error(localize('unsupportedPlatform', "不支持的平台"));
            }

            // 执行下载命令
            logService.info(`执行下载命令: ${downloadCommand}`);

            // 直接使用executeCommandInTerminal函数执行命令
            logService.info('使用executeCommandInTerminal函数执行命令');
            const success = await executeCommandInTerminal(terminal!, downloadCommand, logService, commandService);

            if (!success) {
                logService.warn('终端命令执行失败，尝试使用备用命令');

                // 检查是否有备用命令
                if (isWindows) {
                    // 准备备用命令
                    const backupCommands = [];

                    // 备用命令1：使用PowerShell的Invoke-WebRequest
                    backupCommands.push(`wget "${url}" -O "${targetPath.replace(/\\/g, '\\\\')}" -q`);

                    // 备用命令2：使用Start-BitsTransfer
                    backupCommands.push(`Import-Module BitsTransfer -ErrorAction SilentlyContinue; Start-BitsTransfer -Source '${url}' -Destination '${targetPath.replace(/\\/g, '\\\\')}' -Priority High; Write-Host "下载完成"`);

                    // 尝试所有备用命令，直到成功或全部失败
                    let backupSuccess = false;

                    for (let i = 0; i < backupCommands.length; i++) {
                        const backupCommand = backupCommands[i];
                        logService.info(`尝试备用下载命令 ${i + 1}/${backupCommands.length}: ${backupCommand}`);

                        try {
                            backupSuccess = await executeCommandInTerminal(terminal!, backupCommand, logService, commandService);
                            if (backupSuccess) {
                                logService.info(`备用下载命令 ${i + 1} 执行成功`);
                                break;
                            } else {
                                logService.warn(`备用下载命令 ${i + 1} 执行失败，尝试下一个`);
                            }
                        } catch (error) {
                            logService.error(`备用下载命令 ${i + 1} 执行出错: ${error}`);
                        }
                    }

                    if (!backupSuccess) {
                        throw new Error('所有备用下载命令都失败');
                    }
                } else {
                    throw new Error('终端命令执行失败，且没有可用的备用命令');
                }
            }

            // 给命令一点执行时间
            await new Promise<void>(resolve => setTimeout(resolve, 500));

            // 使用抽离出来的函数检查文件下载状态
            await checkFileDownloadStatus(targetPath, fileService, logService, isRemotePath, progress);

            // 报告下载完成，但整体进度只到80%，预留20%给解压过程
            if (progress) {
                progress.report({
                    message: localize('downloadComplete', "下载完成，准备解压..."),
                    increment: 0 // 不增加进度，保持在当前进度
                });
            }

            // 成功
            resolve(true);
        } catch (error) {
            logService.error(`命令行下载失败: ${error}`);
            reject(error);
        }
    });
}

/**
 * 使用命令解压文件
 * @param targetPath 目标路径
 * @param archiveFileName 压缩文件名
 * @param logService 日志服务
 * @param fileService 文件服务
 * @param commandService 命令服务
 * @param terminal 终端实例
 * @returns 是否成功
 */
export async function extractWithTerminal(
    targetPath: string,
    archiveFileName: string,
    logService: ILogService,
    fileService: IFileService,
    commandService: ICommandService,
    terminal: ITerminalInstance,
    isRemotePath: boolean,
    progress?: IProgress<{ message?: string; increment?: number }>
): Promise<boolean> {
    let attempt = 0;
    let isExtracted = false;
    // 只在循环外获取一次 zip 包路径和 URI，重试时始终使用同一份
    const { archiveFilePath, archiveFileUri, targetPathForCommand } = getPathsForEnv(targetPath, archiveFileName, isRemotePath, logService);
    while (attempt < 3 && !isExtracted) {
        attempt++;
        logService.info(`[extractWithTerminal] 第${attempt}次尝试解压...`);

        // 检查文件是否存在
        if (!await fileService.exists(archiveFileUri)) {
            logService.error(`文件不存在: ${archiveFilePath}`);
            throw new Error(`文件不存在: ${archiveFilePath}`);
        }

        logService.info(`准备解压文件: ${archiveFilePath} 到 ${targetPath}`);

        // 生成解压命令
        let success = false;
        if (isWindows) {
            success = await executeWindowsExtractScript(archiveFilePath, targetPathForCommand, logService, terminal, commandService, fileService);
        } else {
            // 针对 mac 下 zip 文件，分步执行命令
            const fileExtension = path.extname(archiveFileName).toLowerCase();
            let fullCommand = ''
            if (fileExtension === '.zip') {
                if (isRemotePath) {
                    fullCommand = `mkdir -p '${targetPathForCommand}' && TEMP_DIR="${targetPathForCommand}/${EXTRACT_TEMP}" && mkdir -p "$TEMP_DIR" && unzip -o '${archiveFilePath}' -d "$TEMP_DIR" -x "__MACOSX/*" && rm -rf "$TEMP_DIR/__MACOSX" && sync && sleep 1 && cd "$TEMP_DIR" && DIRS_COUNT=$(find . -maxdepth 1 -type d -not -path "." | wc -l) && FILES_COUNT=$(find . -maxdepth 1 -type f | wc -l) && if [ $DIRS_COUNT -eq 1 ] && [ $FILES_COUNT -eq 0 ]; then TOP_DIR=$(find . -maxdepth 1 -type d -not -path "." | head -1) && cp -a "$TOP_DIR/." '${targetPathForCommand}'; else cp -a "$TEMP_DIR/." '${targetPathForCommand}'; fi && rm -rf "$TEMP_DIR" && cd '${targetPathForCommand}' && ls -la`
                } else {
                    fullCommand = `mkdir -p '${targetPath}' && TEMP_DIR="${targetPath}/${EXTRACT_TEMP}" && mkdir -p "$TEMP_DIR" && unzip -o '${archiveFilePath}' -d "$TEMP_DIR" -x "__MACOSX/*" && rm -rf "$TEMP_DIR/__MACOSX" && sync && sleep 1 && cd "$TEMP_DIR" && DIRS_COUNT=$(find . -maxdepth 1 -type d -not -path "." | wc -l) && FILES_COUNT=$(find . -maxdepth 1 -type f | wc -l) && if [ $DIRS_COUNT -eq 1 ] && [ $FILES_COUNT -eq 0 ]; then TOP_DIR=$(find . -maxdepth 1 -type d -not -path "." | head -1) && cp -a "$TOP_DIR/." '${targetPath}'; else cp -a "$TEMP_DIR/." '${targetPath}'; fi && rm -rf "$TEMP_DIR" && cd '${targetPath}' && ls -la`
                }
                logService.info(`执行解压全流程命令: ${fullCommand}`);
                success = await executeCommandInTerminal(terminal!, fullCommand, logService, commandService);
                if (!success) {
                    logService.error(`解压全流程命令执行失败`);
                    throw new Error(`解压全流程命令执行失败`);
                }
            } else {
                const extractCommand = getExtractCommand({
                    archiveFilePath,
                    targetPath,
                    targetPathForCommand,
                    archiveFileName,
                    isRemotePath,
                    logService
                });
                logService.info(`执行解压命令: ${extractCommand}`);
                success = await executeCommandInTerminal(terminal!, extractCommand, logService, commandService);
            }
        }

        // 失败时 Windows 尝试备用命令
        if (!success && isWindows) {
            logService.error('终端解压命令执行失败，尝试使用备用方法');
            const backupCommand = generateWindowsExtractCommand(archiveFilePath, targetPathForCommand);
            logService.info(`尝试备用解压命令: ${backupCommand}`);
            try {
                success = await executeCommandInTerminal(terminal!, backupCommand, logService, commandService);
                if (success) {
                    logService.info(`备用解压命令执行成功`);
                } else {
                    throw new Error('备用解压命令执行失败');
                }
            } catch (error) {
                logService.error(`备用解压命令执行出错: ${error}`);
                throw new Error('所有解压方法都失败');
            }
        } else if (!success) {
            throw new Error('终端解压命令执行失败');
        }

        // 增加等待时间，确保解压命令有足够时间完成
        await new Promise<void>(resolve => setTimeout(resolve, 2000));
        logService.info(`等待解压完成...`);

        //是否存在 isExtracted 文件
        isExtracted = await checkExtractStatus(targetPath, fileService, logService, isRemotePath, progress);

        // 等待额外时间确保所有文件系统操作完成
        logService.info('等待额外时间确保所有文件系统操作完成...', isExtracted);
        await new Promise<void>(resolve => setTimeout(resolve, 2000));

        // 输出目标目录结构
        logService.info('输出目标目录结构...');
        if (isWindows) {
            const listDirCommand = `ls '${targetPathForCommand}'`;
            await executeCommandInTerminal(terminal, listDirCommand, logService, commandService);
        } else {
            const listDirCommand = `ls -lR '${targetPathForCommand}'`;
            await executeCommandInTerminal(terminal, listDirCommand, logService, commandService);
        }

        // 添加错误处理和日志记录
        logService.info(`目录列表命令执行完成。如果没有看到预期的输出，请检查命令是否正确执行。`);

        if (!isExtracted) {
            logService.warn(`[extractWithTerminal] 第${attempt}次解压后 isExtracted=false，准备重试...`);
        }
    }

    if (isExtracted) {
        // 删除压缩文件
        const { archiveFilePath, archiveFileUri } = getPathsForEnv(targetPath, archiveFileName, isRemotePath, logService);
        logService.info(`删除压缩文件: ${archiveFilePath}`);
        try {
            await fileService.del(archiveFileUri);
            logService.info(`成功删除压缩文件: ${archiveFilePath}`);
        } catch (error) {
            logService.error(`删除压缩文件失败: ${error}`);
        }
    } else {
        logService.error(`[extractWithTerminal] 已重试3次，isExtracted 仍为 false，终止处理。`);
    }

    logService.info('解压流程完成');
    return isExtracted;
}

/**
 * 路径与 URI 统一处理
 */
function getPathsForEnv(
    targetPath: string,
    archiveFileName: string,
    isRemotePath: boolean,
    logService: ILogService
) {
    let archiveFilePath: string;
    let archiveFileUri: any;
    let targetPathForCommand: string;

    if (isRemotePath) {
        const targetUri = URI.parse(targetPath);
        targetPathForCommand = targetUri.path;
        archiveFilePath = path.posix.join(targetPathForCommand, archiveFileName);
        archiveFileUri = URI.parse(targetUri.scheme + '://' + targetUri.authority + archiveFilePath);
        logService.info(`远程环境：压缩文件路径 ${archiveFilePath}, URI: ${archiveFileUri}`);
    } else {
        targetPathForCommand = targetPath;
        archiveFilePath = path.join(targetPath, archiveFileName);
        archiveFileUri = URI.file(archiveFilePath);
        logService.info(`本地环境：压缩文件路径 ${archiveFilePath}, URI: ${archiveFileUri}`);
    }
    return { archiveFilePath, archiveFileUri, targetPathForCommand };
}

/**
 * 解压命令生成
 */
function getExtractCommand(params: {
    archiveFilePath: string;
    targetPath: string;
    targetPathForCommand: string;
    archiveFileName: string;
    isRemotePath: boolean;
    logService: ILogService;
}): string {
    const { archiveFilePath, targetPath, targetPathForCommand, archiveFileName, isRemotePath, logService } = params;
    const fileExtension = path.extname(archiveFileName).toLowerCase();
    const fileNameLower = archiveFileName.toLowerCase();

    if (isWindows) {
        logService.info(`Windows平台解压，使用脚本方式`);
        return `powershell -ExecutionPolicy Bypass -File "${targetPath}\\extract_script.ps1"`;
    } else if (isMacintosh || isLinux) {
        if (fileExtension === '.zip') {
            if (isRemotePath) {
                return `mkdir -p '${targetPathForCommand}' && TEMP_DIR="${targetPathForCommand}/${EXTRACT_TEMP}" && mkdir -p "$TEMP_DIR" && echo "创建临时目录: $TEMP_DIR" && unzip -o '${archiveFilePath}' -d "$TEMP_DIR" -x "__MACOSX/*" && rm -rf "$TEMP_DIR/__MACOSX" && sync && sleep 1 && cd "$TEMP_DIR" && DIRS_COUNT=$(find . -maxdepth 1 -type d -not -path "." | wc -l) && FILES_COUNT=$(find . -maxdepth 1 -type f | wc -l) && echo "临时目录内容:" && ls -la && if [ $DIRS_COUNT -eq 1 ] && [ $FILES_COUNT -eq 0 ]; then TOP_DIR=$(find . -maxdepth 1 -type d -not -path "." | head -1) && echo "发现单一顶级目录: $TOP_DIR，使用其内容" && cp -a "$TOP_DIR/." '${targetPathForCommand}' && echo "复制完成，目标目录:" && ls -la '${targetPathForCommand}'; else echo "发现多个目录或文件在顶级目录中，使用所有内容" && cp -a "$TEMP_DIR/." '${targetPathForCommand}' && echo "复制完成，目标目录:" && ls -la '${targetPathForCommand}'; fi && rm -rf "$TEMP_DIR" && cd '${targetPathForCommand}' && echo "解压完成，目录结构:" && ls -la`;
            } else {
                return `mkdir -p '${targetPath}' && TEMP_DIR="${targetPathForCommand}/${EXTRACT_TEMP}" && mkdir -p "$TEMP_DIR" && unzip -o '${archiveFilePath}' -d "$TEMP_DIR" -x "__MACOSX/*" && rm -rf "$TEMP_DIR/__MACOSX" && sync && sleep 1 && cd "$TEMP_DIR" && DIRS_COUNT=$(find . -maxdepth 1 -type d -not -path "." | wc -l) && FILES_COUNT=$(find . -maxdepth 1 -type f | wc -l) && if [ $DIRS_COUNT -eq 1 ] && [ $FILES_COUNT -eq 0 ]; then TOP_DIR=$(find . -maxdepth 1 -type d -not -path "." | head -1) && cp -a "$TOP_DIR/." '${targetPath}'; else cp -a "$TEMP_DIR/." '${targetPath}'; fi && rm -rf "$TEMP_DIR" && cd '${targetPath}' && ls -la`;
            }
        } else if (fileExtension === '.gz' && !fileNameLower.endsWith('.tar.gz')) {
            return `cd '${targetPathForCommand}' && ls -la '${archiveFilePath}' && gunzip -c '${archiveFilePath}' > '${targetPathForCommand}/${path.basename(archiveFileName, '.gz')}'`;
        } else if (fileNameLower.endsWith('.tar.gz') || fileExtension === '.tgz') {
            return `mkdir -p '${targetPathForCommand}' && cd '${targetPathForCommand}' && ls -la '${archiveFilePath}' && tar -xzf '${archiveFilePath}' -C . && echo "检查是否有单一顶级目录..." && DIRS=$(find . -maxdepth 1 -type d | grep -v "^\\.$" | wc -l) && if [ $DIRS -eq 1 ]; then TOPDIR=$(find . -maxdepth 1 -type d | grep -v "^\\.$") && echo "发现单一顶级目录: $TOPDIR，移动其内容到当前目录" && mv "$TOPDIR"/* . 2>/dev/null || true && mv "$TOPDIR"/.* . 2>/dev/null || true && rmdir "$TOPDIR" 2>/dev/null || echo "无法删除目录，可能包含隐藏文件"; else echo "没有单一顶级目录或有多个目录，保持原样"; fi`;
        } else if (fileExtension === '.tar') {
            return `mkdir -p '${targetPathForCommand}' && cd '${targetPathForCommand}' && ls -la '${archiveFilePath}' && tar -xf '${archiveFilePath}' -C . && echo "检查是否有单一顶级目录..." && DIRS=$(find . -maxdepth 1 -type d | grep -v "^\\.$" | wc -l) && if [ $DIRS -eq 1]; then TOPDIR=$(find . -maxdepth 1 -type d | grep -v "^\\.$") && echo "发现单一顶级目录: $TOPDIR，移动其内容到当前目录" && mv "$TOPDIR"/* . 2>/dev/null || true && mv "$TOPDIR"/.* . 2>/dev/null || true && rmdir "$TOPDIR" 2>/dev/null || echo "无法删除目录，可能包含隐藏文件"; else echo "没有单一顶级目录或有多个目录，保持原样"; fi`;
        } else if (fileExtension === '.rar') {
            return `mkdir -p '${targetPathForCommand}' && cd '${targetPathForCommand}' && ls -la '${archiveFilePath}' && if command -v unrar >/dev/null 2>&1; then unrar x -o+ '${archiveFilePath}' .; else echo "需要安装 unrar 来解压此文件类型"; fi`;
        } else if (fileExtension === '.7z') {
            return `mkdir -p '${targetPathForCommand}' && cd '${targetPathForCommand}' && ls -la '${archiveFilePath}' && if command -v 7z >/dev/null 2>&1; then 7z x '${archiveFilePath}' -o.; else echo "需要安装 p7zip 来解压此文件类型"; fi`;
        } else {
            return `mkdir -p '${targetPathForCommand}' && cd '${targetPathForCommand}' && file '${archiveFilePath}' && echo "未知文件类型，尝试使用 tar 解压" && tar -xf '${archiveFilePath}' -C . || echo "解压失败，不支持的文件类型"`;
        }
    } else {
        throw new Error(localize('unsupportedPlatform', "不支持的平台"));
    }
}

/**
 * 生成并执行Windows解压脚本
 * @param archiveFilePath 压缩文件路径
 * @param targetPath 解压目标路径
 * @param logService 日志服务
 * @param terminal 终端实例
 * @param commandService 命令服务
 * @returns 解压是否成功
 */
async function executeWindowsExtractScript(
    archiveFilePath: string,
    targetPath: string,
    logService: ILogService,
    terminal: ITerminalInstance,
    commandService: ICommandService,
    fileService: IFileService
): Promise<boolean> {
    try {
        // 1. 创建临时目录（在目标目录下）
        const tempDirName = `joycoder_temp_${Date.now()}`;
        const tempDirPath = path.join(targetPath, tempDirName);
        const normalizedTempPath = tempDirPath.replace(/\//g, '\\');
        const normalizedArchivePath = archiveFilePath.replace(/\//g, '\\');
        const normalizedTargetPath = targetPath.replace(/\//g, '\\');

        // 确保目标路径存在
        logService.info(`确保目标目录存在: ${normalizedTargetPath}`);
        const createTargetDirCmd = `New-Item -Path "${normalizedTargetPath}" -ItemType Directory -Force -ErrorAction SilentlyContinue`;
        await executeCommandInTerminal(terminal, createTargetDirCmd, logService, commandService);

        // 创建临时目录
        logService.info(`创建临时解压目录: ${normalizedTempPath}`);
        const createTempDirCmd = `New-Item -Path "${normalizedTempPath}" -ItemType Directory -Force -ErrorAction SilentlyContinue`;
        await executeCommandInTerminal(terminal, createTempDirCmd, logService, commandService);

        // 2. 使用Shell.Application对象解压到临时目录
        logService.info(`解压文件到临时目录: ${normalizedArchivePath} -> ${normalizedTempPath}`);
        const extractCmd = `
        $shell = New-Object -ComObject Shell.Application
        $zip = $shell.NameSpace('${normalizedArchivePath}')
        $destination = $shell.NameSpace('${normalizedTempPath}')
        $zipItems = $zip.Items()
        $destination.CopyHere($zipItems, 1556)
        Write-Host "解压文件完成"
        `;
        await executeCommandInTerminal(terminal, extractCmd, logService, commandService);

        // 3. 检查临时目录内容
        logService.info(`检查临时目录内容...`);
        const checkContentCmd = `
        $zipFileName = [System.IO.Path]::GetFileName('${normalizedArchivePath}')
        $allDirs = @(Get-ChildItem -Path '${normalizedTempPath}' -Directory | Where-Object { $_.Name -ne "__MACOSX" })
        $allFiles = @(Get-ChildItem -Path '${normalizedTempPath}' -File | Where-Object {
            $_.Name -ne $zipFileName -and
            -not $_.Name.EndsWith('.tmp') -and
            -not $_.Name.EndsWith('.temp') -and
            -not $_.Name.StartsWith('~') -and
            -not $_.Name.StartsWith('._')
        })

        Write-Host "临时目录中发现 $($allDirs.Count) 个目录和 $($allFiles.Count) 个文件"

        if ($allDirs.Count -gt 0) {
            Write-Host "目录列表:"
            $allDirs | ForEach-Object { Write-Host "  - $($_.Name)" }
        }

        if ($allFiles.Count -gt 0) {
            Write-Host "文件列表:"
            $allFiles | ForEach-Object { Write-Host "  - $($_.Name)" }
        }
        `;
        await executeCommandInTerminal(terminal, checkContentCmd, logService, commandService);

        // 4. 移动文件到目标目录
        logService.info(`移动文件到目标目录...`);
        const moveFilesCmd = `
        $zipFileName = [System.IO.Path]::GetFileName('${normalizedArchivePath}')
        $allItems = @(Get-ChildItem -Path '${normalizedTempPath}' -Force)
        $validItems = @($allItems | Where-Object {
            $_.Name -ne "__MACOSX" -and
            $_.Name -ne $zipFileName -and
            -not $_.Name.EndsWith('.tmp') -and
            -not $_.Name.EndsWith('.temp') -and
            -not $_.Name.StartsWith('~') -and
            -not $_.Name.StartsWith('._')
        })

        if ($validItems.Count -eq 1 -and $validItems[0].PSIsContainer) {
            # 只有一个顶级目录，移动其内容
            $topDir = $validItems[0].FullName
            Write-Host "发现单一顶级目录: $topDir，移动其内容到目标目录"

            Get-ChildItem -Path $topDir -Force | ForEach-Object {
                $destPath = Join-Path -Path '${normalizedTargetPath}' -ChildPath $_.Name
                if (Test-Path -Path $destPath) {
                    Write-Host "目标已存在，删除: $destPath"
                    Remove-Item -Path $destPath -Recurse -Force -ErrorAction SilentlyContinue
                }

                Write-Host "移动: $($_.Name) -> $destPath"
                Move-Item -Path $_.FullName -Destination $destPath -Force
            }
        } else {
            # 多个项目或文件，移动所有内容
            Write-Host "发现多个项目或文件，将所有内容移动到目标目录"

            $validItems | ForEach-Object {
                $destPath = Join-Path -Path '${normalizedTargetPath}' -ChildPath $_.Name
                if (Test-Path -Path $destPath) {
                    Write-Host "目标已存在，删除: $destPath"
                    Remove-Item -Path $destPath -Recurse -Force -ErrorAction SilentlyContinue
                }

                Write-Host "移动: $($_.Name) -> $destPath"
                Move-Item -Path $_.FullName -Destination $destPath -Force
            }
        }

        Write-Host "文件移动完成"

        Start-Sleep -Seconds 2

        Write-Host "开始清理操作..."

        # 删除临时目录
        Write-Host "正在删除临时目录: ${normalizedTempPath}"
        Remove-Item -Path '${normalizedTempPath}' -Recurse -Force -ErrorAction SilentlyContinue

        # 删除原始zip文件
        Write-Host "正在删除原始zip文件: ${normalizedArchivePath}"
        Remove-Item -Path '${normalizedArchivePath}' -Force -ErrorAction SilentlyContinue

        Write-Host "清理操作完成"
        `;
        await executeCommandInTerminal(terminal, moveFilesCmd, logService, commandService);

        logService.info(`Windows解压操作完成，等待用户确认后进行清理`);
        return true;
    } catch (error) {
        logService.error(`执行Windows解压操作失败: ${error}`);
        return false;
    }
}
