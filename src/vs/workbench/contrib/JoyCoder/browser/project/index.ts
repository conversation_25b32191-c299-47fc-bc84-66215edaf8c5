/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/**
 * 新建项目功能 - 提供创建新项目的界面
 * 在File菜单中添加"New Project"选项
 * 点击后弹出对话框让用户输入项目信息
 */
import './newProjectCommands.js';


/**
 * 新建项目服务 - 提供新建项目相关的API
 * 包括获取模板列表、下载和解压模板等功能
 */
import '../service/newProjectService.js'
import '../service/newProjectMainService.js'


/**
 * 模板下载器 - 处理项目模板的下载和解压
 * 在新窗口打开时检测是否有模板需要下载
 * 提供多种下载方式和用户提示
 */
import './templateDownloader.js';


/**
 * 项目设置功能 - 提供项目设置界面
 * 允许用户查看和修改项目的配置信息
 * 包括服务器信息、数据库配置等
 */
import './projectSettingsCommands.js';


/**
 * 删除项目功能 - 提供删除项目的确认对话框和删除逻辑
 * 接受项目数据作为参数，显示确认对话框
 * 提供选项让用户选择是否同时删除本地文件
 */
import './deleteProjectCommands.js';

/**
 * 注册自定义删除对话框服务
 * 这个服务用于显示删除项目的确认对话框
 */
import './customDeleteDialog.js';


