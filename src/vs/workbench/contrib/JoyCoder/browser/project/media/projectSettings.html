<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Settings</title>
    <style>

        .icon {
            width: 1em;
            height: 1em;
            vertical-align: -0.15em;
            fill: currentColor;
            overflow: hidden;
        }

        body {
            font-family: PingFang SC, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            margin: 0;
            padding: 0;
            overflow: hidden;
            font-size: 12px;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--vscode-editor-background);
            z-index: 9999;
            transition: opacity 0.3s ease-out;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            pointer-events: none;
        }

        .overlay.active {
            opacity: 1;
            pointer-events: all;
        }

        .loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid var(--vscode-progressBar-background);
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .loading-text {
            color: var(--vscode-editor-foreground);
            font-size: 16px;
        }

        .project-settings-container {
            display: flex;
            flex-direction: column;
            padding: 24px;
            height:100vh;
            box-sizing: border-box;
            overflow-y: auto;
        }

        .project-settings-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
            flex: 1;
        }

        .form-group {
            display: flex;
            align-items: center;
            /*gap: 10px;*/
            /*margin-bottom: 16px;*/
        }

        .description {
            align-items: flex-start;
        }

        .form-group>label {
            width: 80px;
            color: var(--vscode-input-label);
            font-size: 12px;
            font-weight: normal;
            line-height: 28px;
            margin-right: 8px;
            /*padding-top: 4px;*/
            flex-shrink: 0;
        }

        input[type="text"],
        input[type="password"],
        select,
        textarea {
            flex: 1;
            padding: 4px 6px;
            border: 1px solid var(--vscode-input-border, #c8c8c8);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-settings-textInputForeground);
            border-radius: 2px;
            box-sizing: border-box;
            font-size: 12px;
            font-weight: normal;
        }

        input[type="text"]::placeholder,
        textarea::placeholder {
            color: var(--vscode-input-label-placeholder);
            opacity: 0.6;
        }

        input[type="text"]:focus,
        input[type="password"]:focus,
        select:focus,
        textarea:focus {
            outline: 1px solid var(--vscode-focusBorder);
            border-color: var(--vscode-focusBorder);
        }

        .error-border {
            border: 1px solid var(--vscode-inputValidation-errorBorder, #f48771) !important;
        }

        .error-border:focus {
            outline: 1px solid var(--vscode-inputValidation-errorBorder, #f48771) !important;
        }

        textarea {
            resize: vertical;
            /*min-height: 80px;*/
            line-height: 18px;
        }

        .radio-group {
            display: flex;
            gap: 30px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            height: 24px;
            margin-right: 40px;
        }

        .radio-option input[type="radio"] {
            margin: 0 5px 0 0;
            vertical-align: middle;
            position: relative;

            appearance: none;
            width: 12px;
            height: 12px;
            border: 1px solid var(--vscode-input-border, #888);
            border-radius: 50%;
            /*margin: 0;*/
            cursor: pointer;
        }

        .radio-option input[type="radio"]:checked {
            background: var(--vscode-editor-background, #fff);
            border: 1.5px solid var(--vscode-focusBorder, #005fcc);
            position: relative;
        }

        .radio-option input[type="radio"]:checked::after {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--vscode-editor-foreground, #000);
            /* 黑色背景 */
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .radio-option input[type="radio"]:focus {
            outline: 1.5px solid var(--vscode-focusBorder, #005fcc);
        }

        .radio-option label {
            display: inline-block;
            vertical-align: middle;
            line-height: 24px;
            padding-top: 0;
        }

        .project-settings-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            border-top: 1px solid var(--vscode-panel-border, #333);
            padding: 12px 16px;
            box-sizing: border-box;
            overflow: hidden;
            width: 100%;
            z-index: 100;
            text-align: right;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);

        }

        button.primary {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }

        button.primary:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            white-space: nowrap;
        }

        button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .server-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 28px;
            line-height: 28px;
            width: 100%;
        }

        .upgrade-button {
            color: var(--vscode-textLink-foreground);
            cursor: pointer;
            text-decoration: none;
        }

        .upgrade-button:hover {
            text-decoration: underline;
        }

        .db-section {
            margin-left: 88px;
            /*margin-top: 10px;*/
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        /*.db-label {*/
        /*    width: 100px;*/
        /*    text-align: right;*/
        /*}*/

        .db-user-control {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
        }

        .db-password-label {
            margin-left: 10px;
            width: auto;
        }

        .test-connection {
            display: flex;
            align-items: flex-start;
            /*gap: 10px;*/
            margin-top: -5px;
        }

        .success-icon {
            color: var(--vscode-testing-iconPassed, #4caf50);
        }

        .register-button {
            color: var(--vscode-textLink-foreground);
            cursor: pointer;
            text-decoration: none;
            margin-left: 10px;
        }

        .register-button:hover {
            text-decoration: underline;
        }

        /* 连接状态 */
        #testConnectionButton {
            vertical-align: top;
            margin-right: 0;
            font-size: 12px;
            line-height: 18px;
        }

        #connectionStatus {
            display: inline-flex !important;
            align-items: flex-start;
            gap: 4px;
            vertical-align: top;
            margin-left: 8px;
            font-size: 14px;
            line-height: 18px;
            max-width: 75%;
            word-break: break-all;
        }

        /* 在 style 标签最后添加 */
        .version-custom-select {
            width: 100%;
            min-width: 120px;
            /* max-width: 150px; */
            /* width: 120px !important; */
            display: inline-block;
            /* z-index: 200; */
        }

        .version-custom-select .template-version-input {
            cursor: pointer;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 2px;
            font-size: 12px;
            padding: 0 8px;
            height: 28px;
            width: 100%;
            min-width: 110px;
            /* max-width: 150px; */
            box-sizing: border-box;
            line-height: 28px;
        }

        .version-dropdown-panel {
            position: absolute;
            left: 0;
            right: 0;
            top: 100%;
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 2px;
            box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 1);
            z-index: 9999;
            max-height: 200px;
            overflow-y: auto;
            display: none;
            text-align: left;
            padding:0;
        }

        .version-custom-select.active .version-dropdown-panel {
            display: block !important;
        }

        .version-dropdown-item {
            padding-top: var(--dropdown-padding-top);
            padding-bottom: var(--dropdown-padding-bottom);
            padding-left: 1px;
            padding-right: 1px;
            height: 26px;
            line-height: 26px;
            padding: 0px 6px;
            cursor: pointer;
            outline: none;
            display: block;
            color: var(--vscode-dropdown-foreground);
            background: transparent;
            transition: background 0.2s;
            color: var(--vscode-input-foreground);
        }

        .version-dropdown-item:hover,
        .version-dropdown-item.selected {
            background: var(--vscode-list-hoverBackground);
            color: var(--vscode-dropdown-foreground);
        }

        /* 样式部分 */
        .version-custom-select {
            position: relative;
        }

        .version-dropdown-icon {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            pointer-events: none;
            display: flex;
            align-items: center;
            justify-content: center;

        }

        .version-dropdown-icon .icon-shang,
        .version-dropdown-icon .xia {
            color: var(--vscode-input-label-placeholder);
            font-size: 14px;
        }

        .version-custom-select .icon-shang {
            display: none;
        }

        .version-custom-select.active .icon-xia {
            display: none;
        }

        .version-custom-select.active .icon-shang {
            display: inline;
        }
        #dbType,
        .version-custom-select input.select-input {
            width: 100%;
            height: 28px;
            padding: 5px 12px;
            background: var(--vscode-editor-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border, #c8c8c8);
            border-radius: 4px;
            font-size: 12px;
            font-weight: normal;
        }
    </style>
</head>

<body>
    <div class="overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Saving settings...</div>
    </div>
    <div class="project-settings-container">
        <div class="project-settings-form">
            <div class="form-group">
                <label for="serverInfo">Server Info:</label>
                <div class="server-info">
                    <span id="serverInfo"></span>
                    <!-- <a href="#" class="upgrade-button" id="upgradeButton">Upgrade...</a> -->
                </div>
            </div>

            <div class="form-group">
                <label for="projectName">Project Name:</label>
                <!-- <input type="text" id="projectName" value="Hello World"> -->
                <div class="server-info">
                    <span id="projectName"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="devMode">Dev Mode:</label>
                <div class="server-info">
                    <span id="devMode"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="template">Templates:</label>
                <div class="server-info">
                    <span id="template"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="databaseMode">Database:</label>
                <div class="radio-group">
                    <div class="radio-option">
                        <input type="radio" id="dbAuto" name="databaseMode" value="0">
                        <label for="dbAuto">No Database</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="dbExisting" name="databaseMode" value="2">
                        <label for="dbExisting">Select Existing</label>
                    </div>
                    <div class="radio-option">
                        <input type="radio" id="dbCustom" name="databaseMode" value="3">
                        <label for="dbCustom">Custom</label>
                    </div>
                </div>
            </div>

            <div class="db-section" id="dbExistingSection" style="display: none;">
                <div class="form-group">
                    <label for="existingDbSelect" class="db-label" style="display: none;"></label>
                    <div style="width: 100%;">
                        <div class="custom-select version-custom-select">
                            <input id="existingDbSelect" type="text" class="template-version-input template-input select-input"
                                readOnly />
                            <span class="version-dropdown-icon">
                                <svg class="icon-xia" viewBox="0 0 1024 1024" width="14" height="14">
                                    <path
                                        d="M521.984 659.52a12.8 12.8 0 0 1-19.968 0L272.64 372.8a12.8 12.8 0 0 1 9.984-20.8h458.752a12.8 12.8 0 0 1 9.984 20.8l-229.376 286.72z"
                                        fill="var(--vscode-input-label-placeholder)"></path>
                                </svg>
                                <svg class="icon-shang" viewBox="0 0 1024 1024" width="14" height="14">
                                    <path
                                        d="M521.984 364.48a12.8 12.8 0 0 0-19.968 0l-229.376 286.72a12.8 12.8 0 0 0 9.984 20.8h458.752a12.8 12.8 0 0 0 9.984-20.8l-229.376-286.72z"
                                        fill="var(--vscode-input-label-placeholder)"></path>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="db-section" id="dbCustomSection" style="display: none;">
                <div class="form-group">
                    <label for="dbType" class="db-label">Type:</label>
                    <div style="width: 100%;">
                        <div class="custom-select version-custom-select">
                            <input id="dbType" type="text" class="template-version-input template-input" value="mysql"
                                readOnly />
                            <span class="version-dropdown-icon">
                                <svg class="icon-xia" viewBox="0 0 1024 1024" width="14" height="14">
                                    <path
                                        d="M521.984 659.52a12.8 12.8 0 0 1-19.968 0L272.64 372.8a12.8 12.8 0 0 1 9.984-20.8h458.752a12.8 12.8 0 0 1 9.984 20.8l-229.376 286.72z"
                                        fill="var(--vscode-input-label-placeholder)"></path>
                                </svg>
                                <svg class="icon-shang" viewBox="0 0 1024 1024" width="14" height="14">
                                    <path
                                        d="M521.984 364.48a12.8 12.8 0 0 0-19.968 0l-229.376 286.72a12.8 12.8 0 0 0 9.984 20.8h458.752a12.8 12.8 0 0 0 9.984-20.8l-229.376-286.72z"
                                        fill="var(--vscode-input-label-placeholder)"></path>
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label for="dbUrl" class="db-label">URL:</label>
                    <div style="width: 100%;">
                        <input type="text" id="dbUrl" style="width: 100%;" value="" placeholder="mysql-xxx.com:prot:my_test">
                    </div>
                </div>
                <div class="form-group">
                    <label for="dbUser" class="db-label">User Name:</label>
                    <div class="db-user-control">
                        <input type="text" id="dbUser" value="" style="flex: 1;" placeholder="user name">
                        <label for="dbPassword" class="db-password-label db-label">Password:</label>
                        <input type="password" id="dbPassword" value="" style="flex: 1;">
                    </div>
                </div>
                <!-- <div class="test-connection">
                    <button id="testConnectionButton" class="secondary"
							style="color: #2979ff; background: none; border: none; padding: 0; font-size: 1em;">Test Connection</button>
                    <span id="connectionStatus" class="success-icon">
						<span><svg class="icon" viewBox="0 0 1024 1024" width="14" height="14"><path d="M512 25.6A486.4 486.4 0 0 1 998.4 512 486.4 486.4 0 0 1 512 998.4 486.4 486.4 0 1 1 512 25.6z m-69.504 626.368L288.64 498.176l-54.272 54.272L415.36 733.44a38.4 38.4 0 0 0 48.256 4.928l6.016-4.928 316.8-316.8-54.272-54.272-289.664 289.664z" fill="#2EA043"></path></svg></span>
						<span style="color: #2EA043">Succeeded</span>
					</span>
                </div> -->
            </div>

            <div class="form-group">
                <label for="domain">Domain:</label>
                <div class="server-info">
                    <span id="domain"></span>
                </div>
            </div>

            <div class="form-group">
                <label for="exportPorts">Export Ports:</label>
                <!-- <input type="text" id="exportPorts" value="8080"> -->
                <div class="server-info">
                    <span id="exportPorts"></span>
                </div>
            </div>

            <div class="form-group description">
                <label for="description">Description:</label>
                <textarea id="description" rows="2"></textarea>
            </div>

            <div class="project-settings-buttons">
                <button class="secondary" id="cancelButton">Cancel</button>
                <button class="primary" id="saveButton">Save</button>
            </div>
        </div>
    </div>

    <script>
        try {
            // 等待VS Code API可用
            const vscode = acquireVsCodeApi();

            // 全局变量，用于跟踪保存操作是否在进行中
            let isSaving = false;

            let databaseList = [];

            let existingDbSelectDom;

            // 显示/隐藏加载状态
            function showLoadingState(show) {
                const overlay = document.getElementById('loadingOverlay');
                if (show) {
                    overlay.classList.add('active');
                    // 禁用所有输入元素
                    document.querySelectorAll('input, select, textarea, button').forEach(el => {
                        el.disabled = true;
                    });
                } else {
                    overlay.classList.remove('active');
                    // 启用所有输入元素
                    document.querySelectorAll('input, select, textarea, button').forEach(el => {
                        el.disabled = false;
                    });
                }
            }

            // 初始化下拉
            function initSelectOption(versionInput, list = [], selectedId = null) {
                if (list.length === 0) {
                    list = [{
                        value: '',
                        id: '',
                        label: 'No data available.'
                    }];
                }

                const versionSelectWrapper = versionInput.closest('.version-custom-select');
                if (versionSelectWrapper) {
                    let versionDropdown = versionSelectWrapper.querySelector('.version-dropdown-panel');
                    if (!versionDropdown) {
                        versionDropdown = document.createElement('div');
                        versionDropdown.className = 'dropdown-panel version-dropdown-panel';
                        versionDropdown.style.zIndex = '200';
                        versionSelectWrapper.appendChild(versionDropdown);
                    }

                    versionDropdown.innerHTML = '';
                    list.forEach((ver, idx) => {
                        ver.value = ver.id;
                        ver.label = ver.id ? `${ver.name} ${ver.internalEndpoint}:${ver.port}` : '';
                        const opt = document.createElement('div');
                        opt.className = 'version-dropdown-item';
                        opt.textContent = ver.label;
                        opt.setAttribute('data-value', ver.value);
                        if (selectedId === ver.id) {
                            opt.classList.add('selected');
                            versionInput.value = ver.label;
                            versionInput.setAttribute('data-selected-value', ver.value);
                        }
                        versionDropdown.appendChild(opt);

                        opt.addEventListener('click', (e) => {
                            e.stopPropagation();
                            versionInput.value = ver.label;
                            versionInput.setAttribute('data-selected-value', ver.value);
                            Array.from(versionDropdown.children).forEach(child => child.classList.remove('selected'));
                            opt.classList.add('selected');
                            versionDropdown.style.display = 'none';
                            versionSelectWrapper.classList.remove('active');
                            validateForm();
                        });
                    });

                    // 移除之前的事件监听器
                    versionInput.removeEventListener('click', versionInputClickHandler);

                    // 添加新的事件监听器
                    versionInput.addEventListener('click', versionInputClickHandler);

                    // 移除文档上之前的事件监听器
                    document.removeEventListener('click', documentClickHandler);

                    // 添加新的事件监听器
                    document.addEventListener('click', documentClickHandler);
                }
            }

            function versionInputClickHandler(e) {
                e.stopPropagation();
                const versionSelectWrapper = e.target.closest('.version-custom-select');
                const versionDropdown = versionSelectWrapper.querySelector('.version-dropdown-panel');
                document.querySelectorAll('.version-dropdown-panel').forEach(panel => {
                    if (panel !== versionDropdown) panel.style.display = 'none';
                });
                versionDropdown.style.display = versionDropdown.style.display === 'block' ? 'none' : 'block';
                versionSelectWrapper.classList.toggle('active');
                validateForm();
            }

            function documentClickHandler(e) {
                const versionSelectWrapper = document.querySelector('.version-custom-select');
                if (versionSelectWrapper && !versionSelectWrapper.contains(e.target)) {
                    const versionDropdown = versionSelectWrapper.querySelector('.version-dropdown-panel');
                    if (versionDropdown) {
                        versionDropdown.style.display = 'none';
                        versionSelectWrapper.classList.remove('active');
                    }
                }
                validateForm();
            }

            // 处理数据库模式切换
            function handleDbModeChange() {
                const dbAutoRadio = document.getElementById('dbAuto');
                const dbExistingRadio = document.getElementById('dbExisting');
                const dbCustomRadio = document.getElementById('dbCustom');
                const dbExistingSection = document.getElementById('dbExistingSection');
                const dbCustomSection = document.getElementById('dbCustomSection');
                if (dbExistingRadio.checked) {
                    dbExistingSection.style.display = 'flex';
                    dbCustomSection.style.display = 'none';
                    const existingDbSelect = document.getElementById('existingDbSelect');
                    const selectedId = existingDbSelect.getAttribute('data-selected-value');
                    initSelectOption(existingDbSelect, databaseList, selectedId);
                } else if (dbCustomRadio.checked) {
                    dbExistingSection.style.display = 'none';
                    dbCustomSection.style.display = 'flex';
                } else {
                    dbExistingSection.style.display = 'none';
                    dbCustomSection.style.display = 'none';
                }
            }
            // 初始化表单
            function initForm() {
                const dbAutoRadio = document.getElementById('dbAuto');
                const dbExistingRadio = document.getElementById('dbExisting');
                const dbCustomRadio = document.getElementById('dbCustom');
                const dbExistingSection = document.getElementById('dbExistingSection');
                const dbCustomSection = document.getElementById('dbCustomSection');
                const saveButton = document.getElementById('saveButton');
                const cancelButton = document.getElementById('cancelButton');
                const testConnectionButton = document.getElementById('testConnectionButton');



                // 添加事件监听器
                dbAutoRadio.addEventListener('change', handleDbModeChange);
                dbExistingRadio.addEventListener('change', handleDbModeChange);
                dbCustomRadio.addEventListener('change', handleDbModeChange);

                // 初始状态检查
                handleDbModeChange();

                // 初始化现有数据库下拉框
                initSelectOption(document.getElementById('existingDbSelect'), databaseList);

                // 测试连接按钮点击事件
                testConnectionButton.addEventListener('click', () => {
                    const connectionStatus = document.getElementById('connectionStatus');
                    // connectionStatus.textContent = '✓ Succeeded';
                    // connectionStatus.classList.add('success-icon');

                    // 修改此变量可演示成功或失败
                    // 实际应用中应根据后端返回结果判断
                    const isSuccess = false; // true为成功，false为失败

                    // 清空原有子节点
                    while (connectionStatus.firstChild) {
                        connectionStatus.removeChild(connectionStatus.firstChild);
                    }

                    // 创建图标和文本节点
                    const iconElement = document.createElement('span');
                    const textElement = document.createElement('span');

                    connectionStatus.style.display = 'inline';

                    if (isSuccess) {
                        iconElement.innerHTML = '<svg class="icon" viewBox="0 0 1024 1024" width="14" height="14"><path d="M512 25.6A486.4 486.4 0 0 1 998.4 512 486.4 486.4 0 0 1 512 998.4 486.4 486.4 0 1 1 512 25.6z m-69.504 626.368L288.64 498.176l-54.272 54.272L415.36 733.44a38.4 38.4 0 0 0 48.256 4.928l6.016-4.928 316.8-316.8-54.272-54.272-289.664 289.664z" fill="#2EA043"></path></svg>';
                        textElement.textContent = 'Succeeded';
                        textElement.style.color = '#2EA043';
                    } else {
                        iconElement.innerHTML = '<svg class="icon" viewBox="0 0 1024 1024" width="15" height="15"><path d="M512 25.6A486.4 486.4 0 0 1 998.4 512 486.4 486.4 0 0 1 512 998.4 486.4 486.4 0 1 1 512 25.6zM512 704a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z m-38.4-480V640h76.8V224H473.6z" fill="#F85149"></path></svg>';
                        textElement.textContent = 'Failed Failed  FailedFailedFailedFailedFailedFailed  Failed     Failed Failed Failed Failed Failed Failed Failed  FailedFailedFailedFailedFailedFailed  Failed     Failed Failed Failed Failed Failed   Failed Failed  FailedFailedFailedFailedFailedFailed  Failed     Failed Failed Failed Failed Failed  Failed Failed  FailedFailedFailedFailedFailedFailed  Failed     Failed Failed Failed Failed Failed';
                        textElement.style.color = '#F85149';
                    }

                    // 添加子节点
                    connectionStatus.appendChild(iconElement);
                    connectionStatus.appendChild(textElement);

                    setTimeout(() => {
                        connectionStatus.style.display = 'none';
                    }, 2000);
                });



                // 请求初始数据
                vscode.postMessage({ type: 'requestInitialData' });
            }

            // 保存设置
            function validateForm() {
                const dbMode = document.querySelector('input[name="databaseMode"]:checked').value;
                const saveButton = document.getElementById('saveButton');
                const existingDbSelect = document.getElementById('existingDbSelect');
                const dbUrl = document.getElementById('dbUrl');
                const dbUser = document.getElementById('dbUser');
                const dbPassword = document.getElementById('dbPassword');

                // 移除所有错误边框
                [existingDbSelect, dbUrl, dbUser, dbPassword].forEach(el => el.classList.remove('error-border'));

                if (dbMode === '2') { // Select Existing
                    const isValid = existingDbSelect.value.trim() !== '';
                    saveButton.disabled = !isValid;
                    if (!isValid) {
                        existingDbSelect.classList.add('error-border');
                    }
                } else if (dbMode === '3') { // Custom
                    const isUrlValid = dbUrl.value.trim() !== '';
                    const isUserValid = dbUser.value.trim() !== '';
                    const isPasswordValid = dbPassword.value.trim() !== '';
                    const isValid = isUrlValid && isUserValid && isPasswordValid;
                    saveButton.disabled = !isValid;
                    if (!isUrlValid) dbUrl.classList.add('error-border');
                    if (!isUserValid) dbUser.classList.add('error-border');
                    if (!isPasswordValid) dbPassword.classList.add('error-border');
                } else {
                    saveButton.disabled = false;
                }
            }

            function handleSaveSettings() {
                // 如果已经在保存中，则不重复执行
                if (isSaving) {
                    return;
                }

                // 设置保存中标志
                isSaving = true;

                // 收集表单数据
                const dbMode = document.querySelector('input[name="databaseMode"]:checked').value;
                const description = document.getElementById('description').value.trim();

                // 获取数据库配置
                let databaseConfig = undefined;
                if (dbMode === '2') { // Select Existing
                    const existingDbSelect = document.getElementById('existingDbSelect');
                    databaseConfig = {
                        id: existingDbSelect.getAttribute('data-selected-value'),
                        name: existingDbSelect.value
                    };
                } else if (dbMode === '3') { // Custom
                    databaseConfig = {
                        type: document.getElementById('dbType').value,
                        url: document.getElementById('dbUrl').value.trim(),
                        username: document.getElementById('dbUser').value.trim(),
                        password: document.getElementById('dbPassword').value.trim()
                    };
                }

                // 显示加载状态
                showLoadingState(true);

                // 发送保存设置消息
                try {
                    vscode.postMessage({
                        type: 'save',
                        data: {
                            dbMode,
                            databaseConfig,
                            description
                        }
                    });
                    console.log('Save message sent successfully');
                } catch (error) {
                    console.error('Error sending save message:', error);
                    isSaving = false;
                    showLoadingState(false);
                    return;
                }

                // 设置一个安全超时，如果后端长时间没有响应，也能重置状态
                const timeoutId = setTimeout(() => {
                    isSaving = false;
                    showLoadingState(false);
                    clearTimeout(timeoutId);
                }, 20000); // 20秒后自动重置
            }

            // 处理来自VS Code的消息
            window.addEventListener('message', event => {
                const message = event.data;

                // 确保消息是一个对象并且有type属性
                if (typeof message !== 'object' || !message || !('type' in message)) {
                    return;
                }

                switch (message.type) {
                    case 'setDatabases': event
                        if (message.databases && Array.isArray(message.databases)) {
                            databaseList = []
                            databaseList = message.databases
                            initSelectOption(document.getElementById('existingDbSelect'), databaseList);
                        }
                        break;
                    // 设置初始数据
                    case 'setInitialData':
                        if (message.data) {
                            // 填充表单字段
                            document.getElementById('serverInfo').textContent = message.data.serverInfo || '-';
                            document.getElementById('projectName').value = message.data.name || message.data.projectName;
                            document.getElementById('projectName').textContent = message.data.projectName;
                            if (message.data.devMode) {
                                const devModeStr = message.data.devMode === 1 ? 'Remote' : message.data.devMode === 2 ? 'Local' : '';
                                document.getElementById('devMode').textContent = devModeStr;
                            }
                            document.getElementById('template').textContent = message.data.projectTemplateName || '-';
                            if (message.data.databaseType === 0) {
                                document.getElementById('dbAuto').checked = true;
                            } else if (message.data.databaseType === 2 || message.data.databaseType === 1) {
                                document.getElementById('dbExisting').checked = true;
                                if (databaseList?.length) {
                                    const selected = databaseList.find(f => f.name === message.data.databaseName);
                                    if (selected) {
                                        initSelectOption(document.getElementById('existingDbSelect'), databaseList, selected.id);
                                    }
                                }
                            } else if (message.data.databaseType === 3) {
                                document.getElementById('dbCustom').checked = true;
                                document.getElementById('dbUrl').value = message.data.databaseInfo;
                                document.getElementById('dbUser').value = message.data.databaseUsername;
                                document.getElementById('dbPassword').value = message.data.databasePassword;
                            }

                            document.getElementById('domain').textContent = message.data.url || '-';
                            document.getElementById('exportPorts').textContent = message.data.databasePort;
                            document.getElementById('description').value = message.data.description;

                            // 触发数据库模式切换处理
                            handleDbModeChange();
                            // 验证表单
                            validateForm();
                        }
                        break;
                }
            });

            // 在页面加载完成后初始化
            window.addEventListener('DOMContentLoaded', () => {
                // 延迟一帧，确保样式已应用
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        // 确保加载遮罩层初始状态是隐藏的
                        const overlay = document.getElementById('loadingOverlay');
                        if (overlay) {
                            overlay.classList.remove('active');
                        }
                        existingDbSelectDom = document.getElementById('existingDbSelect')
                        // 初始化表单
                        initForm();
                        // 初始验证表单
                        validateForm();
                    });
                });
            });
            // 保存按钮点击事件
            document.getElementById('saveButton').addEventListener('click', handleSaveSettings);

            // 取消按钮点击事件
            document.getElementById('cancelButton').addEventListener('click', () => {
                vscode.postMessage({ type: 'cancel' });
            });

            // 按Enter键提交表单
            document.addEventListener('keydown', (e) => {
                // 如果加载状态已激活，则不处理键盘事件
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay && loadingOverlay.classList.contains('active')) {
                    return;
                }

                // if (e.key === 'Enter') {
                //     // 直接调用处理函数，而不是触发按钮点击
                //     handleSaveSettings();
                // }

                // ESC键取消
                if (e.key === 'Escape') {
                    document.getElementById('cancelButton').click();
                }
            });

            // 添加事件监听器以在表单变化时进行验证
            document.querySelectorAll('input[name="databaseMode"]').forEach(radio => {
                radio.addEventListener('change', validateForm);
            });
            document.getElementById('existingDbSelect').addEventListener('change', validateForm);
            document.getElementById('dbUrl').addEventListener('input', validateForm);
            document.getElementById('dbUser').addEventListener('input', validateForm);
            document.getElementById('dbPassword').addEventListener('input', validateForm);

            // 为自定义下拉菜单添加验证
            document.addEventListener('click', (e) => {
                if (e.target.closest('.version-dropdown-item')) {
                    validateForm();
                }
            });
        } catch (error) {
            console.error('Error initializing project settings:', error);
        }
    </script>
</body>

</html>
