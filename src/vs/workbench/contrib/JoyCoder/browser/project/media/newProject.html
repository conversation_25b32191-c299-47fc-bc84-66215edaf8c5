<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Project</title>
    <style>
        .icon {
            width: 1em;
            height: 1em;
            vertical-align: -0.15em;
            fill: currentColor;
            overflow: hidden;
        }

        body {
            font-family: PingFang SC, -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            margin: 0;
            padding: 0;
            height: 100%;
            overflow: hidden;

        }

        .new-project-container {
            width: 100%;
            height: 100vh;
            min-height: 500px;
            /* max-width: 560px; */
            min-width: 500px;
            margin: 0 auto;
            animation: fadeIn 0.3s ease-in-out;
            box-sizing: border-box;
            /* overflow-y: auto; */
            font-size: 12px;
            font-family: PingFang SC;
            font-weight: normal;
            color: var(--vscode-editor-foreground);
        }

        .new-project-form {
            padding: 20px 40px;
            max-height: 80vh;
            padding-bottom: 300px;
            overflow-y: auto;
        }

        .new-project-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: var(--vscode-editor-foreground);
            text-align: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--vscode-input-border);
        }

        .form-group {
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            width: 100%;
        }

        .form-group>label {
            width: 80px;
            color: var(--vscode-input-foreground);
            font-size: 12px;
            font-weight: normal;
            line-height: 28px;
            margin-right: 8px;
            /*padding-top: 4px;*/
            flex-shrink: 0;
        }

        .description {
            align-items: flex-start;
        }

        input[type="text"],
        input[type="password"],
        textarea {
            width: 100%;
            padding: 4px 6px;
            border: 1px solid var(--vscode-input-border, #c8c8c8);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-settings-textInputForeground);
            border-radius: 2px;
            box-sizing: border-box;
            font-size: 12px;
            font-weight: normal;
        }

        input[type="text"]::placeholder,
        textarea::placeholder {
            color: var(--vscode-input-label-placeholder);
            opacity: 0.6;
        }

        textarea {
            /*min-height: 80px;*/
            resize: vertical;
            line-height: 18px;
        }

        .radio-group {
            display: flex;
            gap: 30px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            height: 24px;
            margin-right: 40px;
        }

        .radio-option input[type="radio"] {
            margin: 0 5px 0 0;
            vertical-align: middle;
            position: relative;

            appearance: none;
            width: 12px;
            height: 12px;
            border: 1.5px solid var(--vscode-input-border, #c8c8c8);
            background: var(--vscode-editorWidget-background, #fff);
            border-radius: 50%;
            /*margin: 0;*/
            cursor: pointer;
        }

        .radio-option input[type="radio"]:checked {
            background: var(--vscode-editorWidget-background, #fff);
            border: 1.5px solid var(--vscode-input-border, #c8c8c8);
            position: relative;
        }

        .radio-option input[type="radio"]:checked::after {
            content: '';
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--vscode-button-background, #2979ff);
            /* 主题主色点 */
            border-radius: 50%;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .radio-option input[type="radio"]:focus {
            outline: 1.5px solid var(--vscode-focusBorder, #0078d4);
        }

        .radio-option label {
            display: inline-block;
            vertical-align: middle;
            line-height: 24px;
            font-size: 12px;
            padding-top: 0;
        }

        /* 自定义下拉框样式 */
        .custom-select {
            position: relative;
            width: 100%;
            /* border: 1px solid var(--vscode-input-border, #c8c8c8); */
        }

        .icon.icon-chaxun {
            position: absolute;
            top: 6px;
            left: 12px;
            fill: var(--vscode-icon-foreground, var(--vscode-input-foreground, #666));
            color: var(--vscode-icon-foreground, var(--vscode-input-foreground, #666));
            font-size: 16px;
        }

        .template-input {
            width: 100%;
            padding: 8px;
            padding-right: 30px;
            /* border: 1px solid var(--vscode-input-border, #c8c8c8); */
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 3px;
            box-sizing: border-box;
            background-color: yellow;
        }

        .icon.icon-chaxun+.template-input {
            padding-left: 32px !important;
        }

        .template-input:focus {
            outline: 1px solid var(--vscode-focusBorder);
            outline-offset: -1px;
        }

        .dropdown-icon {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--vscode-input-foreground);
            font-size: 10px;
            pointer-events: none;
            transition: transform 0.2s ease;
        }

        /* 当下拉框激活时，箭头向下 */
        .custom-select.active .dropdown-icon {
            transform: translateY(-50%) rotate(180deg);
        }

        .dropdown-panel {
            position: absolute;
            top: 100%;
            right: 0;
            left: 0;
            /* width: 100%; */
            max-height: 377px;
            padding: 8px;
            /* overflow-x: hidden; */
            overflow-y: auto;
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 2px;
            /* box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 1); */
            margin-top: 0px;
            z-index: 100;
            display: none;
        }

        .dropdown-category {
            padding: 0 8px;
            /*background-color: #2d2d2d;*/
            font-size: 13px;
            font-weight: 600;
            color: var(--vscode-input-label);
            line-height: 32px;
            /*border-bottom: 1px solid #333;*/
            /*color: var(--vscode-input-foreground);*/
        }

        .dropdown-item {
            padding: 8px;
            /*border-bottom: 1px solid #333;*/
            cursor: pointer;
            outline: none;
            display: flex;
            align-items: center;
        }

        .dropdown-item:hover,
        .dropdown-item:hover .template-version-select {
            background-color: var(--vscode-list-hoverBackground);
        }

        .dropdown-item.selected {
            background-color: var(--vscode-list-hoverBackground);
            /*color: var(--vscode-list-activeSelectionForeground);*/
        }

        .dropdown-item-content {
            display: grid;
            grid-template-columns: auto auto;
            width: 100%;
        }

        .template-item-img {
            width: 40px;
            height: 40px;
            margin-right: 8px;
            border-radius: 4px;
            object-fit: contain;
            flex-shrink: 0;
        }

        /* 语言类模版下拉选择框样式 */
        .template-version-select {
            height: 20px;
            min-width: 110px;
            max-width: 150px;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editorWidget-foreground, #333);
            border: 1px solid var(--vscode-input-border);
            /*box-shadow: 0px 0px 0px 1px;*/
            border-radius: 2px;
            font-size: 12px;
            font-weight: normal;
            /*margin-left: 18px;*/
            padding: 0 8px;
            box-sizing: border-box;
            /*appearance: none;*/
            /* outline: none; */
            transition: border-color 0.2s;
            vertical-align: middle;
        }

        .template-version-select:focus {
            border-color: var(--vscode-focusBorder);
        }

        .dropdown-item.selected .template-version-select {
            background-color: var(--vscode-list-hoverBackground);
            /*color: var(--vscode-list-activeSelectionForeground, #fff);*/
        }

        /* 统一下拉箭头样式 */
        .template-version-select::-ms-expand {
            display: none;
        }

        .template-version-select option {
            background: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
        }

        .dropdown-item-left-title {
            /* display: flex;
            flex-direction: column; */
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .dropdown-item-title {
            font-size: 13px;
            font-weight: 600;
            color: var(--vscode-input-label);
            line-height: 20px;
        }

        .dropdown-item-description {
            font-size: 12px;
            font-weight: normal;
            color: var(--vscode-descriptionForeground);
            line-height: 18px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: block;
        }

        /* 默认隐藏下拉面板 */
        .dropdown-panel {
            display: none;
        }

        /* 当自定义选择框处于活动状态时显示下拉面板 */
        .custom-select.active>.dropdown-panel {
            display: block !important;
        }

        .version-custom-select.active>.version-dropdown-panel {
            display: block !important;
        }


        button {
            padding: 5px 12px;
            border: none;
            cursor: pointer;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: var(--vscode-input-label);
            line-height: 18px;
        }

        button.primary {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
        }

        button.primary:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            white-space: nowrap;
        }

        button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);

        }

        .new-project-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--vscode-editor-background);
            border-top: 1px solid #333;
            padding: 12px 16px;
            box-sizing: border-box;
            overflow: hidden;
            width: 100%;
            z-index: 10;
            text-align: right;
        }

        .secondary {
            margin-right: 10px;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--vscode-editor-background);
            z-index: 9999;
            transition: opacity 0.3s ease-out;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            pointer-events: none;
        }

        .overlay.active {
            opacity: 0.8;
            pointer-events: all;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            border-top-color: var(--vscode-button-background);
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }

        .loading-text {
            color: var(--vscode-editor-foreground);
            font-size: 16px;
            font-weight: 500;
        }

        .magic-wand-icon {
            color: #9e9e9e;
            font-size: 16px;
            cursor: pointer;
        }



        /* Location 输入框与按钮组合美化 */
        #locationGroup input#projectLocation {
            border-radius: 3px 0 0 3px;
            border-right: none;
            border: 1px solid var(--vscode-input-border, #c8c8c8);
            height: 32px;
            padding: 0 12px;
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            box-sizing: border-box;
            font-size: 1em;
            flex-grow: 1;
            transition: border-color 0.2s;
        }

        #locationGroup input#projectLocation:focus {
            outline: 1px solid var(--vscode-focusBorder);
            z-index: 1;
        }

        #locationGroup button#browseButton {
            border-radius: 0 3px 3px 0;
            border: 1px solid var(--vscode-input-border, #c8c8c8);
            border-left: none;
            background: transparent;
            height: 32px;
            width: 36px;
            min-width: 36px;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: var(--vscode-input-foreground);
            box-sizing: border-box;
            transition: background 0.2s, border-color 0.2s;
        }

        #locationGroup button#browseButton:hover {
            background: var(--vscode-list-hoverBackground);
        }

        #locationGroup {
            margin-top: 8px;
        }

        #locationGroup>label {
            margin-bottom: 0;
        }

        #locationGroup>div {
            width: 100%;
            display: flex;
            align-items: center;
        }

        #locationGroup span {
            font-size: 16px;
            pointer-events: none;
            user-select: none;
        }

        #dbType,
        .version-custom-select input.select-input {
            width: 100%;
            height: 28px;
            padding: 5px 12px;
            background: var(--vscode-editor-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border, #c8c8c8);
            border-radius: 4px;
            font-size: 12px;
            font-weight: normal;
        }

        .custom-database {
            margin-bottom: 16px;
            margin-left: 90px;
        }

        .custom-database .form-group {
            margin-bottom: 8px;
        }

        .db-user-control {
            width: 100%;
            display: flex;
            gap: 20px;
            justify-items: center;
        }

        .db-label {
            line-height: 26px;
            width: 80px;
        }

        .db-password-label {
            width: 68px;
            /*text-align: right;*/
            /*margin-left: 10px;*/
        }

        #templateCardContainer {
            width: 100%;
            display: flex;
            justify-content: left;
            margin-top: 8px;
        }

        .template-card {
            position: relative;
            min-width: 240px;
            max-width: 360px;
            padding: 12px;
            background: var(--vscode-editorWidget-background, #f3f3f3);
            border: 1px solid var(--vscode-focusBorder, #c8c8c8);
            box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
            border-radius: 6px;
            /*box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);*/

            display: flex;
            flex-direction: column;
            align-items: center;
            /* margin: 0 auto; */
            transition: box-shadow 0.2s;
        }

        .template-card-close {
            position: absolute;
            top: 12px;
            right: 12px;
            font-size: 13px;
            color: var(--vscode-descriptionForeground);
            cursor: pointer;
            z-index: 2;
            background: transparent;
            border: 1px solid var(--vscode-descriptionForeground);
            border-radius: 50%;
            outline: none;
            padding: 0;
            width: 15px;
            height: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.15s;
        }

        .template-card-close:hover {
            color: var(--vscode-errorForeground, #ff5252);
            border-color: var(--vscode-errorForeground, #ff5252);
        }

        .template-card-logo {
            font-size: 32px;
            margin-bottom: 4px;
            /*margin-top: 2px;*/
            color: var(--vscode-button-background);
        }

        .template-card-title {
            font-size: 13px;
            font-weight: 600;
            color: var(--vscode-editorWidget-foreground, #333);
            /*margin-bottom: 6px;*/
            text-align: center;
            line-height: 20px;
        }

        .template-card-desc {
            font-size: 12px;
            color: var(--vscode-descriptionForeground, #888);
            font-weight: normal;
            /*margin-bottom: 8px;*/
            text-align: center;
            line-height: 18px;
        }

        .template-card-details {
            font-size: 1.1em;
            font-weight: 500;
            color: var(--vscode-editorWidget-foreground, #333);
            text-align: center;
            margin-top: 8px;
            padding: 4px 10px;
            border-radius: 4px;
            background-color: rgba(0, 0, 0, 0.06);
            display: inline-block;
        }

        .template-version-info {
            /*margin-bottom: -20px;*/
            font-size: 12px;
            font-weight: normal;
            color: var(--vscode-descriptionForeground);
            line-height: 18px;
        }

        .template-version-select-label {
            white-space: nowrap;
            font-size: 12px;
            font-weight: normal;
            color: var(--vscode-editorWidget-foreground);
        }

        .template-version-label {
            color: var(--vscode-descriptionForeground);
            font-size: 12px;
            font-weight: normal;
        }

        .dropdown-item+.dropdown-category {
            border-top: 1px solid var(--vscode-input-border);
        }

        /*	数据库测试 */
        #testDbStatus i {
            display: inline-block;
            width: 15px;
            height: 15px;
        }

        #testDbStatus span {
            display: inline-block;
            font-size: 12px;
            line-height: 18px;
        }

        /*	获取焦点样式 */
        input:focus,
        select:focus,
        textarea:focus {
            outline: 1px solid var(--vscode-focusBorder);
            /*outline-offset: -1px;*/
        }

        /*	select */
        .select-wrapper {
            position: relative;
            width: 100%;
        }

        .select-wrapper select {
            appearance: none;
            padding-right: 28px;
        }

        .dropdown-empty {
            padding: 32px 0;
            text-align: center;
            color: var(--vscode-descriptionForeground);
            font-size: 14px;
            user-select: none;
        }

        /* 在 style 标签最后添加 */
        .version-custom-select {
            min-width: 120px;
            max-width: 150px;
            width: 120px !important;
            display: inline-block;
            background-color: var(--vscode-settings-dropdownBackground);
            color: var(--vscode-settings-dropdownForeground);
            border-color: var(--vscode-settings-dropdownBorder);
            position: relative;
        }

        .custom-select+.version-custom-select {
            border: 1px solid var(--vscode-input-border);
        }

        .version-custom-select .template-version-input {
            cursor: pointer;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-settings-dropdownBackground);
            color: var(--vscode-settings-dropdownForeground);
            border-color: var(--vscode-settings-dropdownBorder);
            border-radius: 2px;
            font-size: 12px;
            padding: 2 6px;
            height: 20px;
            min-width: 110px;
            max-width: 150px;
            box-sizing: border-box;
        }

        .version-dropdown-panel {
            position: absolute;
            left: 0;
            right: 0;
            top: 100%;
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-input-border, #c8c8c8);
            border-radius: 2px;
            box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 1);
            z-index: 99;
            max-height: 200px;
            overflow-y: auto;
            display: none;
            text-align: left;
            padding: 0;
        }

        .select-container {
            position: relative;
            border-color: var(--vscode-settings-dropdownBorder);
        }

        .select-container::after {
            content: var(--vscode-icon-chevron-down-content);
            font-family: var(--vscode-icon-chevron-down-font-family);
            font-size: 12px;
            width: 16px;
            height: 16px;
            line-height: 16px;
            position: absolute;
            top: 0;
            bottom: 0;
            right: 6px;
            margin: auto;
            pointer-events: none;
            z-index: 100;
        }

        .version-custom-select.active .version-dropdown-panel {
            display: block !important;
        }

        .version-dropdown-item {
            padding-top: var(--dropdown-padding-top);
            padding-bottom: var(--dropdown-padding-bottom);
            padding-left: 1px;
            padding-right: 1px;
            height: 26px;
            line-height: 26px;
            padding: 0px 6px;
            cursor: pointer;
            outline: none;
            display: block;
            color: var(--vscode-dropdown-foreground);
            background: transparent;
            transition: background 0.2s;
            color: var(--vscode-input-foreground);
        }

        .version-dropdown-item:hover,
        .version-dropdown-item.selected {
            background: var(--vscode-list-hoverBackground);
            color: var(--vscode-dropdown-foreground);
        }

        /* 样式部分 */
        .version-custom-select {
            position: relative;
        }

        .version-dropdown-icon {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            pointer-events: none;
            width: 16px;
            height: 16px;
            /* font-family: var(--vscode-icon-chevron-down-font-family); */
            font-size: 16px;
            line-height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #testDbStatus {
            display: inline-flex !important;
            align-items: flex-start;
            gap: 4px;
            vertical-align: top;
            margin-left: 8px;
            font-size: 14px;
            line-height: 1.5;
            max-width: 70%;
            word-break: break-all;
        }

        #testDbConnection {
            vertical-align: top;
            margin-right: 0;
        }

        .custom-database .form-group>div {
            display: flex;
            align-items: flex-start !important;
            gap: 14px;
        }
    </style>
</head>

<body>
    <div class="overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">Creating Project...</div>
    </div>
    <div class="new-project-container">
        <div class="new-project-form">
            <div class="form-group">
                <label for="projectName">Name:</label>
                <div style="width: 100%;">
                    <input type="text" id="projectName" value="" placeholder="" autocomplete="">
                    <div id="projectExistsWarning"
                        style="color: #ff5252; font-size: 0.9em; margin-top: 4px; display: none;">Project already exists
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="devMode">Dev Mode:</label>
                <div style="width: 100%;">
                    <div class="radio-group">
                        <div class="radio-option">
                            <input type="radio" id="remoteMode" name="devMode" checked>
                            <label for="remoteMode">Remote</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="localMode" name="devMode">
                            <label for="localMode">Local</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location 字段，默认隐藏，只在选择 Local 模式时显示 -->
            <div class="form-group" id="locationGroup" style="display: none;">
                <label for="projectLocation">Location:</label>
                <div style="width: 100%; display: flex; align-items: center;">
                    <input type="text" id="projectLocation" value="" style="flex-grow: 1;">
                    <button id="browseButton">
                        <span style="font-size: 16px;">📂</span>
                    </button>
                </div>
            </div>

            <div class="form-group">
                <label for="templateInput">Templates:</label>
                <div class="template-container" style="width: 100%; position: relative;">
                    <div class="custom-select">
                        <svg t="1747883548378" class="icon icon-chaxun" viewBox="0 0 1024 1024" version="1.1"
                            xmlns="http://www.w3.org/2000/svg" p-id="3596" width="16" height="16">
                            <path
                                d="M459.264 89.6a358.4 358.4 0 1 0 206.528 651.136l214.336 214.4 54.272-54.272-210.88-210.944A358.4 358.4 0 0 0 459.264 89.6z m0 76.8a281.6 281.6 0 1 1 0 563.264 281.6 281.6 0 0 1 0-563.264z"
                                fill="var(--vscode-input-label-placeholder)" fill-opacity="0.2" p-id="3597"></path>
                        </svg>
                        <input type="text" id="templateInput" class="template-input" placeholder="Search templates..."
                            autocomplete="off">
                        <div class="dropdown-panel"></div>
                    </div>
                    <!-- 模板卡片展示区 -->
                    <div id="templateCardContainer"></div>
                </div>
            </div>

            <!-- Database -->
            <div class="form-group">
                <label for="databaseMode">Database:</label>
                <div style="width: 100%;">
                    <div class="radio-group" id="databaseRadioGroup">
                        <div class="radio-option">
                            <input type="radio" id="dbAuto" name="databaseMode" value="auto" checked>
                            <label for="dbAuto">Automatically Create</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="dbExisting" name="databaseMode" value="existing">
                            <label for="dbExisting">Select Existing</label>
                        </div>
                        <div class="radio-option">
                            <input type="radio" id="dbCustom" name="databaseMode" value="custom">
                            <label for="dbCustom">Custom</label>
                        </div>
                    </div>
                    <!-- Select Existing 下拉框，默认隐藏 -->
                    <div id="existingDbSelectWrapper" style="display:none; margin-top:8px;">
                        <div class="select-wrapper">
                            <input id="existingDbSelect" type="text"
                                class="template-version-input template-input select-input" placeholder="name host:port"
                                readOnly />
                            <span class="version-dropdown-icon">
                                <svg class="version-dropdown-svg" width="20" height="12" viewBox="0 0 20 12"
                                    focusable="false" aria-hidden="true">
                                    <polyline points="4,3 10,9 16,3" fill="none" stroke="currentColor" stroke-width="1"
                                        stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Custom 数据库配置表单，仅在Custom模式下显示 -->
            <div id="customDbConfig" class="custom-database" style="display: none;">
                <div class="form-group">
                    <label for="dbType" class="db-label">Type:</label>
                    <div class="select-wrapper" style="width: 100%;">
                        <!-- <select id="dbType">
                            <option value="mysql">MySQL</option>
                        </select> -->
                        <input id="dbType" type="text" class="template-version-input template-input select-input"
                            readOnly value="mysql" />
                        <span class="version-dropdown-icon">
                            <svg class="version-dropdown-svg" width="20" height="12" viewBox="0 0 20 12"
                                focusable="false" aria-hidden="true">
                                <polyline points="4,3 10,9 16,3" fill="none" stroke="currentColor" stroke-width="1"
                                    stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="dbUrl" class="db-label">URL:</label>
                    <div style="width: 100%;">
                        <input type="text" id="dbUrl" value="" placeholder="mysql-xxx.com:prot:my_test">
                    </div>
                </div>
                <div class="form-group">
                    <label for="dbUser" class="db-label">User Name:</label>
                    <div class="db-user-control">
                        <input type="text" id="dbUser" value="" style="flex: 1;" placeholder="user name">
                        <label for="dbPassword" class="db-password-label db-label">Password
                            :</label>
                        <input type="password" id="dbPassword" value="" style="flex: 1;">
                    </div>
                </div>
                <!-- <div class="form-group">
                    <div style="width: 100%; display: flex; align-items: center; gap: 14px;">
                        <button id="testDbConnection" type="button" class="secondary"
                            style="color: var(--vscode-button-background); background: none; border: none; padding: 0; font-size: 1em;">Test
                            Connection</button>
                        <span id="testDbStatus" style="color: var(--vscode-button-background); font-size: 1em; display: none;">✔
                            Succeeded</span>
                    </div>
                </div> -->
            </div>
            <!-- Description -->
            <div class="form-group description">
                <label for="projectDescription">Description:</label>
                <div style="width: 100%; position: relative;">
                    <textarea id="projectDescription" rows="2" placeholder=""></textarea>
                    <!--                    <span class="magic-wand-icon" style="position: absolute; right: 10px; bottom: 10px;">✨</span>-->
                </div>
            </div>
            <!-- 错误信息展示区 -->
            <div class="form-group">
                <label id="createErrorMsg"
                    style="color: var(--vscode-errorForeground, #ff5252); font-size: 13px; margin: 4px 0 0 0; display: none;width:100%;"></label>
            </div>
        </div>
        <div class="new-project-buttons">
            <button class="secondary" id="cancelButton">Cancel</button>
            <button class="primary" id="createButton">Create</button>
        </div>
    </div>

    <!-- 调试日志区域 -->
    <!-- <div id="debugLogArea" style="height:180px; background:var(--vscode-editor-background); color:var(--vscode-editor-foreground); font-size:13px; font-family:monospace; border-radius:4px; padding:8px; overflow-y:auto; white-space:pre-wrap; border:1px solid var(--vscode-input-border); margin:16px 0 0 0;"></div> -->
    <script>
        // 全局调试日志API
        // const debugLog = function(msg) {
        //     const area = document.getElementById('debugLogArea');
        //     if (area) {
        //         const now = new Date();
        //         const time = now.toLocaleTimeString();
        //         area.innerText += `[${time}] ${msg}\n`;
        //         area.scrollTop = area.scrollHeight;
        //     }
        // };
        // window.clearDebugLog = function() {
        //     const area = document.getElementById('debugLogArea');
        //     if (area) area.innerText = '';
        // };
        try {
            // 等待VS Code API可用
            const vscode = acquireVsCodeApi();
            // 存储从TypeScript接收到的模板数据
            let templatesData = [];

            // 存储getSelectedTemplate函数的引用
            let getSelectedTemplate;

            // 全局变量，用于跟踪项目是否已存在
            let projectExists = false;
            let validateFormFunc = null;

            // 防抖标志，用于跟踪创建按钮是否在冷却期
            let isCreatingProject = false;

            let dropdownItems = [];
            const customSelect = document.querySelector('.custom-select');
            const templateInput = document.getElementById('templateInput');
            const dropdownPanel = document.querySelector('.dropdown-panel');

            // 这些变量只在函数内部使用
            var selectedValue = '';
            var selectedText = '';
            // 已有数据库实例列表
            let existingDbList = [];

            // 下拉选择
            // 生成select下拉
            function initSelectOption(id, data = []) {
                const versionSelectInput = document.getElementById(id);
                // 找到id的父节点
                const versionSelectWrapper = versionSelectInput && versionSelectInput.parentNode;
                if (data.length === 0) {
                    data = [
                        {
                            value: '',
                            id: '',
                            label: 'No data available.'
                        }
                    ]
                }
                let list = data;
                if (versionSelectWrapper) {
                    const versionDropdown = document.createElement('div');
                    versionDropdown.className = 'dropdown-panel version-dropdown-panel';
                    versionDropdown.style.zIndex = '200';

                    list.forEach((ver, idx) => {
                        ver.value = ver.id;
                        ver.label = ver.id ? `${ver.name} ${ver.internalEndpoint}:${ver.port}` : ver.label;
                        const opt = document.createElement('div');
                        opt.className = 'version-dropdown-item';
                        opt.textContent = ver.label;
                        opt.setAttribute('data-value', ver.value);
                        if (idx === 0) opt.classList.add('selected');
                        versionDropdown.appendChild(opt);

                        opt.addEventListener('click', (e) => {
                            e.stopPropagation();
                            // 设置input
                            versionSelectInput.value = ver.label;
                            versionSelectInput.setAttribute('data-selected-value', ver.value);
                            // 高亮
                            Array.from(versionDropdown.children).forEach((child) => child.classList.remove('selected'));
                            opt.classList.add('selected');
                            versionDropdown.style.display = 'none';
                            versionSelectWrapper.classList.remove('active');

                            versionSelectInput.style.borderColor = ''
                            const createButton = document.getElementById('createButton');

                            createButton.disabled = false;
                            createButton.style.opacity = '1';
                            createButton.style.cursor = 'pointer';
                        });
                    });

                    versionSelectInput.addEventListener('click', (e) => {
                        e.stopPropagation();
                        // 只显示当前下拉
                        document.querySelectorAll('.version-dropdown-panel').forEach((panel) => {
                            if (panel !== versionDropdown) panel.style.display = 'none';
                        });
                        if (versionDropdown.style.display === 'block') {
                            versionDropdown.style.display = 'none';
                            versionSelectWrapper.classList.remove('active');
                            return false;
                        }
                        versionDropdown.style.display = 'block';
                        versionSelectWrapper.classList.add('active');
                        // 只绑定一次全局点击关闭事件
                        if (!versionSelectWrapper._outsideClickListener) {
                            versionSelectWrapper._outsideClickListener = (event) => {
                                if (!versionSelectWrapper.contains(event.target)) {
                                    versionDropdown.style.display = 'none';
                                    versionSelectWrapper.classList.remove('active');
                                    document.removeEventListener('click', versionSelectWrapper._outsideClickListener, true);
                                    versionSelectWrapper._outsideClickListener = null;
                                }
                            };
                            document.addEventListener('click', versionSelectWrapper._outsideClickListener, true);
                        }
                    });

                    // 点击外部关闭
                    document.addEventListener('click', (e) => {
                        if (!versionSelectWrapper.contains(e.target)) {
                            versionDropdown.style.display = 'none';
                            versionSelectWrapper.classList.remove('active');
                        }
                    });

                    // 将新选项添加到父节点中
                    versionSelectWrapper.appendChild(versionDropdown);
                    // // 默认选中第一个
                    // versionInput.value = list[0].value; // 默认第一个
                    return versionSelectInput;
                }
                return undefined;
            }

            // 将从TypeScript接收到的模板数据格式化为UI需要的格式
            function formatTemplatesData(templates) {
                // 如果没有数据，返回空数组
                if (!templates || templates.length === 0) {
                    return [];
                }

                // 按模板类别分组
                const typeGroups = {};

                templates.forEach(template => {
                    // 使用templateClassName作为分组依据，如果没有则使用"未分类模板"
                    const type = template.templateClassNameEn || '未分类模板';
                    if (!typeGroups[type]) {
                        typeGroups[type] = [];
                    }
                    typeGroups[type].push(template);
                });

                // 转换为UI需要的格式
                return Object.keys(typeGroups).map(type => {
                    return {
                        category: type,
                        items: typeGroups[type].map(template => {
                            // 判断模板类型（语言类或工程类）
                            const isLanguageTemplate = template.templateClassCode === 'language' || type.includes('Language') || type.includes('语言');

                            // 处理版本信息
                            let versions = [];
                            let componentVersions = []; // 存储组件版本信息

                            if (template.versionConfigList && Array.isArray(template.versionConfigList)) {
                                // 提取版本信息
                                template.versionConfigList.forEach(config => {
                                    // 保存组件名称和版本
                                    const componentName = config.componentName || '';
                                    const versionName = config.versionName || '';

                                    if (componentName && versionName) {
                                        componentVersions.push({
                                            componentName: componentName,
                                            versionName: versionName
                                        });
                                    }

                                    if (config.versionList && Array.isArray(config.versionList)) {
                                        config.versionList.forEach(version => {
                                            if (version.versionName) {
                                                versions.push({
                                                    value: version.versionName,
                                                    label: isLanguageTemplate ? `v${version.versionName}` : version.versionName
                                                });
                                            }
                                        });
                                    }
                                });
                            }

                            return {
                                value: template.id,
                                title: template.templateNameEn || template.templateName || template.id,
                                downloadUrl: template.templateAddr || '',
                                description: template.templateDescEn || template.templateDesc || '',
                                versions: versions.length > 0 ? versions : undefined,
                                imgUrl: template.imgUrl || '',
                                useDatabase: template.useDatabase || false,
                                isLanguageTemplate: isLanguageTemplate,
                                componentVersions: componentVersions.length > 0 ? componentVersions : undefined,
                                prompt: template.prompt,
                                templateClassCode: template.templateClassCode, // engineer 工程类  language
                            };
                        })
                    };
                });
            }

            // 初始化模板选择功能（动态加载数据）
            // 绑定事件
            function bindDropdownEvents() {
                customSelect = document.querySelector('.custom-select');
                templateInput = document.getElementById('templateInput');
                dropdownPanel = document.querySelector('.dropdown-panel');
                // 输入框聚焦时显示下拉面板
                templateInput.addEventListener('focus', () => {
                    customSelect.classList.add('active');

                    // 确保下拉面板可见
                    if (dropdownPanel) {
                        dropdownPanel.style.display = 'block';
                    }

                    // 如果没有数据，尝试重新获取
                    if (dropdownItems.length === 0) {
                        const data = formatTemplatesData(templatesData);
                        renderTemplates(data);
                    }
                });
                templateInput.addEventListener('click', (event) => {
                    event.stopPropagation();
                    customSelect.classList.add('active');

                    // 确保下拉面板可见
                    if (dropdownPanel) {
                        dropdownPanel.style.display = 'block';
                    }
                });
                templateInput.addEventListener('blur', (event) => {
                    const timer = setTimeout(() => {
                        const activeElement = document.activeElement;
                        if (!dropdownPanel.contains(activeElement) && activeElement !== templateInput) {
                            customSelect.classList.remove('active');

                            // 确保下拉面板隐藏
                            if (dropdownPanel) {
                                dropdownPanel.style.display = 'none';
                            }
                        }
                        clearTimeout(timer);
                    }, 100);
                });

                // 键盘导航
                templateInput.addEventListener('keydown', (event) => {
                    if (customSelect.classList.contains('active')) {
                        const visibleItems = dropdownItems.filter(
                            item => item.style.display !== 'none'
                        );
                        if (visibleItems.length === 0) return;
                        const currentIndex = visibleItems.findIndex(item => item.classList.contains('selected'));
                        switch (event.key) {
                            case 'ArrowDown':
                                event.preventDefault();
                                navigateDropdown(visibleItems, currentIndex, 1);
                                break;
                            case 'ArrowUp':
                                event.preventDefault();
                                navigateDropdown(visibleItems, currentIndex, -1);
                                break;
                            case 'Enter':
                                event.preventDefault();
                                if (currentIndex >= 0) {
                                    // 触发点击事件
                                    visibleItems[currentIndex].click();
                                } else {
                                    // 如果没有选中项，也隐藏下拉面板
                                    customSelect.classList.remove('active');
                                    if (dropdownPanel) {
                                        dropdownPanel.style.display = 'none';
                                    }
                                }
                                break;
                            case 'Escape':
                                event.preventDefault();

                                // 移除active类
                                customSelect.classList.remove('active');

                                // 明确隐藏下拉面板
                                if (dropdownPanel) {
                                    dropdownPanel.style.display = 'none';
                                }
                                break;
                        }
                    }
                });
            }

            // 键盘导航
            function navigateDropdown(items, currentIndex, direction) {
                items.forEach(item => item.classList.remove('selected'));
                let newIndex;
                if (currentIndex === -1) {
                    newIndex = direction > 0 ? 0 : items.length - 1;
                } else {
                    newIndex = (currentIndex + direction + items.length) % items.length;
                }
                const selectedItem = items[newIndex];
                selectedItem.classList.add('selected');
                selectedItem.scrollIntoView({ block: 'nearest' });
            }


            // 初始化模版
            function initTemplateSelection() {
                // 加载并渲染模板
                const data = formatTemplatesData(templatesData);
                renderTemplates(data);
                // 返回获取选中模板的函数
                return function getSelectedTemplate() {
                    // 获取当前选中模板的版本（自定义下拉）
                    let selectedVersion = '';
                    if (selectedValue) {
                        const selectedItem = dropdownPanel.querySelector(`.dropdown-item[data-value="${selectedValue}"]`);
                        if (selectedItem) {
                            const versionInput = selectedItem.querySelector && selectedItem.querySelector('.template-version-input');
                            if (versionInput) {
                                selectedVersion = versionInput.getAttribute('data-selected-value') || '';
                            }
                        }
                    }
                    return {
                        value: selectedValue,
                        text: selectedText,
                        version: selectedVersion
                    };
                };
            }

            // 初始化表单验证
            function initFormValidation() {
                const projectNameInput = document.getElementById('projectName');
                const projectLocationInput = document.getElementById('projectLocation');
                const projectExistsWarning = document.getElementById('projectExistsWarning');
                const remoteMode = document.getElementById('remoteMode');
                const localMode = document.getElementById('localMode');
                const locationGroup = document.getElementById('locationGroup');
                const browseButton = document.getElementById('browseButton');
                const createButton = document.getElementById('createButton');
                const dbAutoRadio = document.getElementById('dbAuto');
                const dbExistingRadio = document.getElementById('dbExisting');
                const dbCustomRadio = document.getElementById('dbCustom');
                const dbType = document.getElementById('dbType');
                const dbUrl = document.getElementById('dbUrl');
                const dbUser = document.getElementById('dbUser');
                const dbPassword = document.getElementById('dbPassword');
                const existingDbSelectWrapper = document.getElementById('existingDbSelectWrapper');
                const existingDbSelect = document.getElementById('existingDbSelect');

                // 检查项目是否已存在
                function checkProjectExists() {
                    const projectName = projectNameInput.value.trim();
                    const projectLocation = projectLocationInput.value.trim();
                    const isLocalMode = localMode.checked;

                    // 只在本地模式下且有位置和名称时检查
                    if (isLocalMode && projectLocation && projectName) {
                        // 向VS Code发送消息，检查目录是否存在
                        vscode.postMessage({
                            type: 'checkProjectExists',
                            path: `${projectLocation}/${projectName}`
                        });
                    } else {
                        // 如果不是本地模式或缺少必要信息，隐藏警告
                        projectExistsWarning.style.display = 'none';
                    }
                }

                // 验证表单并更新创建按钮状态
                function validateForm() {
                    const projectName = projectNameInput.value.trim();
                    const projectLocation = projectLocationInput.value.trim();
                    const isLocalMode = localMode.checked;

                    let isValid = projectName && (!isLocalMode || (isLocalMode && projectLocation));
                    // Database-Custom校验
                    if (dbCustomRadio && dbCustomRadio.checked) {
                        const typeValue = dbType ? dbType.value.trim() : '';
                        const urlValue = dbUrl ? dbUrl.value.trim() : '';
                        const userValue = dbUser ? dbUser.value.trim() : '';
                        const pwdValue = dbPassword ? dbPassword.value.trim() : '';
                        const customValid = typeValue && urlValue && userValue && pwdValue;
                        isValid = isValid && customValid;

                        // 高亮必填项
                        dbType.style.borderColor = typeValue ? '' : 'var(--vscode-errorForeground)';
                        dbUrl.style.borderColor = urlValue ? '' : 'var(--vscode-errorForeground)';
                        dbUser.style.borderColor = userValue ? '' : 'var(--vscode-errorForeground)';
                        dbPassword.style.borderColor = pwdValue ? '' : 'var(--vscode-errorForeground)';
                    } else {
                        // 还原样式
                        dbType.style.borderColor = '';
                        dbUrl.style.borderColor = '';
                        dbUser.style.borderColor = '';
                        dbPassword.style.borderColor = '';
                    }

                    // Database-Existing校验
                    if (dbExistingRadio && dbExistingRadio.checked) {
                        const selectedDb = existingDbSelect ? existingDbSelect.value.trim() : '';
                        isValid = isValid && !!selectedDb;
                        // 高亮必填项
                        existingDbSelect.style.borderColor = selectedDb ? '' : 'var(--vscode-errorForeground)';
                    } else if (existingDbSelect) {
                        existingDbSelect.style.borderColor = '';
                    }

                    // 更新创建按钮状态 - 如果项目已存在则禁用按钮
                    const finalValid = isValid && !projectExists;
                    createButton.disabled = !finalValid;
                    createButton.style.opacity = finalValid ? '1' : '0.5';
                    createButton.style.cursor = finalValid ? 'pointer' : 'not-allowed';

                    // 更新必填字段的视觉提示
                    // 只有当项目不存在时，才根据是否为空设置边框颜色
                    // 如果项目已存在，边框颜色会在projectExistsResult消息处理中设置
                    if (!projectExists) {
                        projectNameInput.style.borderColor = projectName ? '' : 'var(--vscode-errorForeground)';
                    }

                    if (isLocalMode) {
                        projectLocationInput.style.borderColor = projectLocation ? '' : 'var(--vscode-errorForeground)';
                    } else {
                        projectLocationInput.style.borderColor = '';
                    }

                    // 检查项目是否已存在
                    checkProjectExists();
                }

                // 将validateForm函数引用保存到全局变量
                validateFormFunc = validateForm;

                // 处理开发模式切换
                function handleDevModeChange() {
                    if (localMode.checked) {
                        locationGroup.style.display = 'flex'; // 显示 Location 字段
                    } else {
                        locationGroup.style.display = 'none'; // 隐藏 Location 字段
                    }

                    // 模式切换后重新验证表单
                    validateForm();
                }

                // 添加事件监听器
                projectNameInput.addEventListener('input', validateForm);
                projectLocationInput.addEventListener('input', validateForm);
                remoteMode.addEventListener('change', handleDevModeChange);
                localMode.addEventListener('change', handleDevModeChange);

                // Custom数据库相关输入变更时校验
                if (dbType) dbType.addEventListener('change', validateForm);
                if (dbUrl) dbUrl.addEventListener('input', validateForm);
                if (dbUser) dbUser.addEventListener('input', validateForm);
                if (dbPassword) dbPassword.addEventListener('input', validateForm);
                if (dbAutoRadio) dbAutoRadio.addEventListener('change', validateForm);
                if (dbExistingRadio) dbExistingRadio.addEventListener('change', validateForm);
                if (dbCustomRadio) dbCustomRadio.addEventListener('change', validateForm);
                if (existingDbSelect) existingDbSelect.addEventListener('change', validateForm);

                // 处理浏览按钮点击事件
                browseButton.addEventListener('click', () => {
                    vscode.postMessage({ type: 'browse' });
                });

                // 初始状态检查
                handleDevModeChange();
                validateForm();
            }

            // 在页面加载完成后初始化
            window.addEventListener('DOMContentLoaded', () => {
                // 延迟一帧，确保样式已应用
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        // 确保加载遮罩层初始状态是隐藏的
                        const overlay = document.getElementById('loadingOverlay');
                        if (overlay) {
                            overlay.classList.remove('active');
                        }
                        vscode.postMessage({ type: 'requestTemplates', query: '' });
                        vscode.postMessage({ type: 'getProjectDatabaseList' });
                        vscode.postMessage({ type: 'getUserPath' });

                        // 初始化模板选择并获取getSelectedTemplate函数
                        getSelectedTemplate = initTemplateSelection();

                        // 初始化表单验证
                        initFormValidation();

                        // 默认隐藏数据库配置部分
                        const databaseSection = document.getElementById('databaseRadioGroup').closest('.form-group');
                        if (databaseSection) {
                            databaseSection.style.display = 'none';
                        }


                        // 数据库 radio 切换显示逻辑
                        const dbRadios = document.querySelectorAll('input[name="databaseMode"]');
                        const customDbConfig = document.getElementById('customDbConfig');
                        const existingDbSelectWrapper = document.getElementById('existingDbSelectWrapper');
                        function handleDbRadioChange() {
                            const selected = document.querySelector('input[name="databaseMode"]:checked').value;
                            if (selected === 'custom') {
                                customDbConfig.style.display = 'block';
                                existingDbSelectWrapper.style.display = 'none';
                            } else if (selected === 'existing') {
                                customDbConfig.style.display = 'none';
                                existingDbSelectWrapper.style.display = 'flex';
                                // 请求已有数据库列表
                                vscode.postMessage({ type: 'getProjectDatabaseList' });
                            } else {
                                customDbConfig.style.display = 'none';
                                existingDbSelectWrapper.style.display = 'none';
                            }
                        }
                        dbRadios.forEach(radio => {
                            radio.addEventListener('change', handleDbRadioChange);
                        });
                        // 初始状态
                        handleDbRadioChange();
                        // 自动聚焦
                        document.getElementById('projectName').focus();



                        // 测试连接按钮演示（成功/失败切换）
                        // const testBtn = document.getElementById('testDbConnection');
                        // const testStatus = document.getElementById('testDbStatus');
                        // if (testBtn && testStatus) {
                        //     testBtn.addEventListener('click', () => {
                        //         // 修改此变量可演示成功或失败
                        //         // 实际应用中应根据后端返回结果判断
                        //         const isSuccess = false; // true为成功，false为失败

                        // 		// 清空原有子节点
                        // 		while(testStatus.firstChild) {
                        // 			testStatus.removeChild(testStatus.firstChild);
                        // 		}

                        // 		// 创建图标和文本节点
                        // 		const iconElement = document.createElement('span');
                        // 		const textElement = document.createElement('span');

                        // 		testStatus.style.display = 'inline';

                        // 		if (isSuccess) {
                        // 			iconElement.innerHTML = '<svg class="icon" viewBox="0 0 1024 1024" width="14" height="14"><path d="M512 25.6A486.4 486.4 0 0 1 998.4 512 486.4 486.4 0 0 1 512 998.4 486.4 486.4 0 1 1 512 25.6z m-69.504 626.368L288.64 498.176l-54.272 54.272L415.36 733.44a38.4 38.4 0 0 0 48.256 4.928l6.016-4.928 316.8-316.8-54.272-54.272-289.664 289.664z" fill="#2EA043"></path></svg>';
                        // 			textElement.textContent = 'Succeeded';
                        // 			textElement.style.color = '#2EA043';
                        // 		} else {
                        // 			iconElement.innerHTML = '<svg class="icon" viewBox="0 0 1024 1024" width="15" height="15"><path d="M512 25.6A486.4 486.4 0 0 1 998.4 512 486.4 486.4 0 0 1 512 998.4 486.4 486.4 0 1 1 512 25.6zM512 704a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z m-38.4-480V640h76.8V224H473.6z" fill="#F85149"></path></svg>';
                        // 			textElement.textContent = 'Failed Failed  FailedFailedFailedFailedFailedFailed  Failed     Failed Failed Failed Failed Failed Failed Failed  FailedFailedFailedFailedFailedFailed  Failed     Failed Failed Failed Failed Failed   Failed Failed  FailedFailedFailedFailedFailedFailed  Failed     Failed Failed Failed Failed Failed  Failed Failed  FailedFailedFailedFailedFailedFailed  Failed     Failed Failed Failed Failed Failed';
                        // 			textElement.style.color = '#F85149';
                        // 		}

                        // 		// 添加子节点
                        // 		testStatus.appendChild(iconElement);
                        // 		testStatus.appendChild(textElement);

                        //         setTimeout(() => {
                        //             testStatus.style.display = 'none';
                        //         }, 2000);
                        //     });
                        // }
                    });
                });
            });

            // 创建项目的实际处理函数
            function handleCreateProject() {
                // 如果已经在创建项目中，则不重复执行
                if (isCreatingProject) {
                    return;
                }

                // 设置创建中标志
                isCreatingProject = true;

                const projectName = document.getElementById('projectName').value.trim();
                const projectDescription = document.getElementById('projectDescription').value.trim();
                // devMode: 1=remote, 2=local
                const devMode = document.getElementById('remoteMode').checked ? 1 : 2;
                const projectLocation = document.getElementById('projectLocation').value.trim();

                // 获取选中的模板
                let templateInfo = null;

                if (getSelectedTemplate) {
                    const selectedTemplate = getSelectedTemplate();

                    if (selectedTemplate.value) {
                        // 查找选中的模板项
                        const selectedItem = document.querySelector(`.dropdown-item[data-value="${selectedTemplate.value}"]`);

                        if (selectedItem) {
                            const title = selectedItem.querySelector('.dropdown-item-title').textContent;
                            const description = selectedItem.querySelector('.dropdown-item-description').textContent;

                            // 优先使用自定义下拉的版本
                            let version = selectedTemplate.version || null;
                            if (!version) {
                                // 兼容旧逻辑
                                const versionMatch = description.match(/\((.*?)\)/);
                                version = versionMatch ? versionMatch[1] : null;
                            }

                            // 查找所属类别
                            let category = selectedItem.previousElementSibling;
                            while (category && !category.classList.contains('dropdown-category')) {
                                category = category.previousElementSibling;
                            }

                            // 获取下载地址
                            const downloadUrl = selectedItem.getAttribute('data-download-url') || '';

                            // 获取是否有数据库属性
                            const useDatabase = selectedItem.getAttribute('data-has-database') === 'true';

                            let componentVersions = [];
                            try {
                                const componentStr = selectedItem.getAttribute('data-component-versions');
                                if (componentStr) {
                                    componentVersions = JSON.parse(componentStr);
                                }
                            } catch (e) {
                                componentVersions = [];
                            }

                            const prompt = selectedItem.getAttribute('data-template-prompt') || '';
                            const templateClassCode = selectedItem.getAttribute('data-template-classCode') || '';
                            templateInfo = {
                                id: selectedTemplate.value,
                                name: title,
                                downloadUrl: downloadUrl,
                                category: category ? category.textContent : '',
                                templateClassId: Number(selectedItem.getAttribute('data-template-class-id')),
                                version: version,
                                templateName: title,
                                versionName: version,
                                useDatabase: useDatabase,
                                componentVersions: componentVersions && componentVersions.length > 0 ? componentVersions : undefined,
                                prompt,
                                templateClassCode
                            };
                        }
                    }
                }

                // 获取Database模式和配置
                let dbMode = 0;
                let databaseConfig = undefined;

                // 只有当模板支持数据库时才获取数据库配置
                if (templateInfo && templateInfo.useDatabase) {
                    const dbRadioVal = document.querySelector('input[name="databaseMode"]:checked')?.value || '';
                    if (dbRadioVal === 'auto') {
                        dbMode = 1;
                    } else if (dbRadioVal === 'existing') {
                        dbMode = 2;
                    } else if (dbRadioVal === 'custom') {
                        dbMode = 3;
                    }
                    if (dbMode === 2) {
                        // 选择已有数据库
                        const existingDbSelect = document.getElementById('existingDbSelect');
                        const selectedValue = existingDbSelect ? existingDbSelect.getAttribute('data-selected-value') : null;
                        databaseConfig = {
                            instanceId: selectedValue
                        };
                    } else if (dbMode === 3) {
                        // 自定义数据库
                        databaseConfig = {
                            type: document.getElementById('dbType')?.value.trim() || '',
                            url: document.getElementById('dbUrl')?.value.trim() || '',
                            user: document.getElementById('dbUser')?.value.trim() || '',
                            password: document.getElementById('dbPassword')?.value.trim() || ''
                        };
                    }
                }

                // 验证表单
                let errorMessage = '';

                if (!projectName) {
                    errorMessage = 'Project name cannot be empty';
                } else if (devMode === 2 && !projectLocation) {
                    errorMessage = 'Project location cannot be empty for local mode';
                }

                if (errorMessage) {
                    vscode.postMessage({ type: 'error', message: errorMessage });
                    isCreatingProject = false; // 重置创建标志
                    return;
                }

                // 显示加载状态并禁用表单
                showLoadingState(true);

                if (devMode === 2) {
                    vscode.postMessage({ type: 'setLocalPath', localPath: projectLocation });
                }

                // 发送创建项目消息
                vscode.postMessage({
                    type: 'create',
                    data: {
                        projectName,
                        projectDescription,
                        devMode,
                        projectLocation: devMode === 2 ? projectLocation + '/' + projectName : undefined,
                        templateInfo,
                        databaseMode: dbMode,
                        databaseConfig: databaseConfig
                    }
                });

                // 设置一个安全超时，如果后端长时间没有响应，也能重置状态
                const timeoutId = setTimeout(() => {
                    isCreatingProject = false;
                    clearTimeout(timeoutId);
                    showLoadingState(false);
                }, 30000); // 30秒后自动重置
            }

            // 创建项目按钮点击事件 - 使用防抖
            document.getElementById('createButton').addEventListener('click', () => {
                handleCreateProject();
            });

            function showLoadingState(isLoading = true) {
                // 获取加载遮罩
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (!loadingOverlay) return;

                // 获取所有表单元素
                const formInputs = document.querySelectorAll('input, textarea, select, button');
                const customSelect = document.querySelector('.custom-select');

                if (isLoading) {
                    // 显示加载遮罩
                    loadingOverlay.classList.add('active');

                    // 禁用所有输入元素
                    formInputs.forEach(input => {
                        input.disabled = true;
                    });

                    // 禁用自定义下拉框
                    if (customSelect) {
                        customSelect.style.pointerEvents = 'none';
                        customSelect.style.opacity = '0.7';
                    }
                } else {
                    // 隐藏加载遮罩
                    loadingOverlay.classList.remove('active');

                    // 启用所有输入元素
                    formInputs.forEach(input => {
                        input.disabled = false;
                    });

                    // 启用自定义下拉框
                    if (customSelect) {
                        customSelect.style.pointerEvents = 'auto';
                        customSelect.style.opacity = '1';
                    }
                }
            }

            document.getElementById('cancelButton').addEventListener('click', () => {
                // 如果加载状态已激活，则不处理取消操作
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay && loadingOverlay.classList.contains('active')) {
                    return;
                }

                vscode.postMessage({ type: 'cancel' });
            });

            // 监听来自VS Code的消息
            window.addEventListener('message', event => {
                const message = event.data;

                switch (message.type) {
                    case 'setUserPath':
                        if (message.userPath) {
                            document.getElementById('projectLocation').value = message.userPath
                        }

                        break;

                    case 'setDatabases':
                        if (message.databases && Array.isArray(message.databases)) {
                            initSelectOption("existingDbSelect", message.databases);
                        }
                        // 重新验证表单
                        if (validateFormFunc) {
                            validateFormFunc();
                        }
                        break;
                    case 'setTemplates':
                        // 处理从TypeScript接收到的模板数据
                        if (message.templates && Array.isArray(message.templates)) {
                            // 存储模板数据
                            templatesData = message.templates;
                            document.querySelector('.dropdown-panel').innerHTML = ''
                            const data = formatTemplatesData(templatesData);
                            renderTemplates(data);
                        }
                        break;

                    case 'loading':
                        // 根据loading参数控制加载状态
                        if (typeof message.loading === 'boolean') {
                            showLoadingState(message.loading);

                            // 如果loading状态结束，重置创建项目标志
                            if (message.loading === false) {
                                isCreatingProject = false;
                            }
                        }
                        break;
                    case 'setLocation':
                        // 更新位置字段
                        if (message.location) {
                            const projectLocationInput = document.getElementById('projectLocation');
                            projectLocationInput.value = message.location;

                            // 手动触发 input 事件，以便更新表单验证状态
                            projectLocationInput.dispatchEvent(new Event('input'));
                        }
                        break;
                    case 'projectExistsResult':
                        // 处理项目存在检查结果
                        const projectExistsWarning = document.getElementById('projectExistsWarning');
                        if (projectExistsWarning) {
                            // 更新警告显示
                            projectExistsWarning.style.display = message.exists ? 'block' : 'none';

                            // 更新项目存在状态
                            projectExists = message.exists;

                            // 设置项目名称输入框的边框颜色
                            const projectNameInput = document.getElementById('projectName');
                            if (projectNameInput) {
                                projectNameInput.style.borderColor = message.exists ? 'var(--vscode-errorForeground)' : '';
                            }

                            // 重新验证表单
                            if (validateFormFunc) {
                                validateFormFunc();
                            }
                        }
                        break;
                    case 'error':
                        errorShow(message.message)
                        // 结束加载状态，允许再次点击
                        isCreatingProject = false;
                        showLoadingState(false);
                        break;
                }
            });

            // 按Enter键提交表单
            document.addEventListener('keydown', (e) => {
                // 如果加载状态已激活，则不处理键盘事件
                const loadingOverlay = document.getElementById('loadingOverlay');
                if (loadingOverlay && loadingOverlay.classList.contains('active')) {
                    return;
                }

                // if (e.key === 'Enter') {
                //     // 直接调用处理函数，而不是触发按钮点击
                //     handleCreateProject();
                // }

                // ESC键取消
                if (e.key === 'Escape') {
                    document.getElementById('cancelButton').click();
                }
            });

            // 监听模版聚焦
            document.getElementById('templateInput').addEventListener('click', () => {
                const dropdownPanel = document.querySelector('.dropdown-panel');
                if (dropdownPanel) {
                    dropdownPanel.style.display = 'block';
                }
                // 如果没有数据，尝试重新获取
                if (dropdownItems.length === 0) {
                    const data = formatTemplatesData(templatesData);
                    renderTemplates(data);
                }
            });
            // 搜索过滤
            document.getElementById('templateInput').addEventListener('input', () => {
                const searchText = templateInput.value.toLowerCase();
                document.querySelector('.dropdown-panel').innerHTML = 'loading...'
                // 发送请求获取模板数据
                const timer = setTimeout(() => {
                    clearTimeout(timer);
                    vscode.postMessage({ type: 'requestTemplates', query: searchText });
                }, 500)
            });


            // 点击外部隐藏
            document.addEventListener('click', (event) => {
                if (!customSelect.contains(event.target)) {
                    // 移除active类
                    customSelect.classList.remove('active');

                    // 明确隐藏下拉面板
                    if (document.querySelector('.dropdown-panel')) {
                        document.querySelector('.dropdown-panel').style.display = 'none';
                    }
                    if (!selectedValue) {
                        document.getElementById('templateInput').value = '';
                    }
                }
            });

            // 渲染模板项
            function renderTemplates(data) {
                if (!document.querySelector('.dropdown-panel')) {
                    return;
                }
                document.querySelector('.dropdown-panel').innerHTML = '';

                // 工程类模版排前，语言类模版排后
                if (Array.isArray(data)) {
                    data = data.slice().sort((a, b) => {
                        const aIsEngineer = (a.category && a.category.includes('Engineering')) || (a.items && a.items[0] && a.items[0].templateClassCode === 'engineer');
                        const bIsEngineer = (b.category && b.category.includes('Engineering')) || (b.items && b.items[0] && b.items[0].templateClassCode === 'engineer');
                        if (aIsEngineer && !bIsEngineer) return -1;
                        if (!aIsEngineer && bIsEngineer) return 1;
                        return 0;
                    });
                }
                // 确保 data 是数组
                // 检查是否有数据
                const hasItems = data && Array.isArray(data) && data.some(group => group.items && group.items.length > 0);
                if (!hasItems) {
                    // 插入暂无数据提示
                    const emptyDiv = document.createElement('div');
                    emptyDiv.className = 'dropdown-empty';
                    emptyDiv.textContent = '暂无数据';
                    dropdownPanel.appendChild(emptyDiv);
                    return;
                }

                data.forEach(function (group) {
                    var cat = document.createElement('div');
                    cat.className = 'dropdown-category';
                    cat.textContent = group.category;
                    dropdownPanel.appendChild(cat);

                    if (group.items && Array.isArray(group.items)) {
                        group.items.forEach(function (item) {
                            var div = document.createElement('div');
                            div.className = 'dropdown-item';
                            div.setAttribute('data-value', item.value);
                            div.setAttribute('tabindex', '0');
                            // 设置下载地址属性
                            if (item.downloadUrl) {
                                div.setAttribute('data-download-url', item.downloadUrl);
                            }

                            // 设置是否有数据库属性
                            div.setAttribute('data-has-database', item.useDatabase === true ? 'true' : 'false');

                            // 设置模板类型属性
                            div.setAttribute('data-is-language-template', item.isLanguageTemplate === true ? 'true' : 'false');
                            // 新增：写入 templateClassId 到 DOM 属性
                            div.setAttribute('data-template-prompt', item.prompt);

                            div.setAttribute('data-template-classCode', item.templateClassCode);

                            // 设置组件版本信息
                            if (item.componentVersions && item.componentVersions.length > 0) {
                                div.setAttribute('data-component-versions', JSON.stringify(item.componentVersions));
                            }

                            // 添加模板图片
                            if (item.imgUrl) {
                                var img = document.createElement('img');
                                img.className = 'template-item-img';
                                img.src = item.imgUrl;
                                img.alt = item.title;
                                img.onerror = function () {
                                    // 图片加载失败时使用默认图标
                                    this.style.display = 'none';
                                };
                                div.appendChild(img);
                            }

                            var content = document.createElement('div');
                            content.className = 'dropdown-item-content';

                            var leftTitleContent = document.createElement('div');
                            leftTitleContent.className = 'dropdown-item-left-title';
                            var title = document.createElement('div');
                            title.className = 'dropdown-item-title';
                            title.textContent = item.title;
                            leftTitleContent.appendChild(title);
                            // 创建描述行
                            var descRow = document.createElement('div');
                            // descRow.style.display = 'flex';
                            // descRow.style.alignItems = 'center';
                            // descRow.style.marginBottom = '5px';
                            var desc = document.createElement('div');
                            desc.className = 'dropdown-item-description';
                            desc.textContent = item.description;
                            descRow.appendChild(desc);
                            leftTitleContent.appendChild(descRow);

                            content.appendChild(leftTitleContent);

                            if (item.versions && item.versions.length) {

                                // 创建版本行
                                var row = document.createElement('div');
                                row.style.display = 'flex';
                                row.style.marginLeft = '20px'

                                row.style.alignItems = 'end';
                                row.style.justifyContent = 'flex-end'; // 右对齐

                                // 判断是否为语言类模板
                                const isLanguageTemplate = item.isLanguageTemplate === true;

                                // 如果是语言类模板，添加版本选择下拉框
                                if (isLanguageTemplate) {
                                    // 创建一个容器来包含Version标签和自定义下拉
                                    var versionContainer = document.createElement('div');
                                    versionContainer.style.marginLeft = 'auto';
                                    versionContainer.style.display = 'flex';
                                    versionContainer.style.alignItems = 'center';
                                    versionContainer.style.whiteSpace = 'nowrap';
                                    versionContainer.style.textAlign = 'right';

                                    var versionLabel = document.createElement('span');
                                    versionLabel.textContent = 'Version: ';
                                    versionLabel.className = 'template-version-select-label';

                                    // 自定义下拉结构
                                    var versionSelectWrapper = document.createElement('div');
                                    versionSelectWrapper.className = 'custom-select version-custom-select select-container';
                                    versionSelectWrapper.style.marginLeft = '5px';
                                    versionSelectWrapper.style.minWidth = '120px';
                                    versionSelectWrapper.style.display = 'inline-block';

                                    var versionInput = document.createElement('input');
                                    versionInput.type = 'text';
                                    versionInput.className = 'template-version-input template-input';
                                    versionInput.readOnly = true;
                                    versionInput.value = item.componentVersions[0].versionName; // 默认第一个
                                    versionInput.setAttribute('data-template', item.value);
                                    versionInput.setAttribute('data-selected-value', item.componentVersions[0].versionName);

                                    var versionDropdown = document.createElement('div');
                                    versionDropdown.className = 'dropdown-panel version-dropdown-panel';
                                    versionDropdown.style.zIndex = 200;
                                    item.componentVersions.forEach(function (ver, idx) {
                                        var opt = document.createElement('div');
                                        opt.className = 'version-dropdown-item';
                                        opt.textContent = "v" + ver.versionName;
                                        opt.setAttribute('data-value', ver.versionName);
                                        if (idx === 0) opt.classList.add('selected');
                                        versionDropdown.appendChild(opt);

                                        opt.addEventListener('click', function (e) {
                                            e.stopPropagation();
                                            // 设置input
                                            versionInput.value = ver.versionName;
                                            versionInput.setAttribute('data-selected-value', ver.versionName);
                                            // 高亮
                                            Array.from(versionDropdown.children).forEach(child => child.classList.remove('selected'));
                                            opt.classList.add('selected');
                                            versionDropdown.style.display = 'none';
                                            versionSelectWrapper.classList.remove('active');
                                        });
                                    });

                                    versionInput.addEventListener('click', function (e) {
                                        e.stopPropagation();
                                        // 只显示当前下拉
                                        document.querySelectorAll('.version-dropdown-panel').forEach(panel => {
                                            if (panel !== versionDropdown) panel.style.display = 'none';
                                        });
                                        if (versionDropdown.style.display === 'block') {
                                            versionDropdown.style.display = 'none';
                                            versionSelectWrapper.classList.remove('active');
                                            return false;
                                        }
                                        versionDropdown.style.display = 'block';
                                        versionSelectWrapper.classList.add('active');

                                        // 检查弹窗是否会被遮挡
                                        const rect = versionDropdown.getBoundingClientRect();
                                        const windowHeight = window.innerHeight;
                                        const dropdownHeight = versionDropdown.offsetHeight;
                                        const spaceBelow = windowHeight - rect.bottom;
                                        const spaceAbove = rect.top;
                                        const minMargin = 10; // 最小边距

                                        if (spaceBelow >= dropdownHeight + minMargin) {
                                            // 如果下方空间足够，向下显示
                                            versionDropdown.style.top = '100%';
                                            versionDropdown.style.bottom = 'auto';
                                            versionDropdown.style.maxHeight = `${spaceBelow - minMargin}px`;
                                        } else if (spaceAbove >= dropdownHeight + minMargin) {
                                            // 如果上方空间足够，向上显示
                                            versionDropdown.style.bottom = '100%';
                                            versionDropdown.style.top = 'auto';
                                            versionDropdown.style.maxHeight = `${spaceAbove - minMargin}px`;
                                        } else {
                                            // 如果上下空间都不够，选择较大的一边并适应其大小
                                            if (spaceBelow >= spaceAbove) {
                                                versionDropdown.style.top = '100%';
                                                versionDropdown.style.bottom = 'auto';
                                                versionDropdown.style.maxHeight = `${spaceBelow - minMargin}px`;
                                            } else {
                                                versionDropdown.style.bottom = '100%';
                                                versionDropdown.style.top = 'auto';
                                                versionDropdown.style.maxHeight = `${spaceAbove - minMargin}px`;
                                            }
                                        }
                                        versionDropdown.style.overflowY = 'auto';

                                        // 只绑定一次全局点击关闭事件
                                        if (!versionSelectWrapper._outsideClickListener) {
                                            versionSelectWrapper._outsideClickListener = function (event) {
                                                if (!versionSelectWrapper.contains(event.target)) {
                                                    versionDropdown.style.display = 'none';
                                                    versionSelectWrapper.classList.remove('active');
                                                    document.removeEventListener('click', versionSelectWrapper._outsideClickListener, true);
                                                    versionSelectWrapper._outsideClickListener = null;
                                                }
                                            };
                                            document.addEventListener('click', versionSelectWrapper._outsideClickListener, true);
                                        }
                                    });
                                    // 点击外部关闭
                                    document.addEventListener('click', function (e) {
                                        if (!versionSelectWrapper.contains(e.target)) {
                                            versionDropdown.style.display = 'none';
                                            versionSelectWrapper.classList.remove('active');
                                        }
                                    });

                                    var versionIcon = document.createElement('span');
                                    versionIcon.className = 'version-dropdown-icon';
                                    versionIcon.innerHTML = `
                                        <svg class="version-dropdown-svg" width="20" height="12" viewBox="0 0 20 12" focusable="false" aria-hidden="true">
                                            <polyline points="4,3 10,9 16,3" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>`;
                                    versionSelectWrapper.appendChild(versionInput);
                                    versionSelectWrapper.appendChild(versionIcon);
                                    versionSelectWrapper.appendChild(versionDropdown);
                                    versionContainer.appendChild(versionLabel);
                                    versionContainer.appendChild(versionSelectWrapper);
                                    row.appendChild(versionContainer);
                                } else {
                                    // 创建一个显示组件版本的容器
                                    var versionInfo = document.createElement('div');
                                    versionInfo.className = 'template-version-info';
                                    versionInfo.style.marginLeft = 'auto'; // 使用auto使其靠右对齐
                                    versionInfo.style.whiteSpace = 'nowrap';
                                    versionInfo.style.textAlign = 'right'; // 文本右对齐

                                    // 获取组件版本信息
                                    const componentVersions = JSON.parse(div.getAttribute('data-component-versions') || '[]');
                                    if (componentVersions.length > 0) {
                                        // 拼接组件版本信息
                                        const versionText = componentVersions.map(cv =>
                                            `${cv.componentName} ${cv.versionName}`
                                        ).join(' & ');
                                        versionInfo.textContent = versionText;
                                    }

                                    row.appendChild(versionInfo);
                                }

                                content.appendChild(row);
                            }

                            div.appendChild(content);
                            dropdownPanel.appendChild(div);
                        });
                    }
                });

                // 更新 dropdownItems 数组
                dropdownItems = Array.from(dropdownPanel.querySelectorAll('.dropdown-item'));

                // 确保下拉面板在聚焦时可见
                if (document.activeElement === templateInput) {
                    customSelect.classList.add('active');
                    dropdownPanel.style.display = 'block';
                }

                // 模板下拉选项，被点击时，触发卡片渲染
                dropdownItems.forEach(item => {
                    item.addEventListener('click', () => {
                        dropdownItems.forEach(i => i.classList.remove('selected'));
                        item.classList.add('selected');
                        selectedValue = item.getAttribute('data-value');
                        selectedText = item.querySelector('.dropdown-item-title').textContent;
                        templateInput.value = selectedText;

                        // 移除active类
                        customSelect.classList.remove('active');

                        // 明确隐藏下拉面板
                        if (dropdownPanel) {
                            dropdownPanel.style.display = 'none';
                        }
                        // 渲染模板卡片
                        const templateCardContainer = document.getElementById('templateCardContainer');
                        // 获取模板详细信息
                        const title = item.querySelector('.dropdown-item-title').textContent;
                        let desc = item.querySelector('.dropdown-item-description')?.textContent || '';
                        let details = '';
                        let logo = '';
                        let imgUrl = item.querySelector('.template-item-img')?.src || '';

                        // 获取是否有数据库属性
                        const useDatabase = item.getAttribute('data-has-database') === 'true';

                        // 获取模板类型
                        const isLanguageTemplate = item.getAttribute('data-is-language-template') === 'true';

                        // 根据useDatabase属性显示或隐藏数据库配置部分
                        const databaseSection = document.getElementById('databaseRadioGroup').closest('.form-group');
                        if (databaseSection) {
                            databaseSection.style.display = useDatabase ? 'flex' : 'none';
                            // 确保布局正确
                            if (useDatabase) {
                                databaseSection.style.alignItems = 'flex-start';
                                databaseSection.style.width = '100%';
                            }
                        }

                        // 隐藏自定义数据库配置部分
                        const customDbConfig = document.getElementById('customDbConfig');
                        if (customDbConfig) {
                            customDbConfig.style.display = 'none';
                        }

                        // 重置数据库模式选择为"自动创建"
                        const dbAutoRadio = document.getElementById('dbAuto');
                        if (dbAutoRadio) {
                            dbAutoRadio.checked = true;
                        }

                        // 根据模板类型获取版本信息
                        if (isLanguageTemplate) {
                            // 语言类模板：从自定义下拉获取版本
                            const versionInput = item.querySelector && item.querySelector('.template-version-input');
                            let versionText = '';
                            if (versionInput) {
                                versionText = versionInput.value;
                            } else {
                                // 兼容旧逻辑
                                const versionSelect = item.querySelector('.template-version-select');
                                if (versionSelect) {
                                    const selectedOption = versionSelect.options[versionSelect.selectedIndex];
                                    versionText = selectedOption ? selectedOption.textContent : '';
                                }
                            }
                            if (versionText) {
                                details = `<span style="display: inline-block; margin-right: 5px;">Version:</span><span>${versionText}</span>`;
                            } else {
                                details = '';
                            }
                        } else {
                            // 工程类模板：从组件版本信息获取版本
                            const versionInfo = item.querySelector('.template-version-info');
                            if (versionInfo) {
                                details = versionInfo.textContent;
                            } else {
                                // 尝试从data-component-versions属性获取
                                try {
                                    const componentVersions = JSON.parse(item.getAttribute('data-component-versions') || '[]');
                                    if (componentVersions.length > 0) {
                                        details = componentVersions.map(cv =>
                                            `${cv.componentName} ${cv.versionName}`
                                        ).join(' & ');
                                    }
                                } catch (e) {
                                    details = '';
                                }
                            }
                        }

                        // 优先使用图片URL，如果没有则根据模板类型选择合适的图标
                        if (imgUrl) {
                            logo = `<img src="${imgUrl}" class="template-card-logo" alt="${title}" style="width: 40px; height: 40px; object-fit: contain;">`;
                        } else if (selectedValue.toLowerCase().includes('node') || title.toLowerCase().includes('node')) {
                            logo = `<span class="template-card-logo">🟩</span>`;
                        } else if (selectedValue.toLowerCase().includes('java') || title.toLowerCase().includes('java')) {
                            logo = `<span class="template-card-logo">☕</span>`;
                        } else if (selectedValue.toLowerCase().includes('python') || title.toLowerCase().includes('python')) {
                            logo = `<span class="template-card-logo">🐍</span>`;
                        } else if (selectedValue.toLowerCase().includes('react') || title.toLowerCase().includes('react')) {
                            logo = `<span class="template-card-logo">⚛️</span>`;
                        } else if (selectedValue.toLowerCase().includes('vue') || title.toLowerCase().includes('vue')) {
                            logo = `<span class="template-card-logo">🟢</span>`;
                        } else if (selectedValue.toLowerCase().includes('mysql') || title.toLowerCase().includes('mysql')) {
                            logo = `<span class="template-card-logo">🐬</span>`;
                        } else if (selectedValue.toLowerCase().includes('ruoyi') || title.toLowerCase().includes('ruoyi')) {
                            logo = `<span class="template-card-logo">🌱</span>`;
                        } else {
                            logo = `<span class="template-card-logo">📦</span>`;
                        }
                        // 渲染卡片HTML
                        templateCardContainer.innerHTML = `
                                    <div class="template-card">
                                        <button class="template-card-close" title="Remove">&times;</button>
                                        ${logo}
                                        <div class="template-card-title">${title}</div>
                                        <div class="template-card-desc">${desc}</div>
                                        ${details ? `<div class="template-card-desc" style="text-align: right;">${details}</div>` : ''}
                                    </div>
                                `;
                        // 绑定关闭事件
                        const closeBtn = templateCardContainer.querySelector('.template-card-close');
                        if (closeBtn) {
                            const createButton = document.getElementById('createButton');
                            closeBtn.addEventListener('click', () => {
                                templateCardContainer.innerHTML = '';
                                templateInput.value = '';
                                selectedValue = '';
                                selectedText = '';
                                // 删除选中模板后，隐藏 Database 区域
                                const databaseSection = document.getElementById('databaseRadioGroup').closest('.form-group');
                                if (databaseSection) databaseSection.style.display = 'none';
                                const customDbConfig = document.getElementById('customDbConfig');
                                customDbConfig.style.display = 'none';
                                // 初始化表单验证
                                createButton.style.cursor = 'pointer';
                                createButton.disabled = false;
                                createButton.style.opacity = '1';
                                validateFormFunc()
                            });
                        }
                    });
                });

            }
            // 错误处理
            function errorShow(message = '') {
                const errorMsgDiv = document.getElementById('createErrorMsg');
                if (errorMsgDiv) {
                    if (message) {
                        errorMsgDiv.textContent = "error: " + message;
                        errorMsgDiv.style.display = 'block';
                        // 添加一次性全局点击监听，点击其他地方隐藏错误
                        if (!window._createErrorMsgClickListener) {
                            window._createErrorMsgClickListener = function (event) {
                                // 如果点击的不是错误提示本身，也不是输入框，则隐藏
                                if (
                                    errorMsgDiv.style.display === 'block' &&
                                    !errorMsgDiv.contains(event.target) &&
                                    !(event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA')
                                ) {
                                    errorMsgDiv.style.display = 'none';
                                    document.removeEventListener('mousedown', window._createErrorMsgClickListener, true);
                                    window._createErrorMsgClickListener = null;
                                }
                            };
                            // 用 mousedown 体验更好，防止输入框 focus 后立即隐藏
                            document.addEventListener('mousedown', window._createErrorMsgClickListener, true);
                        }
                    } else {
                        errorMsgDiv.textContent = '';
                        errorMsgDiv.style.display = 'none';
                    }
                }
            }
        } catch (error) {

        }
    </script>
</body>

</html>
