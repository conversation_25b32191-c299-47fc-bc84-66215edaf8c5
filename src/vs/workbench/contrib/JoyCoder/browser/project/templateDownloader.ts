/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import { localize } from '../../../../../nls.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { IWorkbenchContribution } from '../../../../common/contributions.js';
import { ILifecycleService, LifecyclePhase } from '../../../../services/lifecycle/common/lifecycle.js';
import { Registry } from '../../../../../platform/registry/common/platform.js';
import { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry } from '../../../../common/contributions.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { INewProjectService } from '../../common/newProject.js';
import { ISaveTemplateDownloadInfo } from '../../common/newProject.js';
import { ICommandService, CommandsRegistry } from '../../../../../platform/commands/common/commands.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { URI } from '../../../../../base/common/uri.js';
import * as path from '../../../../../base/common/path.js';
import { IRemoteAgentService } from '../../../../services/remote/common/remoteAgentService.js';
import { IWorkbenchEnvironmentService } from '../../../../services/environment/common/environmentService.js';
import { CancellationToken } from '../../../../../base/common/cancellation.js';
import { IPathService } from '../../../../services/path/common/pathService.js';
import { SHOW_GLOBAL_LOADING_COMMAND_ID } from '../loading/globalLoadingCommands.js';
import { IWorkspaceTrustManagementService } from '../../../../../platform/workspace/common/workspaceTrust.js';
import { VSBuffer } from '../../../../../base/common/buffer.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';


export const AI_PROJECT_PATH = '.joycoder/project.json';
// 配置文件名常量
export const MODES_CONFIG_FILE = '.joycoder/mode.json';

// 模板数据接口定义
interface ITemplateData {
    url: string;
    targetPath: string;
    name?: string;
}

/**
 * 模板下载器贡献点
 * 负责处理项目模板的下载、解压和初始化
 * 支持本地和远程环境
 */
export class TemplateDownloaderContribution implements IWorkbenchContribution {
    // 命令ID常量
    private static readonly INIT_MODES_COMMAND = 'joycoder.joycoderEditor.initModes';
    // 模板下载信息
    private templateData: ISaveTemplateDownloadInfo | undefined;

    // 远程环境相关属性
    // 是否已经处理过模板下载
    private hasProcessedTemplate = false;
    // 远程连接状态
    private isRemoteConnected = false;
    // 轮询计数器
    private pollCount = 0;
    // 最大轮询次数
    private readonly MAX_POLL_COUNT = 30; // 最多轮询30次，约1分钟

    constructor(
        @INotificationService private readonly notificationService: INotificationService,
        @ILogService private readonly logService: ILogService,
        @INewProjectService private readonly newProjectService: INewProjectService,
        @ILifecycleService lifecycleService: ILifecycleService,
        @ICommandService private readonly commandService: ICommandService,
        @IFileService private readonly fileService: IFileService,
        @IRemoteAgentService private readonly remoteAgentService: IRemoteAgentService,
        @IWorkbenchEnvironmentService private readonly environmentService: IWorkbenchEnvironmentService,
        @IPathService private readonly pathService: IPathService,
        @IWorkspaceTrustManagementService private readonly workspaceTrustManagementService: IWorkspaceTrustManagementService,
        @IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService,
    ) {
        // 在工作台准备好后检查是否有模板需要下载
        lifecycleService.when(LifecyclePhase.Ready).then(() => {
            const templateData = this.newProjectService.getTemplateDownloadInfo();
            this.logService.info('TemplateDownloaderContribution: 初始化', templateData);
            if (templateData) {
                try {
                    this.templateData = JSON.parse(JSON.stringify(templateData));
                    // this.newProjectService.clearTemplateDownloadInfo();
                    this.setTrustedArea(this.templateData);
                    // 根据模板类型和环境决定处理方式
                    if (templateData.type === 'local') {
                        this.logService.info('TemplateDownloaderContribution: 处理本地模板下载');
                        this.checkForTemplateDownload();
                    } else if (templateData.type === 'remote' && this.environmentService.remoteAuthority) {
                        // this.setProjectAppliedTemplate();
                        this.logService.info(`TemplateDownloaderContribution: 处理远程模板下载: ${JSON.stringify(templateData)}`);
                        this.pollRemoteEnvironment();
                    } else {
                        this.logService.info('TemplateDownloaderContribution: 模板类型与环境不匹配，忽略');
                        this.templateData = undefined;
                    }
                } catch (error) {
                    this.logService.error(`TemplateDownloaderContribution: 初始化错误: ${error}`);
                }
            }
        });
    }

    // 设置受信任区域
    private async setTrustedArea(templateData: any): Promise<void> {
        const { projectName, sshConfigRes, remotePath } = templateData
        const sshHostLabel = `${projectName}_${sshConfigRes?.data?.projectId}`;
        const remoteAuthority = `ssh-remote+${sshHostLabel}`;
        const uri = URI.parse(`vscode-remote://${remoteAuthority}${remotePath}`);
        try {
            if (this.workspaceTrustManagementService) {
                await this.workspaceTrustManagementService.setUrisTrust([uri], true);
            }
        } catch (trustError) {
            console.error('将远程项目目录添加到受信任列表时出错:', trustError);
        }
    }

    /**
     * 轮询检查远程环境是否准备好
     */
    private pollRemoteEnvironment(): void {
        this.pollCount++;
        this.logService.info(`TemplateDownloaderContribution: 开始轮询检查远程环境 (${this.pollCount}/${this.MAX_POLL_COUNT})`);

        // 避免重复处理
        if (this.hasProcessedTemplate) {
            this.logService.info('TemplateDownloaderContribution: 已经处理过模板下载，跳过轮询');
            return;
        }

        // 检查是否超过最大轮询次数
        if (this.pollCount > this.MAX_POLL_COUNT) {
            this.logService.warn(`TemplateDownloaderContribution: 超过最大轮询次数 ${this.MAX_POLL_COUNT}，停止轮询`);
            return;
        }

        // 检查远程环境是否准备好
        this.checkConnectionState().then(isConnected => {
            if (isConnected) {
                this.logService.info('TemplateDownloaderContribution: 轮询检测到远程环境已准备好');
                this.isRemoteConnected = true;

                // 避免重复处理
                if (this.hasProcessedTemplate) {
                    this.logService.info('TemplateDownloaderContribution: 已经处理过模板下载，跳过');
                    return;
                }

                // 标记为已处理
                this.hasProcessedTemplate = true;

                // 检查模板下载
                this.checkForTemplateDownload().catch(error => {
                    this.logService.error('TemplateDownloaderContribution: 检查模板下载失败', error);
                });
            } else {
                this.logService.info(`TemplateDownloaderContribution: 远程环境尚未准备好，继续轮询 (${this.pollCount}/${this.MAX_POLL_COUNT})`);

                // 继续轮询，直到连接建立或超时
                setTimeout(() => {
                    this.pollRemoteEnvironment();
                }, 2000); // 每2秒轮询一次
            }
        }).catch(error => {
            this.logService.error(`TemplateDownloaderContribution: 轮询检查远程环境失败 (${this.pollCount}/${this.MAX_POLL_COUNT}): ${error}`);

            // 出错后继续轮询，除非超过最大次数
            if (this.pollCount < this.MAX_POLL_COUNT) {
                setTimeout(() => {
                    this.pollRemoteEnvironment();
                }, 5000); // 出错后延长轮询间隔
            }
        });
    }

    /**
     * 检查当前连接状态
     * @returns 是否已连接
     */
    private async checkConnectionState(): Promise<boolean> {
        try {
            // 检查是否有远程连接
            if (!this.environmentService.remoteAuthority) {
                this.logService.info('TemplateDownloaderContribution: 不是远程连接');
                return true; // 本地环境直接返回 true
            }

            // 检查远程连接是否已经建立
            const connection = this.remoteAgentService.getConnection();
            if (!connection) {
                this.logService.info('TemplateDownloaderContribution: 无法获取远程连接');
                return false;
            }

            // 尝试获取远程环境信息，如果成功则表示连接已建立
            const remoteEnv = await this.remoteAgentService.getEnvironment();
            if (remoteEnv) {
                this.logService.info(`TemplateDownloaderContribution: 远程环境已准备好，OS: ${remoteEnv.os}, 架构: ${remoteEnv.arch}`);

                // 尝试获取远程文件系统信息，进一步确认远程环境已准备好
                try {
                    // 尝试获取远程用户主目录
                    const userHomeUri = await this.pathService.userHome();
                    if (userHomeUri) {
                        this.logService.info(`TemplateDownloaderContribution: 远程用户主目录: ${userHomeUri.fsPath}`);

                        // 尝试检查远程用户主目录是否存在
                        const exists = await this.fileService.exists(userHomeUri);
                        this.logService.info(`TemplateDownloaderContribution: 远程用户主目录存在: ${exists}`);

                        if (exists) {
                            // 远程文件系统已准备好
                            return true;
                        }
                    }
                } catch (fsError) {
                    this.logService.warn(`TemplateDownloaderContribution: 检查远程文件系统失败: ${fsError}`);
                    // 即使文件系统检查失败，如果环境信息已获取，也认为连接已建立
                    return true;
                }

                // 环境信息已获取，认为连接已建立
                return true;
            }

            this.logService.info('TemplateDownloaderContribution: 无法获取远程环境信息');
            return false;
        } catch (error) {
            this.logService.error(`TemplateDownloaderContribution: 检查连接状态失败: ${error}`);
            return false;
        }
    }

    /**
     * 检查远程环境是否准备好
     * @param operation 操作名称，用于日志
     * @returns 远程环境是否准备好
     */
    private async isRemoteEnvironmentReady(operation: string): Promise<boolean> {
        // 如果不是远程环境，直接返回 true
        if (!this.environmentService.remoteAuthority) {
            return true;
        }

        // 检查连接状态标志
        if (!this.isRemoteConnected) {
            this.logService.warn(`TemplateDownloaderContribution: 远程连接尚未建立，无法${operation}`);

            // 尝试再次检查连接状态
            const isConnected = await this.checkConnectionState();
            if (!isConnected) {
                return false;
            }

            // 更新连接状态
            this.isRemoteConnected = true;
        }

        // 确保远程环境已经准备好
        try {
            const remoteEnv = await this.remoteAgentService.getEnvironment();
            if (!remoteEnv) {
                this.logService.warn(`TemplateDownloaderContribution: 远程环境尚未准备好，无法${operation}`);
                return false;
            }
            this.logService.info(`TemplateDownloaderContribution: 远程环境已准备好，OS: ${remoteEnv.os}`);
            return true;
        } catch (error) {
            this.logService.error(`TemplateDownloaderContribution: 获取远程环境信息失败: ${error}`);
            return false;
        }
    }

    /**
     * 创建适合当前环境的 URI（本地或远程）
     * @param filePath 文件路径
     * @returns 适合当前环境的 URI
     */
    private createAppropriateUri(filePath: string): URI {
        // 检查是否在远程环境中
        if (this.environmentService.remoteAuthority) {
            // 在远程环境中，使用适合远程的 URI
            // 使用 pathService 的 defaultUriScheme 来确定正确的方案
            return URI.from({
                scheme: this.pathService.defaultUriScheme,
                authority: this.environmentService.remoteAuthority,
                path: filePath
            });
        } else {
            // 在本地环境中，使用 file:// 方案
            return URI.file(filePath);
        }
    }

    /**
     * 读取项目根目录下的.joycodermodes文件，并返回第一个自定义模式的agentId
     * @param projectPath 项目路径
     * @returns 自定义模式的agentId，如果文件不存在或解析失败则返回undefined
     */
    private async readJoyCoderModeAgentId(projectPath: string): Promise<string | undefined> {
        try {
            // 构建配置文件路径
            const joyCoderModesFilePath = path.join(projectPath, MODES_CONFIG_FILE);

            // 创建适合当前环境的 URI
            const joyCoderModesFileUri = this.createAppropriateUri(joyCoderModesFilePath);

            // 检查文件是否存在
            if (!await this.fileService.exists(joyCoderModesFileUri)) {
                this.logService.warn(`项目根目录下的${MODES_CONFIG_FILE}文件不存在: ${joyCoderModesFilePath}`);
                return undefined;
            }

            // 读取并解析文件内容
            const content = (await this.fileService.readFile(joyCoderModesFileUri)).value.toString();
            const modesConfig = JSON.parse(content);

            // 检查并提取agentId
            if (modesConfig?.customModes?.[0]?.agentId) {
                const agentId = modesConfig.customModes[0].agentId;
                this.logService.info(`成功从${MODES_CONFIG_FILE}文件读取到agentId: ${agentId}`);
                return agentId;
            }

            this.logService.warn(`从${MODES_CONFIG_FILE}文件中未找到有效的自定义模式`);
            return undefined;
        } catch (error) {
            // 统一处理所有错误
            this.logService.error(`读取或解析${MODES_CONFIG_FILE}文件失败:`, error);
            return undefined;
        }
    }


    private async getProjectId(): Promise<number | null> {
        let projectId: number | null = null;
        try {
            const workspace = this.workspaceContextService.getWorkspace();
            const folders = workspace?.folders || [];
            const workspaceUri = folders.length > 0 ? folders[0].uri : '';
            if (this.templateData?.projectInfo?.id) {
                if (folders.length > 0) {
                    if (workspaceUri) {
                        if (workspaceUri.scheme === 'file' && this.templateData?.projectInfo?.id) {
                            if (this.templateData?.projectInfo.name === folders[0]?.name) {
                                projectId = this.templateData?.projectInfo?.id
                            }
                        }

                        if (workspaceUri.scheme === 'vscode-remote' && this.templateData?.projectInfo?.id) {
                            const authority = workspaceUri.authority;
                            const label = `${this.templateData.projectInfo.name}_${this.templateData?.projectInfo?.id}}`;
                            if (label && authority.includes(label)) {
                                projectId = this.templateData?.projectInfo?.id
                            }
                        }
                    }
                }
            } else {
                const projectList = await this.newProjectService.getProjectList();
                projectList.forEach(f => {
                    if (workspaceUri) {
                        const workspaceType = workspaceUri?.scheme === 'vscode-remote';
                        if (workspaceType && f.devMode === 1) {
                            const authority = workspaceUri.authority;
                            const label = `${f.name}_${f.id}`;
                            if (authority.includes(label)) {
                                projectId = f.id
                            }
                        }

                        if (workspaceUri.scheme === 'file' && f.devMode === 2) {
                            if (folders[0]?.name === f.name) {
                                projectId = f.id
                            }
                        }
                    }
                })
            }
        } catch (error) {
            console.error('获取项目ID失败', error);
        }

        return projectId;
    }

    /**
     * 处理模板下载和初始化
     * @param templateData 模板数据
     */
    private async processTemplate(templateData: ITemplateData): Promise<void> {
        const workspace = this.workspaceContextService.getWorkspace();
        const folders = workspace?.folders || [];
        if (!folders.length) {
            return;
        }
        const downloadParams = {
            url: templateData.url,
            targetPath: templateData.targetPath,
            name: templateData.name,
        }
        let projectId = await this.getProjectId();

        try {
            await this.commandService.executeCommand(SHOW_GLOBAL_LOADING_COMMAND_ID, true);
            // 获取项目详情
            if (projectId) {
                const res = await this.newProjectService.getProjectInfo(projectId);
                if (res?.code === 200) {
                    const projectInfo = res.data;
                    const projectTemplateApplied = projectInfo?.projectTemplateApplied;

                    if (!projectTemplateApplied && !templateData.url) {
                        // 如果项目已经应用了模板，则不需要再次下载
                        this.logService.info('TemplateDownloaderContribution: 项目已应用模板，跳过下载');
                        return;
                    }

                    if (projectTemplateApplied && !templateData.url) {
                        const workspace = this.workspaceContextService.getWorkspace();
                        const folders = workspace?.folders || [];
                        const workspaceUri = folders.length > 0 ? folders[0].uri : '';
                        downloadParams.name = projectInfo?.name
                        workspaceUri && (downloadParams.targetPath = workspaceUri?.path)
                        downloadParams.url = projectInfo?.projectTemplateUrl
                    }
                }
            }
        } catch (e) {
            this.logService.warn('TemplateDownloaderContribution: 显示全屏 loading 失败', e);
        }

        try {
            if (!downloadParams.url || !downloadParams.targetPath) {
                this.logService.warn('TemplateDownloaderContribution: 模板数据不完整，缺少必要的URL或目标路径');
                return;
            }

            // 检查远程环境是否准备好（如果是远程环境）
            if (this.environmentService.remoteAuthority) {
                const isReady = await this.isRemoteEnvironmentReady('处理模板下载');
                if (!isReady) {
                    this.logService.warn('TemplateDownloaderContribution: 远程环境未准备好，无法处理模板下载');
                    return;
                }
            }

            // 显示下载通知
            this.notificationService.info(localize('downloadingTemplate', "正在下载项目模板 '{0}'...", downloadParams.name || ''));

            this.logService.info(`TemplateDownloaderContribution: 开始下载项目模板: ${downloadParams.url}`);

            // 使用新项目服务执行下载
            const downloadResult = await this.newProjectService.downloadAndExtractTemplate(
                downloadParams.url,
                downloadParams.targetPath,
                downloadParams.name,
                CancellationToken.None
            );

            if (downloadResult) {
                // 读取项目根目录下的.joycodermodes文件
                const agentId = await this.readJoyCoderModeAgentId(downloadParams.targetPath);
                this.logService.info(`TemplateDownloaderContribution: 从.joycodermodes文件读取到的agentId: ${agentId || '未找到'}`);

                // 写入项目信息文件
                await this.ensureProjectJson(downloadParams.targetPath);
                // 初始化模式
                await this.initializeMode(agentId);

                this.newProjectService.clearTemplateDownloadInfo();
            }
        } finally {
            // 关闭全屏 loading
            try {
                await this.commandService.executeCommand(SHOW_GLOBAL_LOADING_COMMAND_ID, false);
            } catch (e) {
                this.logService.warn('TemplateDownloaderContribution: 关闭全屏 loading 失败', e);
            }
            projectId && this.newProjectService.setProjectAppliedTem({ projectId, applied: false })
        }
    }
    /**
     * 写入项目信息文件
     * @param targetPath
     * @param projectInfo
     */

    private async ensureProjectJson(targetPath: string): Promise<void> {
        try {
            const projectInfo = this.templateData?.projectInfo;
            console.log(this.templateData, 'this.templateData')
            if (!projectInfo) return;
            const projectJsonPath = path.join(targetPath, AI_PROJECT_PATH);
            const projectJsonUri = this.createAppropriateUri(projectJsonPath);
            let data = projectInfo;
            if (await this.fileService.exists(projectJsonUri)) {
                // 已存在则读取并合并
                const content = (await this.fileService.readFile(projectJsonUri)).value.toString();
                try {
                    const oldData = JSON.parse(content);
                    data = { ...oldData, ...projectInfo };
                } catch { /* ignore parse error */ }
            }
            // 写入或覆盖
            await this.fileService.writeFile(projectJsonUri, VSBuffer.fromString(JSON.stringify(data, null, 2)));
        } catch (error) {
            this.logService.warn('写入项目信息文件失败', error);
        }
    }

    /**
     * 初始化JoyCoder模式
     * @param agentId 代理ID
     */
    private async initializeMode(agentId: string | undefined): Promise<void> {
        // 先确保侧边栏被打开并获得焦点
        try {
            this.logService.info('TemplateDownloaderContribution: 尝试打开侧边栏');
            await this.commandService.executeCommand('workbench.action.focusAuxiliaryBar');
            this.logService.info('TemplateDownloaderContribution: 侧边栏已打开并获得焦点');
        } catch (sidebarError) {
            this.logService.error('TemplateDownloaderContribution: 打开侧边栏失败:', sidebarError);
            // 即使打开侧边栏失败，也继续执行后续操作
        }

        const commandId = TemplateDownloaderContribution.INIT_MODES_COMMAND;
        const hasCommand = !!CommandsRegistry.getCommand(commandId);

        this.logService.info(`TemplateDownloaderContribution: ${commandId}命令${hasCommand ? '存在' : '不存在'}`);

        if (!hasCommand) {
            this.logService.warn(`TemplateDownloaderContribution: ${commandId}命令不存在，无法执行`);
            return;
        }

        const templateInfo = this.templateData?.templateInfo;
        if (!templateInfo?.prompt) return;

        try {
            // 统一使用'ask'作为默认值
            const defaultMode = 'ask';

            await this.commandService.executeCommand(commandId, {
                mode: agentId || defaultMode,
                text: templateInfo.prompt
            });

            this.logService.info(`TemplateDownloaderContribution: 成功执行${commandId}命令`);
        } catch (cmdError) {
            this.logService.error(`TemplateDownloaderContribution: 执行${commandId}命令失败:`, cmdError);
        }
    }

    /**
     * 检查是否有模板需要下载
     */
    private async checkForTemplateDownload(): Promise<void> {
        try {
            this.logService.info('TemplateDownloaderContribution: 检查是否有模板需要下载');

            // 如果是远程环境，确保远程环境已准备好
            if (this.environmentService.remoteAuthority) {
                if (!await this.isRemoteEnvironmentReady('检查模板下载')) {
                    return;
                }
            }

            // 尝试从 URL 查询参数中获取模板信息（仅在本地环境中有效）
            if (!this.environmentService.remoteAuthority) {
                const urlParams = new URLSearchParams(window.location.search);
                const templateInfo = urlParams.get('templateInfo');

                // 清除location.search
                if (templateInfo) {
                    window.history.replaceState({}, document.title, window.location.pathname);
                }

                if (templateInfo) {
                    try {
                        // 尝试解析 JSON 字符串
                        const templateData = JSON.parse(decodeURIComponent(templateInfo));

                        // 检查模板类型是否与当前环境匹配
                        const isLocalTemplate = templateData?.type === 'local';
                        const isRemoteTemplate = templateData?.type === 'remote';

                        if ((isLocalTemplate && !this.environmentService.remoteAuthority) ||
                            (isRemoteTemplate && this.environmentService.remoteAuthority)) {
                            this.logService.info(`TemplateDownloaderContribution: 从URL参数获取到模板信息: ${JSON.stringify(templateData)}`);
                            await this.processTemplate(templateData);
                            return;
                        } else {
                            this.logService.info('TemplateDownloaderContribution: 模板类型与环境不匹配，忽略');
                        }
                    } catch (parseError) {
                        this.logService.error('TemplateDownloaderContribution: 解析模板信息失败:', parseError);
                    }
                }
            }

            // 如果URL中没有模板信息或在远程环境中，则尝试从服务中获取
            if (this.templateData) {
                this.logService.info(`TemplateDownloaderContribution: 从服务中获取到模板信息: ${JSON.stringify(this.templateData)}`);

                // 检查模板类型是否与当前环境匹配
                const isLocalTemplate = this.templateData?.type === 'local';
                const isRemoteTemplate = this.templateData?.type === 'remote';

                if ((isLocalTemplate && !this.environmentService.remoteAuthority) ||
                    (isRemoteTemplate && this.environmentService.remoteAuthority)) {
                    // 清除服务中的模板信息，避免重复下载
                    // this.newProjectService.clearTemplateDownloadInfo();
                    await this.processTemplate(this.templateData);
                } else {
                    this.logService.info('TemplateDownloaderContribution: 模板类型与环境不匹配，忽略');
                }
            } else {
                this.logService.info('TemplateDownloaderContribution: 没有找到模板下载信息');
            }
        } catch (error) {
            this.logService.error('TemplateDownloaderContribution: 检查模板下载失败:', error);
        } finally {
            // 无论成功与否，都清除模板下载信息
            // this.newProjectService.clearTemplateDownloadInfo();
        }
    }
}

// 注册贡献点
// 注意：虽然WorkbenchExtensions.Workbench和registerWorkbenchContribution已被标记为弃用，
// 但目前VS Code仍然使用这种方式注册工作台贡献点
Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(
    TemplateDownloaderContribution,
    LifecyclePhase.Restored
);
