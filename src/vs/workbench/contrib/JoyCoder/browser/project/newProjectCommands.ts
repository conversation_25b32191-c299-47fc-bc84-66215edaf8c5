/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { registerAction2, Action2 } from '../../../../../platform/actions/common/actions.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { <PERSON>u<PERSON>eg<PERSON>ry, MenuId } from '../../../../../platform/actions/common/actions.js';
import { localize } from '../../../../../nls.js';
import { IFileDialogService } from '../../../../../platform/dialogs/common/dialogs.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { DisposableStore } from '../../../../../base/common/lifecycle.js';
import { IAuxiliaryWindowService, AuxiliaryWindowMode } from '../../../../services/auxiliaryWindow/browser/auxiliaryWindowService.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { URI } from '../../../../../base/common/uri.js';
import { IWebviewService, WebviewContentPurpose } from '../../../webview/browser/webview.js';
import { process } from '../../../../../base/parts/sandbox/electron-sandbox/globals.js';
import { IHostService } from '../../../../services/host/browser/host.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../loginAction.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { INewProjectResult, INewProjectService, ISaveTemplateDownloadInfo } from '../../common/newProject.js';
import { INewProjectParams, IRes } from '../../common/newProject.js';
import { FileAccess } from '../../../../../base/common/network.js';
import { IWorkspaceTrustManagementService } from '../../../../../platform/workspace/common/workspaceTrust.js';
import { ICommandService } from '../../../../../platform/commands/common/commands.js';
import { IPathService } from '../../../../services/path/common/pathService.js';
import { IStorageService, StorageScope } from '../../../../../platform/storage/common/storage.js';

// 命令ID
export const NEW_PROJECT_COMMAND_ID = 'workbench.action.newProject';
export const CONNECT_REMOTESSH = 'clouddev.connectRemoteSSH';
export const CLOUDDEV_REFRESH = 'clouddev.refresh';
export const LOCAL_PROJECT_PATH = 'joycode.userProjectPath';

export const TEMPLATE_CLASS_CODE = {
    0: 'engineer',
    1: 'language',
}

export const TEMPLATE_TYPE = {
    0: 'remote',
    1: 'local',
}


// 注册新建项目命令
registerAction2(class NewProjectAction extends Action2 {
    // 静态变量，用于跟踪当前是否已经打开了新建项目窗口
    private static currentNewProjectWindow: { window: any, focus: () => void } | null = null;
    private static templateList: any[];


    private static async createProjectData(newProjectService: INewProjectService, data: any): Promise<IRes | null> {
        let params: INewProjectParams = {
            name: data.projectName,
            description: data.projectDescription,
            devMode: data.devMode,
        }
        if (data.templateInfo) {
            params.projectTemplateId = data.templateInfo.id;
            params.projectTemplateName = data.templateInfo.name;
            params.projectTemplateVersion = data.templateInfo.version ?? '1.0.0';
            params.projectTemplateUrl = data.templateInfo.downloadUrl;
            // const isLan = data.templateInfo.category === '语言类模版';
            if (data.templateInfo.componentVersions?.length) {
                const projectTemplateEnvs = [];
                for (const item of data.templateInfo.componentVersions) {
                    if (data.templateInfo.templateClassCode === TEMPLATE_CLASS_CODE[0]) {
                        projectTemplateEnvs.push({
                            name: item.componentName,
                            version: item.versionName
                        })
                    }
                    if (params.projectTemplateVersion === item.versionName && data.templateInfo.templateClassCode === TEMPLATE_CLASS_CODE[1]) {
                        projectTemplateEnvs.push({
                            name: item.componentName,
                            version: item.versionName
                        })
                    }
                }
                params.projectTemplateEnvs = projectTemplateEnvs;
            }
        }
        if (data.devMode === 2) {
            params.localLocation = data.projectLocation;
        }
        if (data?.templateInfo?.useDatabase) {
            params.databaseType = data.databaseMode; // 1:自动初始化共享库 2:选择已有数据库实例 3:自定义数据库配置
            if (data.databaseMode === 2 && data.databaseConfig) {
                params.databaseId = data.databaseConfig.instanceId;
            }
            if (data.databaseMode === 3 && data.databaseConfig) {
                const url = data.databaseConfig.url; // 数据库连接字符串
                if (url) {
                    // mysql--internet--cn-north-1--a5d4855414754ebf.rds.jdcloud.com:3306:my_test
                    // 以最后两个冒号为分隔，兼容数据库名中有冒号的情况
                    const lastColon = url.lastIndexOf(':');
                    const secondLastColon = url.lastIndexOf(':', lastColon - 1);
                    if (secondLastColon !== -1 && lastColon !== -1) {
                        params.databaseCustomHost = url.substring(0, secondLastColon);
                        params.databaseCustomPort = Number(url.substring(secondLastColon + 1, lastColon));
                        params.databaseCustomName = url.substring(lastColon + 1);
                    }
                }
                params.databaseCustomUsername = data.databaseConfig.user;
                params.databaseCustomPassword = data.databaseConfig.password;
            }
        }
        console.log(params, 'params')
        const res = await newProjectService.createProject(params);
        return res;

    }



    // 新增：抽离 createProjectAndMaybeOpenWindow 函数
    private static async createProjectAndMaybeOpenWindow(
        {
            message,
            hasWorkspace,
            fileService,
            newProjectService,
            notificationService,
            workspaceTrustManagementService,
            hostService,
            webview,
            disposables,
            auxiliaryWindow,
            commandService
        }: {
            message: any;
            hasWorkspace: boolean;
            fileService: IFileService;
            newProjectService: INewProjectService;
            notificationService: INotificationService;
            workspaceTrustManagementService: IWorkspaceTrustManagementService;
            hostService: IHostService;
            webview: any;
            disposables: DisposableStore;
            auxiliaryWindow: any;
            commandService: ICommandService;

        }
    ) {
        try {
            const { projectName, devMode = 0, projectLocation, templateInfo } = message.data;

            // 确保prompt透传
            const selectTempInfo = NewProjectAction.templateList.find(item => item.id === Number(templateInfo?.id));
            if (!templateInfo?.prompt && selectTempInfo?.prompt) templateInfo.prompt = selectTempInfo?.prompt || '';
            if (!templateInfo?.templateClassCode && selectTempInfo?.templateClassCode) templateInfo.templateClassCode = selectTempInfo.templateClassCode;
            let projectId = -1;
            let saveTemplateDownloadInfo: ISaveTemplateDownloadInfo = {
                type: 'local',
                url: templateInfo?.downloadUrl,
                name: projectName ?? templateInfo?.templateName,
                targetPath: projectLocation,
                projectInfo: null,
                templateInfo,
            };

            let project: any = null;

            // 先创建项目
            const res = await NewProjectAction.createProjectData(newProjectService, message.data);
            if (res?.code !== 200) {
                webview.postMessage({ type: 'error', message: res?.message });
                return;
            }
            projectId = res?.data?.id;
            saveTemplateDownloadInfo.projectInfo = res?.data;
            if (res?.data?.status === 'Pending' && res?.data?.id) {
                const infoRes = await NewProjectAction.getProjectInfo(res.data?.id, newProjectService, webview);
                if (infoRes?.code === 200) {
                    saveTemplateDownloadInfo.projectInfo = infoRes?.data || null;
                    project = {
                        ...infoRes?.data
                    }
                }
            }

            // 刷新列表
            try {
                commandService.executeCommand(CLOUDDEV_REFRESH);
            } catch (error) {
                console.log(error)
            }

            // 本地
            if (devMode === 2 && projectLocation) {
                const projectFolderPath = URI.file(projectLocation);
                if (!await fileService.exists(projectFolderPath)) {
                    await fileService.createFolder(projectFolderPath);
                }

                const windowOpenOptions = {
                    forceNewWindow: hasWorkspace,
                    forceReuseWindow: !hasWorkspace,
                };

                if (templateInfo && templateInfo.downloadUrl) {
                    await newProjectService.saveTemplateDownloadInfo(saveTemplateDownloadInfo);
                    notificationService.info(localize('templateWillDownload', "项目模板将在新窗口中下载"));
                }

                try {
                    if (workspaceTrustManagementService) {
                        await workspaceTrustManagementService.setUrisTrust([projectFolderPath], true);
                    }
                } catch (trustError) {
                    console.error('将项目目录添加到受信任列表时出错:', trustError);
                }

                // // 清理资源
                disposables.dispose();

                // 关闭窗口
                auxiliaryWindow.dispose();
                // 清除窗口引用
                (NewProjectAction as any).currentNewProjectWindow = null;
                const uriWithProjectId = projectFolderPath.with({ query: `projectId=${projectId}` });
                await hostService.openWindow([{ folderUri: uriWithProjectId }], windowOpenOptions);
            } else if (devMode === 1) {
                saveTemplateDownloadInfo.type = 'remote';
                const sshConfigRes = await newProjectService.getProjectSSHConfig(projectId);
                if (sshConfigRes?.code === 200) {
                    project = {
                        ...project,
                        sshConfig: {
                            ...sshConfigRes?.data
                        }
                    }
                    const remotePath = '/config/workspace';
                    const remoteHost = sshConfigRes?.data?.host;
                    const sshHostLabel = `${projectName}_${sshConfigRes?.data?.projectId}`;
                    const remoteAuthority = `ssh-remote+${sshHostLabel}`;
                    const uri = URI.parse(`vscode-remote://${remoteAuthority}${remotePath}`);
                    saveTemplateDownloadInfo.remoteHost = remoteHost
                    saveTemplateDownloadInfo.targetPath = remotePath.replace(/^~/, process.env.HOME || '/config') as string;
                    saveTemplateDownloadInfo.sshConfigRes = sshConfigRes;
                    saveTemplateDownloadInfo.sshHostLabel = sshHostLabel;
                    saveTemplateDownloadInfo.remotePath = remotePath;
                    // const windowOpenable = { folderUri: uri } as IWindowOpenable;
                    await newProjectService.saveTemplateDownloadInfo(saveTemplateDownloadInfo);

                    try {
                        if (workspaceTrustManagementService) {
                            await workspaceTrustManagementService.setUrisTrust([uri], true);
                        }
                    } catch (trustError) {
                        console.error('将远程项目目录添加到受信任列表时出错:', trustError);
                    }

                    try {
                        const timer3 = setTimeout(() => {
                            // // 清理资源
                            disposables.dispose();

                            // 关闭窗口
                            auxiliaryWindow.dispose();
                            // 清除窗口引用
                            (NewProjectAction as any).currentNewProjectWindow = null;
                            commandService.executeCommand(CONNECT_REMOTESSH, {
                                sshHost: sshConfigRes?.data?.host,
                                sshUser: sshConfigRes?.data?.user,
                                sshPort: sshConfigRes?.data?.port,
                                base64PrivateKey: sshConfigRes?.data?.privateKey,
                                sshHostLabel,
                                workingDir: remotePath,
                                forceNewWindow: hasWorkspace,
                            });
                            clearTimeout(timer3)
                        }, 1000)
                    } catch (error) {
                        console.warn(`命令 ${CONNECT_REMOTESSH} 不存在`);
                    }
                    // 触发扩展的指令
                    // vscode.commands.executeCommand('clouddev.connectRemoteSSH', {
                    //     sshHost: data.host,
                    //     sshUser: data.user,
                    //     sshPort: data.port,
                    //     base64PrivateKey: data.privateKey,
                    //     sshHostLabel: `${currentProject.name}_${currentProject.id}`,
                    //     workingDir: '/config/workspace',
                    //     forceNewWindow: result === messages.newWindow ? true : false,
                    // });



                    // await hostService.openWindow([windowOpenable], {
                    //     forceNewWindow: hasWorkspace,
                    //     forceReuseWindow: !hasWorkspace,
                    //     remoteAuthority: remoteAuthority
                    // });
                }

            }
        } catch (error) {
            console.log(error)
        }
    }

    // 将 handleCreateProject 提到类作用域内，run 方法外部
    private static async handleCreateProject(
        message: any,
        {
            notificationService,
            fileService,
            hostService,
            workspaceContextService,
            newProjectService,
            workspaceTrustManagementService,
            webview,
            hasWorkspace,
            disposables,
            auxiliaryWindow,
            commandService
        }: {
            notificationService: INotificationService,
            fileService: IFileService,
            hostService: IHostService,
            workspaceContextService: IWorkspaceContextService,
            newProjectService: INewProjectService,
            workspaceTrustManagementService: IWorkspaceTrustManagementService,
            webview: any,
            hasWorkspace: boolean,
            disposables: DisposableStore,
            auxiliaryWindow: any,
            commandService: ICommandService
        }
    ) {
        if (!message.data || typeof message.data !== 'object') {
            return;
        }

        try {
            await NewProjectAction.createProjectAndMaybeOpenWindow({
                message,
                hasWorkspace,
                fileService,
                newProjectService,
                notificationService,
                workspaceTrustManagementService,
                hostService,
                webview,
                disposables,
                auxiliaryWindow,
                commandService
            });
        } catch (error) {
            notificationService.error(localize('failedToOpenNewWindow', "Failed to open new window: {0}", error.message));
            // 恢复界面状态（如果出错）
            webview.postMessage({ type: 'loading', loading: false });
            // // 清理资源
            disposables.dispose();

            // 关闭窗口
            auxiliaryWindow.dispose();
            // 清除窗口引用
            (NewProjectAction as any).currentNewProjectWindow = null;
        }

    }

    //  如果创建返回的status 是Pending的话，需要轮询，直到status的值是running
    private static async getProjectInfo(id: number, newProjectService: INewProjectService, webview: any): Promise<IRes<INewProjectResult> | null> {
        const startTime = Date.now();
        const timeout = 60 * 1000; // 60秒超时

        while (Date.now() - startTime < timeout) {
            const result = await newProjectService.getProjectInfo(id);

            if (!result) {
                throw new Error('获取项目信息失败');
            }

            // 如果状态是running，直接返回结果
            if (result?.data?.status === 'Running') {
                return result;
            }

            // 如果状态不是Pending，说明出错了 Pending
            if (result?.data?.status !== 'Pending') {
                throw new Error(`项目状态异常: ${result?.data?.status}`);
            }

            // 等待1秒后继续轮询
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        webview.postMessage({ type: 'error', message: 'Timeout while retrieving project information' });
        webview.postMessage({ type: 'loading', loading: false });
        throw new Error('Timeout while retrieving project information');
    }

    private static async getUserHomePath(pathService: IPathService): Promise<string> {
        const userHomeUri = await pathService.userHome({ preferLocal: true });
        return userHomeUri?.fsPath || '';
    }

    private static saveUserPath(path: string, storageService: any) {
        if (!path || !storageService) return;
        storageService.store(LOCAL_PROJECT_PATH, path, StorageScope.APPLICATION);
    }

    private static getUserPath(storageService: any) {
        if (!storageService) return;
        const path = storageService.get(LOCAL_PROJECT_PATH, StorageScope.APPLICATION, '');
        return path;
    }

    constructor() {
        super({
            id: NEW_PROJECT_COMMAND_ID,
            title: {
                value: localize('newProject', "New Project"),
                original: 'New Project'
            },
            category: Categories.File,
            f1: true,
            // 添加前置条件：只有在已登录状态下才启用
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
        });
    }

    async run(accessor: ServicesAccessor): Promise<void> {
        // 获取服务
        const fileDialogService = accessor.get(IFileDialogService);
        const notificationService = accessor.get(INotificationService);
        const auxiliaryWindowService = accessor.get(IAuxiliaryWindowService);
        const fileService = accessor.get(IFileService);
        const webviewService = accessor.get(IWebviewService);
        const hostService = accessor.get(IHostService);
        const workspaceContextService = accessor.get(IWorkspaceContextService);
        const newProjectService = accessor.get(INewProjectService);
        const workspaceTrustManagementService = accessor.get(IWorkspaceTrustManagementService);
        const commandService = accessor.get(ICommandService);
        const pathService = accessor.get(IPathService);
        const storageService = accessor.get(IStorageService);

        // 如果已经存在新建项目窗口，则聚焦到该窗口并返回
        if (NewProjectAction.currentNewProjectWindow) {
            try {
                // 检查窗口是否仍然存在（可能已被关闭）
                if (NewProjectAction.currentNewProjectWindow.window &&
                    !NewProjectAction.currentNewProjectWindow.window.closed) {
                    // 聚焦到已存在的窗口
                    NewProjectAction.currentNewProjectWindow.focus();

                    // 通知用户窗口已经存在
                    notificationService.info(localize('projectWindowExists', "Project window is already open"));

                    return;
                } else {
                    // 窗口已关闭，重置引用
                    NewProjectAction.currentNewProjectWindow = null;
                }
            } catch (e) {
                // 如果访问窗口属性出错，说明窗口可能已关闭
                NewProjectAction.currentNewProjectWindow = null;
            }
        }

        // 检查当前窗口是否有工作目录
        const hasWorkspace = !!workspaceContextService.getWorkspace().folders.length;
        // const nativeHostService = accessor.get(INativeHostService);
        try {
            // 设置固定窗口大小
            const width = 750;
            const height = 600;

            // 获取当前窗口信息
            const mainWin = window;

            // 检测是否处于全屏模式
            // 使用VS Code内部的isFullscreen函数或检查document.fullscreenElement
            const isFullScreen = document.fullscreenElement !== null ||
                (mainWin.screen.width === mainWin.outerWidth &&
                    mainWin.screen.height === mainWin.outerHeight);

            let x, y;

            // 获取当前窗口的位置和尺寸
            const mainX = mainWin.screenX;
            const mainY = mainWin.screenY;
            const mainWidth = mainWin.outerWidth;
            const mainHeight = mainWin.outerHeight;

            // 水平居中 - 相对于当前窗口
            x = mainX + Math.floor((mainWidth - width) / 2);

            // 垂直位置计算
            if (isFullScreen) {
                // 全屏模式下，使用固定的垂直偏移量
                const verticalOffset = 150; // 固定偏移量，可以根据需要调整
                y = mainY + verticalOffset;
            } else {
                // 非全屏模式下，使用居中逻辑
                y = mainY + Math.floor((mainHeight - height) / 2);
            }

            // 创建一个新窗口，并设置初始位置和固定大小
            const auxiliaryWindow = await auxiliaryWindowService.open({
                mode: AuxiliaryWindowMode.Normal,
                bounds: {
                    width: width,
                    height,
                    x: x,
                    y: y
                },
                nativeTitlebar: true, // 使用原生标题栏，确保窗口可以拖动
                disableFullscreen: true // 禁用全屏功能，确保窗口保持固定大小
            });

            // 保存窗口引用
            NewProjectAction.currentNewProjectWindow = {
                window: auxiliaryWindow.window,
                focus: () => {
                    if (auxiliaryWindow.window) {
                        // 尝试使用多种方法确保窗口前置
                        try {
                            // 方法1: 使用标准focus方法
                            auxiliaryWindow.window.focus();

                            // 方法2: 尝试使用其他方法前置窗口
                            if (auxiliaryWindow.window.parent) {
                                auxiliaryWindow.window.parent.focus();
                            }

                            // 方法3: 尝试使用hostService前置窗口
                            hostService.focus(auxiliaryWindow.window, { force: true });

                            // 方法4: 尝试闪烁窗口以引起注意
                            const flashWindow = () => {
                                try {
                                    // 临时修改标题以引起注意
                                    const originalTitle = auxiliaryWindow.window.document.title;
                                    auxiliaryWindow.window.document.title = "● " + originalTitle;

                                    // 500毫秒后恢复原标题
                                    setTimeout(() => {
                                        auxiliaryWindow.window.document.title = originalTitle;
                                    }, 500);
                                } catch (e) {
                                    console.error('Error while flashing window title:', e);
                                }
                            };

                            // 闪烁两次
                            flashWindow();
                            setTimeout(flashWindow, 1000);
                        } catch (e) {
                            console.error('Error while trying to focus window:', e);
                        }
                    }
                }
            };

            // 设置窗口标题
            auxiliaryWindow.window.document.title = localize('newProject', "New Project");

            // 获取容器元素
            const container = auxiliaryWindow.container;

            // 创建webview
            const webview = webviewService.createWebviewElement({
                providedViewType: 'newProject',
                title: localize('newProject', "New Project"),
                options: {
                    purpose: WebviewContentPurpose.WebviewView,
                    enableFindWidget: false
                },
                contentOptions: {
                    allowScripts: true,
                    localResourceRoots: [URI.file(process.cwd())]
                },
                extension: undefined // 不需要扩展ID
            });

            // 使用FileAccess API获取正确的文件URI
            const htmlFileUri = FileAccess.asFileUri('vs/workbench/contrib/JoyCoder/browser/project/media/newProject.html');

            try {
                const htmlContent = await fileService.readFile(htmlFileUri);
                const htmlString = htmlContent.value.toString();

                // 由于我们不需要处理相对路径，直接使用原始HTML内容
                const processedHtml = htmlString;

                // 设置HTML内容
                webview.setHtml(processedHtml);

                // 将webview挂载到窗口
                webview.mountTo(container, auxiliaryWindow.window);

                const disposables = new DisposableStore();

                // 处理webview消息
                disposables.add(webview.onMessage(async event => {
                    const message = event.message;

                    // 确保消息是一个对象并且有type属性
                    if (typeof message !== 'object' || !message || !('type' in message)) {
                        return;
                    }

                    switch (message.type) {
                        case 'setLocalPath':
                            const localPath = message.localPath || '';
                            NewProjectAction.saveUserPath(localPath, storageService)
                            break;
                        case 'getUserPath':
                            let userPath = NewProjectAction.getUserPath(storageService);
                            console.log(userPath, 'userPath1')
                            userPath = userPath || await NewProjectAction.getUserHomePath(pathService);
                            console.log(userPath, 'userPath2')
                            webview.postMessage({ type: 'setUserPath', userPath });
                            break;
                        // 请求模板数据
                        case 'requestTemplates':
                            // 获取查询参数
                            const query = message.query || '';
                            console.log('查询参数:', query);
                            try {
                                // 根据查询参数获取模板列表
                                newProjectService.getTemplateList({ templateName: query })
                                    .then(templates => {
                                        // 发送模板数据到WebView
                                        webview.postMessage({
                                            type: 'setTemplates',
                                            templates: templates,
                                            query: query // 返回原始查询参数，方便WebView处理
                                        });
                                        NewProjectAction.templateList = templates;
                                    })
                                    .catch(error => {
                                        console.error('获取模板列表失败:', error);
                                    });
                            } catch (error) {
                                // 发送错误消息到WebView
                                webview.postMessage({
                                    type: 'templatesError',
                                    error: error.message || '获取模板列表失败',
                                    query: query
                                });
                            }
                            break;
                        // 获取数据库模版
                        case 'getProjectDatabaseList':
                            try {
                                newProjectService.getProjectDatabaseList()
                                    .then(list => {
                                        // 发送模板数据到WebView
                                        webview.postMessage({
                                            type: 'setDatabases',
                                            databases: list,
                                        });
                                    })
                                    .catch(error => {
                                        console.error('获取模板列表失败:', error);
                                    });
                            } catch (error) {

                            }
                            break;
                        // 选择文件
                        case 'browse':
                            const uri = await fileDialogService.showOpenDialog({
                                title: localize('selectProjectLocation', "Select Project Location"),
                                canSelectFiles: false,
                                canSelectFolders: true,
                                canSelectMany: false,
                                availableFileSystems: ['file'] // 只允许本地文件系统
                            });

                            if (uri && uri.length > 0) {
                                webview.postMessage({ type: 'setLocation', location: uri[0].fsPath });
                            }
                            break;

                        // 点击创建项目
                        case 'create':
                            await NewProjectAction.handleCreateProject(message, {
                                notificationService,
                                fileService,
                                hostService,
                                workspaceContextService,
                                newProjectService,
                                workspaceTrustManagementService,
                                webview,
                                hasWorkspace,
                                disposables,
                                auxiliaryWindow,
                                commandService
                            });
                            break;

                        case 'cancel':
                            // 清理资源
                            disposables.dispose();

                            // 关闭窗口
                            auxiliaryWindow.dispose();
                            // 清除窗口引用
                            NewProjectAction.currentNewProjectWindow = null;
                            break;

                        case 'error':
                            if (typeof message.message === 'string') {
                                notificationService.warn(message.message);
                            }
                            break;

                        case 'checkProjectExists':
                            if (typeof message.path === 'string') {
                                const exists = await newProjectService.checkProjectExists(message.path);
                                webview.postMessage({
                                    type: 'projectExistsResult',
                                    exists
                                });
                            }
                            break;
                    }
                }));
                // 窗口关闭时清理资源
                disposables.add(auxiliaryWindow.onUnload(() => {
                    disposables.dispose();
                    // 清除窗口引用
                    NewProjectAction.currentNewProjectWindow = null;
                }));
            } catch (error) {
                console.error('NewProjectAction: 读取HTML文件失败', error);
                notificationService.error(localize('htmlReadError', "Failed to read HTML file: {0}", error.message));

                // 关闭窗口
                auxiliaryWindow.dispose();
                // 清除窗口引用
                NewProjectAction.currentNewProjectWindow = null;
            }
        } catch (error) {
            console.error('NewProjectAction: 创建或显示窗口失败', error);
            notificationService.error(localize('newProjectError', "Failed to create new project window: {0}", error));
        }
    }
});

// 在File菜单中添加"New Project"选项
MenuRegistry.appendMenuItem(MenuId.MenubarNewMenu, {
    group: '1_new',
    command: {
        id: NEW_PROJECT_COMMAND_ID,
        title: localize({ key: 'miNewProject', comment: ['&& denotes a mnemonic'] }, "Project..."),
        // 添加前置条件：只有在已登录状态下才启用
        precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false)
    },
    // 不添加when条件，确保菜单项始终显示
    order: 1 // 放在"New Text File"之后
});
