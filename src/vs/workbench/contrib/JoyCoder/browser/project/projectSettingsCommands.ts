/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Action2, registerAction2 } from '../../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../../platform/instantiation/common/instantiation.js';
import { localize } from '../../../../../nls.js';
import { Categories } from '../../../../../platform/action/common/actionCommonCategories.js';
import { INotificationService } from '../../../../../platform/notification/common/notification.js';
import { ContextKeyExpr } from '../../../../../platform/contextkey/common/contextkey.js';
import { NOT_LOGGED_IN_KEY } from '../loginAction.js';
import { INewProjectService } from '../../common/newProject.js';
import { IAuxiliaryWindowService, AuxiliaryWindowMode } from '../../../../services/auxiliaryWindow/browser/auxiliaryWindowService.js';
import { IFileService } from '../../../../../platform/files/common/files.js';
import { URI } from '../../../../../base/common/uri.js';
import { IWebviewService, WebviewContentPurpose } from '../../../webview/browser/webview.js';
import { FileAccess } from '../../../../../base/common/network.js';
import { DisposableStore } from '../../../../../base/common/lifecycle.js';
import { IHostService } from '../../../../services/host/browser/host.js';
import { process } from '../../../../../base/parts/sandbox/electron-sandbox/globals.js';

// 命令ID
export const PROJECT_SETTINGS_COMMAND_ID = 'workbench.action.projectSettings';

// 项目设置数据接口
export interface IProjectSettingsData {
    id: number;
    serverInfo?: string;
    name?: string;
    devMode?: number;
    projectTemplateName?: string;
    databaseInfo?: string;
    databaseUsername?: string;
    databasePassword?: string;
    databaseHost?: string;
    databasePort?: string;
    description?: string;
}

// 项目设置命令类
export class ProjectSettingsAction extends Action2 {
    private static currentProjectSettingsWindow: { window: any, focus: () => void } | null = null;

    constructor() {
        super({
            id: PROJECT_SETTINGS_COMMAND_ID,
            title: {
                value: localize('projectSettings', "Project Settings"),
                original: 'Project Settings'
            },
            category: Categories.View,
            f1: true,
            precondition: ContextKeyExpr.equals(NOT_LOGGED_IN_KEY, false),
        });
    }

    async run(accessor: ServicesAccessor, projectData: IProjectSettingsData): Promise<void> {
        const notificationService = accessor.get(INotificationService);
        const newProjectService = accessor.get(INewProjectService);
        const auxiliaryWindowService = accessor.get(IAuxiliaryWindowService);
        const fileService = accessor.get(IFileService);
        const webviewService = accessor.get(IWebviewService);
        const hostService = accessor.get(IHostService);

        if (ProjectSettingsAction.currentProjectSettingsWindow) {
            try {
                if (ProjectSettingsAction.currentProjectSettingsWindow.window &&
                    !ProjectSettingsAction.currentProjectSettingsWindow.window.closed) {
                    ProjectSettingsAction.currentProjectSettingsWindow.focus();
                    notificationService.info(localize('projectSettingsWindowExists', "Project settings window is already open"));
                    return;
                } else {
                    ProjectSettingsAction.currentProjectSettingsWindow = null;
                }
            } catch (e) {
                ProjectSettingsAction.currentProjectSettingsWindow = null;
            }
        }

        try {
            const width = 750;
            const height = 650;
            const mainWin = window;
            const isFullScreen = document.fullscreenElement !== null ||
                (mainWin.screen.width === mainWin.outerWidth &&
                    mainWin.screen.height === mainWin.outerHeight);

            let x = mainWin.screenX + Math.floor((mainWin.outerWidth - width) / 2);
            let y = isFullScreen ? mainWin.screenY + 150 : mainWin.screenY + Math.floor((mainWin.outerHeight - height) / 2);

            const auxiliaryWindow = await auxiliaryWindowService.open({
                mode: AuxiliaryWindowMode.Normal,
                bounds: { width, height, x, y },
                nativeTitlebar: true,
                disableFullscreen: true
            });

            ProjectSettingsAction.currentProjectSettingsWindow = {
                window: auxiliaryWindow.window,
                focus: () => {
                    if (auxiliaryWindow.window) {
                        auxiliaryWindow.window.focus();
                        hostService.focus(auxiliaryWindow.window);
                    }
                }
            };

            auxiliaryWindow.window.document.title = localize('projectSettings', "Project Settings");

            const container = auxiliaryWindow.container;

            const webview = webviewService.createWebviewElement({
                providedViewType: 'projectSettings',
                title: localize('projectSettings', "Project Settings"),
                options: {
                    purpose: WebviewContentPurpose.WebviewView,
                    enableFindWidget: false
                },
                contentOptions: {
                    allowScripts: true,
                    localResourceRoots: [URI.file(process.cwd())]
                },
                extension: undefined
            });

            const htmlFileUri = FileAccess.asFileUri('vs/workbench/contrib/JoyCoder/browser/project/media/projectSettings.html');

            try {
                const htmlContent = await fileService.readFile(htmlFileUri);
                const htmlString = htmlContent.value.toString();
                const processedHtml = htmlString;

                webview.setHtml(processedHtml);
                webview.mountTo(container, auxiliaryWindow.window);

                const disposables = new DisposableStore();

                let databases = [];
                try {
                    databases = await newProjectService.getProjectDatabaseList();
                    webview.postMessage({
                        type: 'setDatabases',
                        databases,
                    });
                } catch (error) {
                    console.log('获取数据库列表出错')
                }

                // 发送初始项目数据到webview
                webview.postMessage({
                    type: 'setInitialData',
                    data: projectData
                });

                disposables.add(webview.onMessage(async event => {
                    const message = event.message;

                    if (typeof message !== 'object' || !message || !('type' in message)) {
                        return;
                    }

                    switch (message.type) {
                        case 'save':
                            try {
                                if (message.data) {
                                    console.log(message.data, 'message.data')
                                    const description = message.data.description.trim();
                                    const databaseConfig = message.data.databaseConfig;
                                    const params: any = {
                                        projectId: projectData.id,
                                        description,
                                        databaseType: Number(message.data.dbMode)
                                    }

                                    if (Number(message.data.dbMode) === 2) {
                                        params.databaseId = message.data.databaseConfig.id;
                                    }
                                    if (Number(message.data.dbMode) === 3 && databaseConfig) {
                                        params.databaseCustomHost = databaseConfig.url;
                                        const url = databaseConfig.url; // 数据库连接字符串
                                        if (url) {
                                            // mysql--internet--cn-north-1--a5d4855414754ebf.rds.jdcloud.com:330/:my_test
                                            // 以最后两个冒号为分隔，兼容数据库名中有冒号的情况
                                            const lastColon = url.lastIndexOf(':');
                                            const secondLastColon = url.lastIndexOf(':', lastColon - 1);
                                            if (secondLastColon !== -1 && lastColon !== -1) {
                                                params.databaseCustomHost = url.substring(0, secondLastColon);
                                                params.databaseCustomPort = Number(url.substring(secondLastColon + 1, lastColon));
                                                params.databaseCustomName = url.substring(lastColon + 1);
                                            }
                                            if (!params.databaseCustomName) {
                                                params.databaseCustomName = url.split('/')?.[1 || '']
                                            }
                                        }
                                        params.databaseCustomUsername = databaseConfig.username;
                                        params.databaseCustomPassword = databaseConfig.password;
                                    }
                                    const res = await newProjectService.setProjectInfo(params);
                                    if (res.code === 200) {
                                        notificationService.info(localize('projectSettingsSaved', "Project settings saved successfully"));
                                        auxiliaryWindow.dispose();
                                        ProjectSettingsAction.currentProjectSettingsWindow = null;
                                    } else {
                                        notificationService.error(localize('projectSettingsSaveError', "Failed to save project settings: {0}", res?.message));
                                    }
                                }
                            } catch (error) {
                                console.error('保存项目设置失败:', error);
                                notificationService.error(localize('projectSettingsSaveError', "Failed to save project settings: {0}", error.message));
                            }
                            break;

                        case 'cancel':
                            auxiliaryWindow.dispose();
                            ProjectSettingsAction.currentProjectSettingsWindow = null;
                            break;
                    }
                }));

                disposables.add(auxiliaryWindow.onUnload(() => {
                    disposables.dispose();
                    ProjectSettingsAction.currentProjectSettingsWindow = null;
                }));
            } catch (error) {
                console.error('ProjectSettingsAction: 读取HTML文件失败', error);
                notificationService.error(localize('htmlReadError', "Failed to read HTML file: {0}", error.message));
                auxiliaryWindow.dispose();
                ProjectSettingsAction.currentProjectSettingsWindow = null;
            }
        } catch (error) {
            console.error('ProjectSettingsAction: 创建或显示窗口失败', error);
            notificationService.error(localize('projectSettingsError', "Failed to create project settings window: {0}", error));
        }
    }
}

// 注册项目设置命令
registerAction2(ProjectSettingsAction);
