/**
 * Project Settings 自定义弹窗，100%还原 projectSettings.html 结构和事件，全部用 DOM API 动态创建
 */
export interface IProjectSettingsDialogOptions {
    data?: any;
    databases?: any[];
    onConfirm: (formData: any, close?: Function) => void;
    onCancel?: () => void;
}

// 完整 style 内容
const PROJECT_SETTINGS_STYLE = `
 :root {
            /* 这些变量将被VS Code主题覆盖 */
            /*--vscode-editor-background: #1e1e1e;*/
            --vscode-editor-background: #18181B;
            --vscode-editor-foreground: #d4d4d4;
            --vscode-input-background: #3c3c3c;
            /*--vscode-input-foreground: #cccccc;*/
            --vscode-input-foreground: #C0C4CC;
            /*--vscode-input-border: #3c3c3c;*/
            --vscode-input-border: #303035;
            --vscode-button-background: #2979ff;
            --vscode-button-foreground: #ffffff;
            --vscode-button-secondaryBackground: #3a3d41;
            --vscode-button-secondaryForeground: #ffffff;
            /*--vscode-list-hoverBackground: #2a2d2e;*/
            --vscode-list-hoverBackground: #202023;
            --vscode-list-activeSelectionBackground: #37373d;
            --vscode-dropdown-background: #3c3c3c;
            --vscode-dropdown-foreground: #f0f0f0;
            --vscode-dropdown-border: #3c3c3c;
            --vscode-checkbox-background: #3c3c3c;
            --vscode-checkbox-foreground: #f0f0f0;
            --vscode-checkbox-border: #3c3c3c;
			--vscode-focusBorder: rgba(255,255,255,0.4) !important;
			--vscode-descriptionForeground: rgba(255,255,255,0.5);
			--vscode-textLink-foreground: #247BFF;
            --vscode-errorForeground: #f44747;

			/** 自定义变量，不属于vscode **/
			--vscode-input-label: rgba(255,255,255,0.8);
			--vscode-input-label-placeholder: rgba(255,255,255,0.2);
			--vscode-button-primary2: #F3FBFB;

        }
		.icon {
			width: 1em;
			height: 1em;
			vertical-align: -0.15em;
			fill: currentColor;
			overflow: hidden;
		}

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--vscode-editor-background);
            z-index: 9999;
            transition: opacity 0.3s ease-out;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            opacity: 0;
            pointer-events: none;
        }

        .overlay.active {
            opacity: 1;
            pointer-events: all;
        }

        .loading-spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid var(--vscode-progressBar-background);
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .loading-text {
            color: var(--vscode-editor-foreground);
            font-size: 16px;
        }

        .project-settings-container {
            display: flex;
            flex-direction: column;
            padding: 24px;
            padding-top: 0;
            box-sizing: border-box;
            background-color:var(--vscode-editor-background, #2b2b2b);
            overflow-y: auto;
            box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
            border-radius: 6px;
        }

        .project-settings-form {
            display: flex;
            flex-direction: column;
            gap: 16px;
            flex: 1;
        }

        .form-group {
            display: flex;
			align-items: center;
            /*gap: 10px;*/
			/*margin-bottom: 16px;*/
        }

        .form-group>label {
            width: 80px;
			color: var(--vscode-input-label);
			font-size: 12px;
			font-weight: normal;
			line-height: 28px;
			margin-right: 8px;
			/*padding-top: 4px;*/
			flex-shrink: 0;
        }

        input[type="text"],
        input[type="password"],
        select,
        textarea {
            flex: 1;
			padding: 6px 12px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-input-foreground);
			border-radius: 4px;
            /*font-family: var(--vscode-font-family);*/
            /*font-size: var(--vscode-font-size);*/
			box-sizing: border-box;
			font-size: 12px;
			font-weight: normal;
        }

		input[type="text"]::placeholder,
		textarea::placeholder {
			color: var(--vscode-input-label-placeholder);
		}

        input[type="text"]:focus,
        input[type="password"]:focus,
        select:focus,
        textarea:focus {
            outline: 1px solid var(--vscode-focusBorder);
            border-color: var(--vscode-focusBorder);
        }

        textarea {
            resize: vertical;
            /*min-height: 80px;*/
			line-height: 18px;
        }

		.radio-group {
			display: flex;
			gap: 30px;
		}

		.radio-option {
			display: flex;
			align-items: center;
			height: 24px;
			margin-right: 40px;
		}

		.radio-option input[type="radio"] {
			margin: 0 5px 0 0;
			vertical-align: middle;
			position: relative;

			appearance: none;
			width: 12px;
			height: 12px;
			border: 1px solid rgba(255, 255, 255, 0.4);
			border-radius: 50%;
			/*margin: 0;*/
			cursor: pointer;
		}

		.radio-option input[type="radio"]:checked {
			background: #fff;
			border: none;
			position: relative;
		}

		.radio-option input[type="radio"]:checked::after {
			content: '';
			position: absolute;
			width: 4px;
			height: 4px;
			background: #000;  /* 黑色背景 */
			border-radius: 50%;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}

		.radio-option input[type="radio"]:focus {
			outline: none;
		}

		.radio-option label {
			display: inline-block;
			vertical-align: middle;
			line-height: 24px;
			padding-top: 0;
		}

        .project-settings-buttons {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
        }

        button {
            padding: 8px 16px;
            border: none;
            border-radius: 2px;
            cursor: pointer;
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
        }

        button.primary {
			background-color: var(--vscode-button-primary2);
			color: var(--vscode-editor-background);
        }

        /* button.primary:hover {
            background-color: var(--vscode-button-hoverBackground);
        } */

        button.secondary {
			background-color: var(--vscode-input-border);
			color: var(--vscode-input-label);
			white-space: nowrap;
        }

        /* button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        } */

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .server-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 28px;
            line-height: 28px;
            width: 100%;
        }

        .upgrade-button {
            color: var(--vscode-textLink-foreground);
            cursor: pointer;
            text-decoration: none;
        }

        .upgrade-button:hover {
            text-decoration: underline;
        }

        .db-section {
            margin-left: 88px;
            /*margin-top: 10px;*/
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        /*.db-label {*/
        /*    width: 100px;*/
        /*    text-align: right;*/
        /*}*/

        .db-user-control {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
        }

        .db-password-label {
            margin-left: 10px;
            width: auto;
        }

        .test-connection {
            display: flex;
            align-items: flex-start;
            /*gap: 10px;*/
            margin-top: -5px;
        }

        .success-icon {
            color: #4caf50;
        }

        .register-button {
            color: var(--vscode-textLink-foreground);
            cursor: pointer;
            text-decoration: none;
            margin-left: 10px;
        }

        .register-button:hover {
            text-decoration: underline;
        }

        /* 连接状态 */
        #testConnectionButton {
            vertical-align: top;
            margin-right: 0;
            font-size: 12px;
            line-height: 18px;
        }
        #connectionStatus{
            display: inline-flex !important;
            align-items: flex-start;
            gap: 4px;
            vertical-align: top;
            margin-left: 8px;
            font-size: 14px;
            line-height: 18px;
            max-width: 75%;
            word-break: break-all;
        }

        /* 在 style 标签最后添加 */
        .version-custom-select {
            width: 100%;
            min-width: 120px;
            /* max-width: 150px; */
            /* width: 120px !important; */
            display: inline-block;
            /* z-index: 200; */
        }
        .version-custom-select .template-version-input {
            cursor: pointer;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-input-foreground);
            border: 1px solid var(--vscode-input-border);
            border-radius: 2px;
            font-size: 12px;
            padding: 0 8px;
            height: 28px;
            width: 100%;
            min-width: 110px;
            /* max-width: 150px; */
            box-sizing: border-box;
            line-height: 28px;
        }
        .version-dropdown-panel {
            position: absolute;
            left: 0;
            right: 0;
            top: 100%;
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-input-border);
            border-radius: 6px;
            box-shadow: 0 4px 24px 0 rgba(0,0,0,1);
            z-index: 9999;
            max-height: 200px;
            overflow-y: auto;
            display: none;
            text-align: left;
        }
        .version-custom-select.active .version-dropdown-panel {
            display: block !important;
        }
        .version-dropdown-item {
            padding: 6px 10px;
            cursor: pointer;
            outline: none;
            display: block;
            color: var(--vscode-dropdown-foreground);
            background: transparent;
            transition: background 0.2s;
            word-break: break-all;
        }
        .version-dropdown-item:hover,
        .version-dropdown-item.selected {
            background: var(--vscode-list-hoverBackground);
            color: var(--vscode-dropdown-foreground);
        }
        /* 样式部分 */
        .version-custom-select {
            position: relative;
        }
        .version-dropdown-icon {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            pointer-events: none;
            display: flex;
            align-items: center;
            justify-content: center;

        }
        .version-dropdown-icon .icon-shang,.version-dropdown-icon .xia{
            color: var(--vscode-input-label-placeholder);
            font-size: 14px;
        }
        .version-custom-select .icon-shang { display: none; }
        .version-custom-select.active .icon-xia { display: none; }
        .version-custom-select.active .icon-shang { display: inline; }
`;

// 工具函数：创建带属性的 HTML 元素
function createEl<K extends keyof HTMLElementTagNameMap>(
    tag: K,
    attrs: { [key: string]: any } = {},
    ...children: (HTMLElement | string)[]
): HTMLElementTagNameMap[K] {
    const el = document.createElement(tag);
    for (const k in attrs) {
        if (k === "class") el.className = attrs[k];
        else if (k === "style" && typeof attrs[k] === "object") Object.assign(el.style, attrs[k]);
        else if (k.startsWith("on") && typeof attrs[k] === "function") (el as any)[k] = attrs[k];
        else el.setAttribute(k, attrs[k]);
    }
    for (const child of children) {
        if (typeof child === "string") el.appendChild(document.createTextNode(child));
        else if (child) el.appendChild(child);
    }
    return el;
}

// 工具函数：创建 SVG 元素
function createSvgEl(tag: string, attrs: { [key: string]: any } = {}, ...children: (SVGElement | string)[]): SVGElement {
    const el = document.createElementNS("http://www.w3.org/2000/svg", tag);
    for (const k in attrs) {
        if (k === "class") el.setAttribute("class", attrs[k]);
        else el.setAttribute(k, attrs[k]);
    }
    for (const child of children) {
        if (typeof child === "string") el.appendChild(document.createTextNode(child));
        else if (child) el.appendChild(child);
    }
    return el;
}

export function showCustomProjectSettingsDialog(options: IProjectSettingsDialogOptions) {

    // 生成select下拉
    function initSelectOption(selectDom: HTMLInputElement, list: { label: string; value: string }[] = []): HTMLInputElement | undefined {
        const versionSelectWrapper: any = selectDom?.parentElement;
        if (versionSelectWrapper) {
            console.log('1111')
            const versionDropdown = document.createElement('div');
            versionDropdown.className = 'dropdown-panel version-dropdown-panel';
            versionDropdown.style.zIndex = '200';

            list.forEach((ver, idx) => {
                const opt = document.createElement('div');
                opt.className = 'version-dropdown-item';
                opt.textContent = ver.label;
                opt.setAttribute('data-value', ver.value);
                if (idx === 0) opt.classList.add('selected');
                versionDropdown.appendChild(opt);

                opt.addEventListener('click', (e) => {
                    e.stopPropagation();
                    // 设置input
                    selectDom.value = ver.label;
                    selectDom.setAttribute('data-selected-value', ver.value);
                    // 高亮
                    Array.from(versionDropdown.children).forEach((child) => child.classList.remove('selected'));
                    opt.classList.add('selected');
                    versionDropdown.style.display = 'none';
                    versionSelectWrapper.classList.remove('active');
                    validateDbInputs && validateDbInputs();
                });
            });

            selectDom.addEventListener('click', (e) => {
                e.stopPropagation();
                // 只显示当前下拉
                document.querySelectorAll('.version-dropdown-panel').forEach((panel: any) => {
                    if (panel !== versionDropdown) panel.style.display = 'none';
                });
                versionDropdown.style.display = 'block';
                versionSelectWrapper.classList.add('active');
                // 只绑定一次全局点击关闭事件
                if (!versionSelectWrapper?._outsideClickListener) {
                    versionSelectWrapper._outsideClickListener = (event: MouseEvent) => {
                        if (!versionSelectWrapper.contains(event.target)) {
                            versionDropdown.style.display = 'none';
                            versionSelectWrapper.classList.remove('active');
                            document.removeEventListener('click', versionSelectWrapper._outsideClickListener, true);
                            versionSelectWrapper._outsideClickListener = null;
                        }
                    };
                    document.addEventListener('click', versionSelectWrapper._outsideClickListener, true);
                }
            });

            // 点击外部关闭
            document.addEventListener('click', (e: MouseEvent) => {
                if (!versionSelectWrapper.contains(e.target)) {
                    versionDropdown.style.display = 'none';
                    versionSelectWrapper.classList.remove('active');
                }
            });

            // 将新选项添加到父节点中
            versionSelectWrapper.appendChild(versionDropdown);
            // // 默认选中第一个
            // versionInput.value = list[0].value; // 默认第一个
            return selectDom;
        }
        return undefined;
    }


    // 遮罩
    const mask = createEl("div", {
        class: "joycoder-dialog-mask",
        style: {
            position: "fixed",
            top: "0", left: "0", width: "100vw", height: "100vh",
            // background: "rgba(0,0,0,0.35)",
            zIndex: "9999",
            display: "flex", alignItems: "center", justifyContent: "center"
        }
    });

    // 动态插入样式
    const styleTag = document.createElement("style");
    styleTag.textContent = PROJECT_SETTINGS_STYLE;
    mask.appendChild(styleTag);

    // Titlebar
    const titlebar = createEl("div", {
        class: "project-settings-titlebar",
        style: {
            display: "flex",
            alignItems: "center",
            height: "36px",
            borderTopLeftRadius: "8px",
            borderTopRightRadius: "8px",
            borderBottom: "1px solid #222",
            padding: "0 16px",
            userSelect: "none",
            position: "relative"
        }
    },
        // Title
        createEl("div", {
            style: {
                flex: "1",
                textAlign: "center",
                fontWeight: "bold",
                color: "#bbb",
                fontSize: "15px",
                letterSpacing: "0.5px"
            }
        },
            // 动态获取项目名
            `Settings: ${options?.data?.name || ""}`
        )
    );

    // 主容器
    const container = createEl("div", { class: "project-settings-container", style: { position: "relative", zIndex: 10000 } });

    // 正确插入 titlebar
    container.appendChild(titlebar);

    // 表单
    const form = createEl("div", { class: "project-settings-form" });

    // 1. Server Info
    const serverInfoSpan = createEl("span", { id: "serverInfo" });
    form.appendChild(
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "serverInfo" }, "Server Info:"),
            createEl("div", { class: "server-info" }, serverInfoSpan)
        )
    );

    // 2. Project Name
    const projectNameSpan = createEl("span", { id: "projectName" });
    form.appendChild(
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "projectName" }, "Project Name:"),
            createEl("div", { class: "server-info" }, projectNameSpan)
        )
    );

    // 3. Dev Mode
    const devModeSpan = createEl("span", { id: "devMode" });
    form.appendChild(
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "devMode" }, "Dev Mode:"),
            createEl("div", { class: "server-info" }, devModeSpan)
        )
    );

    // 4. Template
    const templateSpan = createEl("span", { id: "template" });
    form.appendChild(
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "template" }, "Templates:"),
            createEl("div", { class: "server-info" }, templateSpan)
        )
    );

    // 5. Database Mode (radio)
    const dbAuto = createEl("input", { type: "radio", id: "dbAuto", name: "databaseMode", value: 0 });
    const dbAutoLabel = createEl("label", { htmlFor: "dbAuto", for: "dbAuto" }, "No Database");
    const dbExisting = createEl("input", { type: "radio", id: "dbExisting", name: "databaseMode", value: 2 });
    const dbExistingLabel = createEl("label", { htmlFor: "dbExisting", for: "dbExisting" }, "Select Existing");
    const dbCustom = createEl("input", { type: "radio", id: "dbCustom", name: "databaseMode", value: 3, });
    const dbCustomLabel = createEl("label", { htmlFor: "dbCustom", for: "dbCustom" }, "Custom");
    form.appendChild(
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "databaseMode" }, "Database:"),
            createEl("div", { class: "radio-group" },
                createEl("div", { class: "radio-option" }, dbAuto, dbAutoLabel),
                createEl("div", { class: "radio-option" }, dbExisting, dbExistingLabel),
                createEl("div", { class: "radio-option" }, dbCustom, dbCustomLabel)
            )
        )
    );

    // 6. db-section
    // dbType 下拉
    const dbTypeInput = createEl("input", { id: "dbType", type: "text", class: "template-version-input template-input", value: 'mysql', readonly: true });
    dbTypeInput.textContent = 'MySQL';
    const iconXia = createSvgEl("svg", { class: "icon-xia", viewBox: "0 0 1024 1024", width: "14", height: "14" },
        createSvgEl("path", { d: "M521.984 659.52a12.8 12.8 0 0 1-19.968 0L272.64 372.8a12.8 12.8 0 0 1 9.984-20.8h458.752a12.8 12.8 0 0 1 9.984 20.8l-229.376 286.72z", fill: "var(--vscode-input-label-placeholder)" })
    );
    const iconShang = createSvgEl("svg", { class: "icon-shang", viewBox: "0 0 1024 1024", width: "14", height: "14" },
        createSvgEl("path", { d: "M521.984 364.48a12.8 12.8 0 0 0-19.968 0l-229.376 286.72a12.8 12.8 0 0 0 9.984 20.8h458.752a12.8 12.8 0 0 0 9.984-20.8l-229.376-286.72z", fill: "var(--vscode-input-label-placeholder)" })
    );
    const versionDropdownIcon = createEl("span", { class: "version-dropdown-icon" }) as HTMLSpanElement;
    versionDropdownIcon.appendChild(iconXia);
    versionDropdownIcon.appendChild(iconShang);
    const versionCustomSelect = createEl("div", { class: "custom-select version-custom-select" }, dbTypeInput, versionDropdownIcon);

    // dbUrl
    const dbUrlInput = createEl("input", { type: "text", id: "dbUrl", style: "width:100%;" });
    // dbUser/dbPassword
    const dbUserInput = createEl("input", { type: "text", id: "dbUser", style: "flex:1;" });
    const dbPasswordLabel = createEl("label", { htmlFor: "dbPassword", class: "db-password-label db-label" }, "Password:");
    const dbPasswordInput = createEl("input", { type: "password", id: "dbPassword", style: "flex:1;" });

    const dbSection = createEl("div", { class: "db-section", id: "dbCustomSection" },
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "dbType", class: "db-label" }, "Type:"),
            createEl("div", { style: "width:100%;" }, versionCustomSelect)
        ),
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "dbUrl", class: "db-label" }, "URL:"),
            createEl("div", { style: "width:100%;" }, dbUrlInput)
        ),
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "dbUser", class: "db-label" }, "User Name:"),
            createEl("div", { class: "db-user-control" }, dbUserInput, dbPasswordLabel, dbPasswordInput)
        )
    );
    form.appendChild(dbSection);

    // xx 下拉
    const dbExistingSelectInput = createEl("input", { id: "dbExistingSelect", type: "text", class: "template-version-input template-input", value: '', readonly: true });
    const dbExistingSelectDropdownIcon = createEl("span", { class: "version-dropdown-icon" }) as HTMLSpanElement;
    dbExistingSelectDropdownIcon.appendChild(iconXia);
    dbExistingSelectDropdownIcon.appendChild(iconShang);
    const dbExistingSelectCustomSelect = createEl("div", { class: "custom-select version-custom-select" }, dbExistingSelectInput, dbExistingSelectDropdownIcon);

    const dbExistingSection = createEl("div", { class: "db-section", id: "dbExistingSection" },
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "dbExistingSelect", style: "width:0" }),
            createEl("div", { style: "width:100%;" }, dbExistingSelectCustomSelect)
        )
    );
    form.appendChild(dbExistingSection);

    dbAuto.addEventListener('change', handleDbModeChange);
    dbExisting.addEventListener('change', handleDbModeChange);
    dbCustom.addEventListener('change', handleDbModeChange);

    function handleDbModeChange() {
        if (dbCustom.checked) {
            dbTypeInput.value = 'mysql';
            dbSection.style.display = 'flex';
            dbExistingSection.style.display = 'none';
        } else if (dbExisting.checked) {
            dbSection.style.display = 'none';
            dbExistingSection.style.display = 'flex';
        } else {
            dbSection.style.display = 'none';
            dbExistingSection.style.display = 'none';
        }
    }
    // 初始化一次
    handleDbModeChange()

    // --- Custom/Select Existing 数据库输入校验与红框、Save 禁用 ---
    function validateDbInputs() {
        // 先全部清除
        dbTypeInput.style.border = '';
        dbUrlInput.style.border = '';
        dbUserInput.style.border = '';
        dbPasswordInput.style.border = '';
        const dbExistingSelect = document.getElementById('dbExistingSelect') as HTMLInputElement | null;
        if (dbExistingSelect) dbExistingSelect.style.border = '';

        let hasEmpty = false;

        if (dbCustom.checked) {
            // Custom 模式校验
            if (!dbTypeInput.value) {
                dbTypeInput.style.border = '1px solid var(--vscode-errorForeground)';
                hasEmpty = true;
            }
            if (!dbUrlInput.value.trim()) {
                dbUrlInput.style.border = '1px solid var(--vscode-errorForeground)';
                hasEmpty = true;
            }
            if (!dbUserInput.value.trim()) {
                dbUserInput.style.border = '1px solid var(--vscode-errorForeground)';
                hasEmpty = true;
            }
            if (!dbPasswordInput.value.trim()) {
                dbPasswordInput.style.border = '1px solid var(--vscode-errorForeground)';
                hasEmpty = true;
            }
        } else if (dbExisting.checked) {
            // Select Existing 校验
            const dbExistingSelect = document.getElementById('dbExistingSelect') as HTMLInputElement | null;
            if (dbExistingSelect && !dbExistingSelect.value) {
                dbExistingSelect.style.border = '1px solid var(--vscode-errorForeground)';
                hasEmpty = true;
            }
        }
        saveButton.disabled = hasEmpty;
    }

    // 监听 Custom 选项和输入框变化
    dbCustom.addEventListener('change', validateDbInputs);
    dbTypeInput.addEventListener('input', validateDbInputs);
    dbUrlInput.addEventListener('input', validateDbInputs);
    dbUserInput.addEventListener('input', validateDbInputs);
    dbPasswordInput.addEventListener('input', validateDbInputs);
    dbExistingSelectInput.addEventListener('change', validateDbInputs);

    // 监听 Select Existing 相关
    dbExisting.addEventListener('change', validateDbInputs);
    // 其它 radio 切换时也要校验
    dbAuto.addEventListener('change', validateDbInputs);

    // 初始校验
    setTimeout(validateDbInputs, 0);

    // 7. Domain
    const domainSpan = createEl("span", { id: "domain" });
    form.appendChild(
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "domain" }, "Domain:"),
            createEl("div", { class: "server-info" }, domainSpan)
        )
    );

    // 8. Export Ports
    const exportPortsSpan = createEl("span", { id: "exportPorts" });
    form.appendChild(
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "exportPorts" }, "Export Ports:"),
            createEl("div", { class: "server-info" }, exportPortsSpan)
        )
    );

    // 9. Description
    const descriptionTextarea = createEl("textarea", { id: "description", rows: "2" });
    form.appendChild(
        createEl("div", { class: "form-group" },
            createEl("label", { htmlFor: "description" }, "Description:"),
            descriptionTextarea
        )
    );

    // 10. 按钮区
    const cancelButton = createEl("button", { class: "secondary", id: "cancelButton", type: "button" }, "Cancel");
    const saveButton = createEl("button", { class: "primary", id: "saveButton", type: "button" }, "Save");
    const btnDiv = createEl("div", { class: "project-settings-buttons" }, cancelButton, saveButton);
    form.appendChild(btnDiv);

    // 组装
    container.appendChild(form);
    mask.appendChild(container);

    // 关闭弹窗
    function closeDialog() {
        document.body.removeChild(mask);
    }

    // 事件绑定
    cancelButton.onclick = () => {
        closeDialog();
        options.onCancel?.();
    };
    saveButton.onclick = () => {
        // 收集表单数据
        const description = descriptionTextarea.value.trim();
        const databaseConfig = {
            type: dbTypeInput.value,
            url: dbUrlInput.value.trim(),
            username: dbUserInput.value.trim(),
            password: dbPasswordInput.value.trim()
        };
        const databaseType = dbAuto.checked ? 0 : dbExisting.checked ? 2 : dbCustom.checked ? 3 : 0;
        let params: any = {
            databaseType,
            projectId: options.data.projectId,
            description: description,
        }
        console.log('paramsparamsparams', params)
        if (databaseType === 2) {
            console.log(dbExistingSection, 'dbExistingSection', document.getElementById('dbExistingSelect'))
            const databaseId = (document.getElementById('dbExistingSelect') as HTMLSelectElement)?.getAttribute('data-selected-value');
            databaseId && (params.databaseId = Number(databaseId))
        } else if (databaseType === 3) {
            params.databaseCustomHost = databaseConfig.url;
            const url = databaseConfig.url; // 数据库连接字符串
            if (url) {
                // mysql--internet--cn-north-1--a5d4855414754ebf.rds.jdcloud.com:330/:my_test
                // 以最后两个冒号为分隔，兼容数据库名中有冒号的情况
                const lastColon = url.lastIndexOf(':');
                const secondLastColon = url.lastIndexOf(':', lastColon - 1);
                if (secondLastColon !== -1 && lastColon !== -1) {
                    params.databaseCustomHost = url.substring(0, secondLastColon);
                    params.databaseCustomPort = Number(url.substring(secondLastColon + 1, lastColon));
                    params.databaseCustomName = url.substring(lastColon + 1);
                }
                if (!params.databaseCustomName) {
                    params.databaseCustomName = url.split('/')?.[1 || '']
                }
            }
            params.databaseCustomUsername = databaseConfig.username;
            params.databaseCustomPassword = databaseConfig.password;
        }
        options.onConfirm(params, closeDialog);
    };

    // 数据回填
    const { data } = options;
    // 初始化下拉选项
    const databases = options?.databases ?? [];
    const list = databases.map(m => {
        return {
            value: m.id,
            label: `${m.internalEndpoint}:${m.port}/${m.name}`
        }
    });
    initSelectOption(dbExistingSelectInput, list);
    if (data) {
        serverInfoSpan.textContent = data.serverInfo || '-';
        if (data.name) projectNameSpan.textContent = data.name;
        if (data.devMode) devModeSpan.textContent = data.devMode === 1 ? 'Remote' : data.devMode === 2 ? 'Local' : '';
        templateSpan.textContent = data.projectTemplateName || '-';
        // if (data.databaseType) dbTypeInput.value = data.databaseType === 1 ? 'mysql' : '';
        if (data.databaseType === 0) {
            dbAuto.checked = true;
        } else if (data.databaseType === 1 || data.databaseType === 2) {
            dbExisting.checked = true;
            const selectedDb = list.find(f => f.label === data.databaseInfo);
            dbExistingSelectInput.value = selectedDb?.label || '';
            dbExistingSelectInput?.setAttribute('data-selected-value', selectedDb?.value);
        } else if (data.databaseType === 3) {
            dbCustom.checked = true;
        } else {
            dbAuto.checked = true;
        }
        handleDbModeChange()
        if (data.databaseInfo) dbUrlInput.value = data.databaseInfo;
        if (data.databaseUsername) dbUserInput.value = data.databaseUsername;
        if (data.databasePassword) dbPasswordInput.value = data.databasePassword;
        domainSpan.textContent = data.url || '-';
        exportPortsSpan.textContent = data.port || '-';
        if (data.description) descriptionTextarea.value = data.description;
    }

    document.body.appendChild(mask);


}
