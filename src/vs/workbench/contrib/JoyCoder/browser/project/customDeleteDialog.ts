/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { localize } from '../../../../../nls.js';
import { ILayoutService } from '../../../../../platform/layout/browser/layoutService.js';
import { ILogService } from '../../../../../platform/log/common/log.js';
import { Dialog } from '../../../../../base/browser/ui/dialog/dialog.js';
import { DisposableStore } from '../../../../../base/common/lifecycle.js';
import { StandardKeyboardEvent } from '../../../../../base/browser/keyboardEvent.js';
import { EventHelper } from '../../../../../base/browser/dom.js';
import { IKeybindingService } from '../../../../../platform/keybinding/common/keybinding.js';
import { ThemeIcon } from '../../../../../base/common/themables.js';
// import { Codicon } from '../../../../../base/common/codicons.js';
import { defaultButtonStyles, defaultCheckboxStyles, defaultDialogStyles, defaultInputBoxStyles } from '../../../../../platform/theme/browser/defaultStyles.js';
import { ResultKind } from '../../../../../platform/keybinding/common/keybindingResolver.js';
// import { $ } from '../../../../../base/browser/dom.js';
import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../../platform/instantiation/common/extensions.js';

/**
 * 自定义删除对话框选项接口
 */
export interface IDeleteDialogOptions {
  /**
   * 项目名称
   */
  projectName: string;

  /**
   * 项目路径（可选）
   */
  projectPath?: string;

  /**
   * 是否为远程项目
   */
  isRemote?: boolean;

  /**
   * 对话框标题
   */
  title?: string;

  /**
   * 对话框消息
   */
  message?: string;

  /**
   * 对话框图标
   */
  icon?: ThemeIcon;

  /**
   * 是否禁用关闭按钮
   */
  disableCloseAction?: boolean;

  /**
   * 确认按钮文本
   */
  confirmButtonLabel?: string;

  /**
   * 取消按钮文本
   */
  cancelButtonLabel?: string;

  /**
   * 复选框标签
   */
  checkboxLabel?: string;

  /**
   * 复选框默认状态
   */
  checkboxChecked?: boolean;
}

/**
 * 删除对话框结果接口
 */
export interface IDeleteDialogResult {
  /**
   * 是否确认删除
   */
  confirmed: boolean;

  /**
   * 是否删除本地文件
   */
  deleteLocalFiles?: boolean;
}

/**
 * 自定义删除对话框服务接口
 */
export const ICustomDeleteDialogService = createDecorator<ICustomDeleteDialogService>('customDeleteDialogService');

export interface ICustomDeleteDialogService {
  readonly _serviceBrand: undefined;

  /**
   * 显示删除项目确认对话框
   * @param options 对话框选项
   * @returns 对话框结果的Promise
   */
  showDeleteDialog(options: IDeleteDialogOptions): Promise<IDeleteDialogResult>;
}

/**
 * 自定义删除对话框服务实现
 */
export class CustomDeleteDialogService implements ICustomDeleteDialogService {
  readonly _serviceBrand: undefined;

  private static readonly ALLOWABLE_COMMANDS = [
    'copy',
    'cut',
    'editor.action.selectAll',
    'editor.action.clipboardCopyAction',
    'editor.action.clipboardCutAction',
    'editor.action.clipboardPasteAction'
  ];

  constructor(
    @ILogService private readonly logService: ILogService,
    @ILayoutService private readonly layoutService: ILayoutService,
    @IKeybindingService private readonly keybindingService: IKeybindingService
  ) { }

  private static readonly DIALOG_STYLE_ID = 'joycoder-delete-dialog-style';
  private static readonly DIALOG_CUSTOM_STYLE = `
    .delete-project-dialog-container.joycoder-delete-dialog {
      border-radius: 16px !important;
      background: var(--vscode-editor-background) !important;
      box-shadow: 0 8px 32px rgba(0,0,0,0.25);
      color: var(--vscode-editor-foreground);
      min-width: 400px;
      padding: 32px 28px 24px 28px;
    }
    .delete-dialog-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
    }
    .delete-dialog-icon {
      width: 32px;
      height: 32px;
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .delete-dialog-title {
      font-size: 20px;
      font-weight: bold;
      color: var(--vscode-editor-foreground);
    }
    .delete-dialog-message {
      font-size: 15px;
      color: var(--vscode-editor-foreground);
      margin-bottom: 18px;
      line-height: 1.6;
    }
    .delete-dialog-message b {
      color: var(--vscode-inputValidation-errorBorder, #F85149);
      font-weight: bold;
    }
    .delete-dialog-checkbox-spacer {
      height: 16px;
    }
    .monaco-dialog-box .monaco-dialog-buttons {
      justify-content: flex-end !important;
    }
    .monaco-dialog-box .monaco-dialog-buttons .monaco-button {
      border-radius: 8px;
      min-width: 96px;
      font-size: 15px;
    }
    .monaco-dialog-box .monaco-dialog-buttons .monaco-button.monaco-text-button {
      background: var(--vscode-button-secondaryBackground);
      color: var(--vscode-editor-foreground);
      border: none;
    }
    .monaco-dialog-box .monaco-dialog-buttons .monaco-button.monaco-text-button:last-child {
      background: var(--vscode-editor-foreground);
      color: var(--vscode-inputValidation-errorBorder, #F85149);
      font-weight: bold;
    }
    `;

  private static injectDialogStyle() {
    if (!document.getElementById(CustomDeleteDialogService.DIALOG_STYLE_ID)) {
      const style = document.createElement('style');
      style.id = CustomDeleteDialogService.DIALOG_STYLE_ID;
      style.textContent = CustomDeleteDialogService.DIALOG_CUSTOM_STYLE;
      document.head.appendChild(style);
    }
  }

  private static removeDialogStyle() {
    const style = document.getElementById(CustomDeleteDialogService.DIALOG_STYLE_ID);
    if (style) style.remove();
  }

  /**
   * 显示删除项目确认对话框
   * @param options 对话框选项
   * @returns 对话框结果的Promise
   */
  async showDeleteDialog(options: IDeleteDialogOptions): Promise<IDeleteDialogResult> {
    this.logService.trace('CustomDeleteDialogService#showDeleteDialog', options.projectName);

    const dialogDisposables = new DisposableStore();

    // 设置默认值
    // const title = options.title || localize('deleteProject', "Delete Project");
    const message = options.message || localize('deleteProjectConfirm', "Delete Project");
    const confirmButtonLabel = options.confirmButtonLabel || localize('deleteButton', "Delete");
    const cancelButtonLabel = options.cancelButtonLabel || localize('cancelButton', "Cancel");
    const checkboxLabel = options.checkboxLabel || localize('deleteLocalFiles', "Also delete local files from disk");
    const checkboxChecked = options.checkboxChecked !== undefined ? options.checkboxChecked : false;
    // const icon = options.icon || { id: Codicon.trash.id }; // 移除默认垃圾桶icon
    const disableCloseAction = true; // 强制去掉右上角关闭按钮

    // 创建自定义渲染函数
    const renderBody = (container: HTMLElement) => {
      container.classList.add('delete-project-dialog-container', 'joycoder-delete-dialog');
      // 头部：图标+标题
      const header = container.appendChild(document.createElement('div'));
      header.className = 'delete-dialog-header';
      const icon = header.appendChild(document.createElement('div'));
      icon.className = 'delete-dialog-icon';
      // 原生 SVG 创建
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('width', '32');
      svg.setAttribute('height', '32');
      svg.setAttribute('viewBox', '0 0 1024 1024');
      svg.setAttribute('fill', 'none');
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', '512');
      circle.setAttribute('cy', '512');
      circle.setAttribute('r', '486.4');
      circle.setAttribute('fill', 'var(--vscode-inputValidation-errorBackground)');
      svg.appendChild(circle);
      const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
      path.setAttribute('d', 'M512 25.6A486.4 486.4 0 0 1 998.4 512 486.4 486.4 0 0 1 512 998.4 486.4 486.4 0 1 1 512 25.6zM512 704a51.2 51.2 0 1 0 0 102.4z m-38.4-480V640h76.8V224H473.6z');
      path.setAttribute('fill', 'var(--vscode-inputValidation-errorBorder, #F85149)');
      svg.appendChild(path);
      icon.appendChild(svg);
      const title = header.appendChild(document.createElement('div'));
      title.className = 'delete-dialog-title';
      title.textContent = options.title || 'Delete Project';
      // 说明文字
      const message = container.appendChild(document.createElement('div'));
      message.className = 'delete-dialog-message';
      // 原生拼接说明内容，含高亮项目名
      const msg1 = document.createTextNode('Are you sure delete project ');
      const b = document.createElement('b');
      b.textContent = options.projectName;
      const msg2 = document.createTextNode('?');
      const br = document.createElement('br');
      const msg3 = document.createTextNode('This action can not be undone.');
      message.appendChild(msg1);
      message.appendChild(b);
      message.appendChild(msg2);
      message.appendChild(br);
      message.appendChild(msg3);
      // 复选框区域（原有复选框会自动渲染在 Dialog 里，不需要手动插入）
      const checkboxSpacer = container.appendChild(document.createElement('div'));
      checkboxSpacer.className = 'delete-dialog-checkbox-spacer';
    };

    // 注入样式
    CustomDeleteDialogService.injectDialogStyle();

    // 创建对话框
    const dialog = new Dialog(
      this.layoutService.activeContainer,
      message,
      [confirmButtonLabel, cancelButtonLabel],
      {
        detail: localize('deleteProjectDetail', "Are you sure you want to delete project \"{0}\"?", options.projectName),
        cancelId: 1,
        type: 'none', // 不显示默认icon
        keyEventProcessor: (event: StandardKeyboardEvent) => {
          const resolved = this.keybindingService.softDispatch(event, this.layoutService.activeContainer);
          if (resolved.kind === ResultKind.KbFound && resolved.commandId) {
            if (CustomDeleteDialogService.ALLOWABLE_COMMANDS.indexOf(resolved.commandId) === -1) {
              EventHelper.stop(event, true);
            }
          }
        },
        renderBody,
        // 不传icon参数
        disableCloseAction,
        checkboxLabel,
        checkboxChecked,
        buttonStyles: defaultButtonStyles,
        checkboxStyles: defaultCheckboxStyles,
        inputBoxStyles: defaultInputBoxStyles,
        dialogStyles: defaultDialogStyles
      }
    );

    dialogDisposables.add(dialog);

    // 显示对话框并获取结果
    const result = await dialog.show();
    dialogDisposables.dispose();

    // 移除样式
    CustomDeleteDialogService.removeDialogStyle();

    // 返回结果
    return {
      confirmed: result.button === 0,
      deleteLocalFiles: result.checkboxChecked
    };
  }
}

registerSingleton(ICustomDeleteDialogService, CustomDeleteDialogService, InstantiationType.Delayed);

export interface ICustomDeleteDialogOptions {
  projectName: string;
  isRemote?: boolean;
  onConfirm: (deleteLocalFiles: boolean) => void;
  onCancel?: () => void;
}

export function showCustomDeleteDialog(options: ICustomDeleteDialogOptions) {
  // 1. 创建遮罩
  const mask = document.createElement('div');
  mask.className = 'joycoder-dialog-mask';

  // 2. 创建弹窗
  const dialog = document.createElement('div');
  dialog.className = 'joycoder-dialog';

  // 3. 弹窗内容（优化布局）
  // 主体区域：flex，左icon，右内容
  const main = document.createElement('div');
  main.className = 'joycoder-dialog-main';
  // 左侧icon区
  const iconWrap = document.createElement('div');
  iconWrap.className = 'joycoder-dialog-icon-wrap';
  const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
  svg.setAttribute('width', '23');
  svg.setAttribute('height', '23');
  svg.setAttribute('viewBox', '0 0 1024 1024');
  svg.setAttribute('fill', 'none');
  const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
  circle.setAttribute('cx', '512');
  circle.setAttribute('cy', '512');
  circle.setAttribute('r', '486.4');
  circle.setAttribute('fill', '#fff3f0');
  svg.appendChild(circle);
  const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
  path.setAttribute('d', 'M512 25.6A486.4 486.4 0 0 1 998.4 512 486.4 486.4 0 0 1 512 998.4 486.4 486.4 0 1 1 512 25.6zM512 704a51.2 51.2 0 1 0 0 102.4 51.2 51.2 0 0 0 0-102.4z m-38.4-480V640h76.8V224H473.6z');
  path.setAttribute('fill', '#F85149');
  svg.appendChild(path);
  iconWrap.appendChild(svg);
  main.appendChild(iconWrap);
  // 右侧内容区
  const contentWrap = document.createElement('div');
  contentWrap.className = 'joycoder-dialog-content-wrap';
  // 标题
  const title = document.createElement('div');
  title.className = 'joycoder-dialog-title';
  title.textContent = 'Delete Project';
  contentWrap.appendChild(title);
  // 正文内容
  const message = document.createElement('div');
  message.className = 'joycoder-dialog-message';
  const msg1 = document.createTextNode('Are you sure delete project ');
  // const b = document.createElement('b');
  // b.textContent = options.projectName;
  const projectName = document.createTextNode(`"${options.projectName}"`);
  const msg2 = document.createTextNode(' ? ');
  // const br = document.createElement('br');
  const msg3 = document.createTextNode('This action can not be undone.');
  message.appendChild(msg1);
  message.appendChild(projectName);
  message.appendChild(msg2);
  // message.appendChild(br);
  message.appendChild(msg3);
  contentWrap.appendChild(message);
  // 复选框
  const checkboxRow = document.createElement('div');
  checkboxRow.className = 'joycoder-dialog-checkbox-row';
  // 自定义checkbox结构
  const checkboxWrapper = document.createElement('label');
  checkboxWrapper.className = 'joycoder-checkbox-wrapper';
  const checkbox = document.createElement('input');
  checkbox.type = 'checkbox';
  checkbox.id = 'joycoder-dialog-checkbox';
  checkbox.className = 'joycoder-checkbox-input';
  const customBox = document.createElement('span');
  customBox.className = 'joycoder-checkbox-custom';
  checkboxWrapper.appendChild(checkbox);
  checkboxWrapper.appendChild(customBox);
  const checkboxLabel = document.createElement('span');
  checkboxLabel.className = 'joycoder-checkbox-label';
  checkboxLabel.textContent = 'Also delete local files from disk';
  checkboxRow.appendChild(checkboxWrapper);
  checkboxRow.appendChild(checkboxLabel);
  if (!options.isRemote) {
    contentWrap.appendChild(checkboxRow);
  }
  main.appendChild(contentWrap);
  dialog.appendChild(main);
  // 按钮区
  const buttons = document.createElement('div');
  buttons.className = 'joycoder-dialog-buttons';
  const btnCancel = document.createElement('button');
  btnCancel.className = 'joycoder-dialog-btn joycoder-dialog-cancel';
  btnCancel.textContent = 'Cancel';
  const btnDelete = document.createElement('button');
  btnDelete.className = 'joycoder-dialog-btn joycoder-dialog-delete';
  btnDelete.textContent = 'Delete';
  buttons.appendChild(btnCancel);
  buttons.appendChild(btnDelete);
  dialog.appendChild(buttons);
  // 4. 挂载
  mask.appendChild(dialog);
  // 优先插入到带有 VSCode 主题变量的容器下，保证 CSS 变量生效
  const workbench = document.querySelector('.monaco-workbench');
  if (workbench) {
    workbench.appendChild(mask);
  } else {
    document.body.appendChild(mask);
  }
  // 5. 样式
  if (!document.getElementById('joycoder-dialog-style')) {
    const style = document.createElement('style');
    style.id = 'joycoder-dialog-style';
    style.textContent = `
      .joycoder-dialog-mask {
        position: fixed; left: 0; top: 0; right: 0; bottom: 0;
        background: var(--vscode-widget-shadow); z-index: 9999;
        display: flex; align-items: center; justify-content: center;
      }
      .joycoder-dialog {
        background: var(--vscode-editor-background);
        border-radius: 10px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.25);
        color: var(--vscode-editor-foreground);
        min-width: 230px;
        padding: 24px;
        font-family: inherit;
        animation: joycoder-dialog-in 0.18s;
        display: flex;
        flex-direction: column;
        /*max-width: 480px;*/
      }
      @keyframes joycoder-dialog-in {
        from { transform: scale(0.96); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
      }
      .joycoder-dialog-main {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
      }
      .joycoder-dialog-icon-wrap {
        flex: none;
        width: 23px;
        margin-right: 9px;
        display: flex;
        align-items: flex-start;
        justify-content: center;
      }
      .joycoder-dialog-content-wrap {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
      .joycoder-dialog-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--vscode-input-label);
        line-height: 21px;
        margin-bottom: 4px;
      }
      .joycoder-dialog-message {
        max-width: 400px;
        font-size: 12px;
        color: var(--vscode-input-label);
        margin-bottom: 12px;
        line-height: 18px;
      }
      /*.joycoder-dialog-message b {
        color: #ff5555;
        font-weight: bold;
      }*/
      .joycoder-dialog-checkbox-row {
        margin-bottom: 0;
        display: flex;
        align-items: center;
        gap: 4px;
      }
      .joycoder-checkbox-wrapper {
        position: relative;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        width: 14px;
        height: 14px;
      }
      .joycoder-checkbox-input {
        opacity: 0;
        width: 14px;
        height: 14px;
        margin: 0;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
        cursor: pointer;
      }
      .joycoder-checkbox-custom {
        width: 14px;
        height: 14px;
        border: 1px solid var(--vscode-checkbox-border);
        border-radius: 3px;
        background: transparent;
        display: inline-block;
        box-sizing: border-box;
        position: relative;
        transition: background 0.15s, border 0.15s;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .joycoder-checkbox-input:checked + .joycoder-checkbox-custom {
        background: var(--vscode-checkbox-background);
        border-color: var(--vscode-checkbox-border);
      }
      .joycoder-checkbox-custom::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 41%;
        width: 3px;
        height: 6px;
        border: solid var(--vscode-editor-foreground, #222);
        border-width: 0 2px 2px 0;
        opacity: 0;
        transform: translate(-50%, -50%) scale(1) rotate(45deg);
        transition: opacity 0.15s;
      }
      .joycoder-checkbox-input:checked + .joycoder-checkbox-custom::after {
        opacity: 1;
      }
      .joycoder-checkbox-label {
        font-size: 12px;
        color: var(--vscode-input-label);
        user-select: none;
        margin-left: 4px;
      }
      .joycoder-dialog-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 16px;
      }
      .joycoder-dialog-btn {
        border: none;
        outline: none;
        border-radius: 4px;
        // min-width: 96px;
        color: var(--vscode-input-label);
        font-size: 12px;
        font-weight: 500;
        padding: 8px 12px;
        cursor: pointer;
        transition: background 0.15s;
      }
      .joycoder-dialog-cancel {
        background: var(--vscode-button-secondaryBackground);
        color: var(--vscode-button-secondaryForeground);
      }
      .joycoder-dialog-cancel:hover {
        background: var(--vscode-button-secondaryHoverBackground);
      }
      .joycoder-dialog-delete {
        background: var(--vscode-button-background);
        color: var(--vscode-button-foreground);
        font-weight: bold;
      }
      .joycoder-dialog-delete:hover {
        background: var(--vscode-button-hoverBackground);
      }
      .joycoder-dialog-btn:active { opacity: 0.85; }
      `;
    document.head.appendChild(style);
  }
  // 6. 事件
  function close() {
    const workbench = document.querySelector('.monaco-workbench');
    if (workbench) {
      workbench.removeChild(mask);
    } else {
      document.body.removeChild(mask);
    }
  }
  btnCancel.addEventListener('click', () => {
    close();
    options.onCancel?.();
  });
  btnDelete.addEventListener('click', () => {
    close();
    options.onConfirm(!!checkbox.checked);
  });
  dialog.addEventListener('click', e => e.stopPropagation());
  document.addEventListener('keydown', function escListener(ev) {
    if (ev.key === 'Escape') {
      close();
      options.onCancel?.();
      document.removeEventListener('keydown', escListener);
    }
  });
}
