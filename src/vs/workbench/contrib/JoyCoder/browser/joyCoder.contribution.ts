/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

/**
 * JoyCode.contribution.ts
 *
 * 这个文件是JoyCoder IDE的核心贡献文件，负责注册和初始化所有JoyCoder功能模块。
 * 它通过导入各种服务和组件，将JoyCoder的功能集成到VS Code工作台中。
 * 文件中的导入顺序很重要，因为某些模块可能依赖于其他模块的初始化。
 */

/**
 * 内联差异服务 - 提供代码内联差异比较功能
 * 用于在编辑器中直接显示代码修改前后的差异
 */
import './inlineDiffsService.js'

/**
 * 侧边栏相关功能 - 通过Ctrl+L快捷键激活
 * sidebarActions.js: 定义侧边栏相关操作，包括快捷键、菜单按钮等
 * sidebarPane.js: 定义侧边栏视图面板，包括视图容器注册和内容渲染
 * sidebarStateService.js: 管理侧边栏状态，如当前标签、历史是否打开等
 */
import './sidebarActions.js'
import './sidebarPane.js'
import './sidebarStateService.js'

/**
 * 快速编辑功能 - 通过Ctrl+K快捷键激活
 * 提供代码选择区域的快速编辑功能，包括AI辅助编辑
 */
import './quickEditActions.js'

/**
 * 线程历史服务 - 管理聊天线程的历史记录
 * 提供创建新线程、切换线程、向当前线程添加消息等功能
 * 使用IStorageService存储线程历史数据
 */
import './threadHistoryService.js'

/**
 * 自动完成服务 - 提供代码自动完成功能
 * 集成AI辅助的代码补全能力
 */
import './autocompleteService.js'

/**
 * 设置面板 - 提供JoyCoder的设置界面
 * 允许用户配置JoyCoder的各种功能和行为
 */
import './joyCoderSettingsPane.js'

/**
 * 登录相关功能 - 处理用户身份验证
 * 注意导入顺序非常重要:
 * 1. loginAction.js: 定义登录按钮和相关操作，包括上下文键
 * 2. loginStatusService.js: 实现登录状态跟踪器，管理用户登录状态
 * 3. loginContribution.js: 初始化登录相关功能，依赖于前两个模块
 *
 * 登录功能特性:
 * - 在登录失败时在右上角显示登录按钮
 * - 不会在首次启动时自动重定向到浏览器登录
 * - 已登录状态下不会再打开浏览器登录页面
 */
import './loginAction.js'
import './loginStatusService.js'
import './loginContribution.js'

/**
 * 欢迎页面 - 提供JoyCoder的欢迎指南
 * 在开发环境中每次启动都会显示欢迎页面
 * 在帮助菜单中添加打开欢迎指南的选项
 */
import './welcome.contribution.js'

/**
 * 新建项目功能 - 提供创建新项目的界面
 * 在File菜单中添加"New Project"选项
 * 点击后弹出对话框让用户输入项目信息
 */
import './project/index.js'

/**
 * 项目选择器 - 在顶部栏左侧提供项目选择功能
 * 允许用户快速切换不同的项目
 */
import './projectSelectorContribution.js';

/**
 * CSS样式 - 导入JoyCoder的样式定义
 * 定义JoyCoder界面的视觉风格和布局
 */
import './media/JoyCoder.css'

/**
 * 默认设置应用器 - 在启动时应用默认设置
 * 包括禁用屏幕阅读器和其他无障碍功能
 */
// import '../../../platform/JoyCoder/common/applyDefaultSettings.js'

/**
 * 双击Shift服务 - 提供双击Shift弹出对话框功能
 * 当用户快速连续按两次Shift键时，显示一个对话框
 * 对话框宽度为应用宽度的50%，高度为200px
 */
// import './doubleShiftService.js'
// import './doubleShiftContribution.js'
// import './doubleShiftCommands.js'


// import './loading/globalLoadingCommands.js'


/**
 * 全局Loading命令 - 提供全局Loading显示/隐藏指令
 */
import './loading/globalLoadingCommands.js'
