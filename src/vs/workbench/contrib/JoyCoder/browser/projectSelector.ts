/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import './media/projectSelector.css';

import { Disposable, DisposableStore } from '../../../../base/common/lifecycle.js';
import { Emitter } from '../../../../base/common/event.js';
import { IWorkspacesService, IR<PERSON><PERSON>, isR<PERSON><PERSON><PERSON><PERSON><PERSON>, isR<PERSON><PERSON>Workspace } from '../../../../platform/workspaces/common/workspaces.js';
import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { IHostService } from '../../../services/host/browser/host.js';
import { ILabelService } from '../../../../platform/label/common/label.js';
import { addDisposableListener, EventType } from '../../../../base/browser/dom.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { localize } from '../../../../nls.js';
import { IHoverService } from '../../../../platform/hover/browser/hover.js';
import { StandardKeyboardEvent } from '../../../../base/browser/keyboardEvent.js';
import { KeyCode } from '../../../../base/common/keyCodes.js';
import { IThemeService } from '../../../../platform/theme/common/themeService.js';
import { IFileDialogService } from '../../../../platform/dialogs/common/dialogs.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { OpenFileFolderAction } from '../../../browser/actions/workspaceActions.js';
import { INewProjectService } from '../common/newProject.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { ILifecycleService } from '../../../services/lifecycle/common/lifecycle.js';

export const CLOUDDEV_OPENPROJECT = 'workbench.view.extension.clouddevDashboard';
const PROJECT_LIST_KEY = 'workbench.view.openedProjects';

// 项目选择器控件
export class ProjectSelectorControl extends Disposable {

    private readonly _onDidChange = this._register(new Emitter<void>());
    readonly onDidChange = this._onDidChange.event;

    private readonly element: HTMLElement;
    private readonly projectNameElement: HTMLElement;
    private readonly projectIconElement: HTMLElement;
    private readonly disposables = this._register(new DisposableStore());
    // 获取当前工作区的 projectId（通过 IWorkspaceContextService + workspacesService）

    constructor(
        container: HTMLElement,
        @ICommandService private readonly commandService?: ICommandService,
        @IWorkspacesService private readonly workspacesService?: IWorkspacesService,
        @IInstantiationService _instantiationService?: IInstantiationService,
        @IHostService private readonly hostService?: IHostService,
        @ILabelService private readonly labelService?: ILabelService,
        @IWorkspaceContextService private readonly contextService?: IWorkspaceContextService,
        @IHoverService _hoverService?: IHoverService,
        @IThemeService _themeService?: IThemeService,
        @IFileDialogService private readonly fileDialogService?: IFileDialogService,
        @INewProjectService private readonly newProjectService?: INewProjectService,
        @IStorageService private readonly storageService?: IStorageService,
        @ILifecycleService private readonly lifecycleService?: ILifecycleService,
        // @IContextMenuService private readonly _contextMenuService?: IContextMenuService
    ) {
        super();

        // 检查容器中是否已经存在项目选择器
        const existingSelector = container.querySelector('.project-selector');
        if (existingSelector) {
            this.element = existingSelector as HTMLElement;
            this.projectIconElement = this.element.querySelector('.project-icon') as HTMLElement;
            this.projectNameElement = this.element.querySelector('.project-name') as HTMLElement;

            this.registerListeners();
            this.updateProjectName();
            return;
        }

        this.element = document.createElement('div');
        this.element.className = 'project-selector';
        this.element.setAttribute('role', 'button');
        this.element.setAttribute('tabindex', '0');
        this.element.setAttribute('aria-haspopup', 'true');
        this.element.setAttribute('aria-label', localize('projectSelector', "Project Selector"));

        // 确保元素可点击
        this.element.style.pointerEvents = 'auto';
        this.element.style.cursor = 'pointer';

        this.projectIconElement = document.createElement('div');
        this.projectIconElement.className = 'project-icon';
        // this.projectIconElement.appendChild(renderIcon(Codicon.folderOpened));
        this.element.appendChild(this.projectIconElement);

        this.projectNameElement = document.createElement('div');
        this.projectNameElement.className = 'project-name';
        this.element.appendChild(this.projectNameElement);

        const dropdownIcon = document.createElement('div');
        dropdownIcon.className = 'dropdown-icon';
        // dropdownIcon.appendChild(renderIcon(Codicon.chevronDown));
        dropdownIcon.appendChild(renderJoyIcon(joyiconsLibray.chevronDown));
        this.element.appendChild(dropdownIcon);

        // 设置样式，确保不会遮挡其他元素
        this.element.style.position = 'relative';
        this.element.style.zIndex = '1000';
        this.element.style.marginLeft = '0'; // 重置左边距

        // 复用头部的背景颜色
        // this.element.style.backgroundColor = 'inherit';

        container.appendChild(this.element);

        this.registerListeners();
        this.updateProjectName();
        this.monitorWindowProjectLifecycle();
    }


    private monitorWindowProjectLifecycle() {
        // 新窗口打开时添加项目
        this.addCurrentProjectToStorage();
        // this.storageService?.remove(PROJECT_LIST_KEY, StorageScope.APPLICATION);
        // 保证关闭窗口时清理当前项目
        this.lifecycleService?.onBeforeShutdown(event => {
            event.veto(
                (async () => {
                    await this.removeCurrentProjectFromStorage();
                    return false; // 不阻止关闭，清理后允许关闭
                })(),
                '清理项目数据'
            );
        });
    }
    private async getCurrentProjectInfo() {
        // 假设 projectList 结构为 { id, name, ... }
        const workspace = this.contextService?.getWorkspace();
        const list = await this.newProjectService?.getProjectList();
        if (list?.length === 0) {
            this.storageService?.remove(PROJECT_LIST_KEY, StorageScope.APPLICATION);
        }
        if (list?.length && workspace?.folders?.length) {
            const folder = workspace.folders[0];
            const scheme = folder.uri.scheme;
            if (scheme === 'file') {
                const project = list.find((item: any) => item.name === folder.name);
                return project;
            }

            if (scheme === 'vscode-remote' && folder.uri.authority?.split('+')?.[1]) {
                const authority = folder.uri.authority.split('+')[1];
                const project = list.find((item: any) => `${item.name}_${item.id}` === authority);
                return project;
            }
        }

        return null;
    }
    private showOpenList(storageList: any[], projectList: any[]) {
        if (!Array.isArray(storageList) || !Array.isArray(projectList)) {
            return [];
        }
        const storageIds = new Set(storageList.map(item => item.id));
        return projectList.filter(item => storageIds.has(item.id));
    }

    public getCurrentProjectFromStorage(): any[] {
        const raw = this.storageService?.get(PROJECT_LIST_KEY, StorageScope.APPLICATION, '[]');
        if (!raw) return [];
        try {
            return JSON.parse(raw);
        } catch {
            return [];
        }
    }

    private async addCurrentProjectToStorage() {
        const project = await this.getCurrentProjectInfo();
        if (!project) return;
        let list = this.getCurrentProjectFromStorage();
        if (!list.find((item: any) => item.id === project.id)) {
            list.push(project);
            this.storageService?.store(PROJECT_LIST_KEY, JSON.stringify(list), StorageScope.APPLICATION, StorageTarget.USER);
        }
    }

    private async removeCurrentProjectFromStorage(): Promise<void> {
        const project = await this.getCurrentProjectInfo();
        if (!project) {
            return;
        }
        let list = this.getCurrentProjectFromStorage();
        list = list.filter((item: any) => item.id !== project.id);

        if (list.length === 0) {
            this.storageService?.remove(PROJECT_LIST_KEY, StorageScope.APPLICATION);
        } else {
            this.storageService?.store(PROJECT_LIST_KEY, JSON.stringify(list), StorageScope.APPLICATION, StorageTarget.USER);
        }
        return Promise.resolve();
    }

    private registerListeners(): void {
        // 当工作区变化时更新项目名称
        if (this.contextService) {
            this._register(this.contextService.onDidChangeWorkspaceFolders(() => this.updateProjectName()));
            this._register(this.contextService.onDidChangeWorkbenchState(() => this.updateProjectName()));
        }

        try {
            // 点击时显示项目选择器菜单
            this._register(addDisposableListener(this.element, EventType.CLICK, e => {
                e.preventDefault();
                e.stopPropagation();
                this.showProjectSelector(e);
            }));

            // 按Enter/Space键时显示项目选择器菜单
            this._register(addDisposableListener(this.element, EventType.KEY_DOWN, e => {
                const event = new StandardKeyboardEvent(e);
                if (event.equals(KeyCode.Enter) || event.equals(KeyCode.Space)) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.showProjectSelector(e);
                }
            }));

            // 添加鼠标悬停效果
            // 移除悬停背景色，不做任何处理
            this._register(addDisposableListener(this.element, EventType.MOUSE_LEAVE, () => {
                this.element.style.backgroundColor = 'var(--vscode-titleBar-activeBackground)';
                // this.element.style.backgroundColor = 'inherit';
            }));
        } catch (error) {
            console.error('注册项目选择器事件监听器时出错:', error);
        }

        // 设置悬停提示
        this.element.title = this.getTooltip();
    }

    // 已移动到下方

    private async updateProjectName(): Promise<void> {
        let workspaceName = '';

        if (this.labelService && this.contextService) {
            workspaceName = this.labelService.getWorkspaceLabel(this.contextService.getWorkspace());
            const folders = this.contextService.getWorkspace().folders;
            if (workspaceName === 'workspace' && folders.length > 0) {
                const folder = folders[0].uri;
                const authority = folder.authority;
                if (folder.scheme === 'vscode-remote' && authority?.split('+')?.[1]) {
                    workspaceName = `workspace[ssh: ${authority?.split('+')?.[1]}]`
                }
            }
        }
        // 设置项目名称，如果没有打开的工作区，则显示"未打开文件夹"
        let displayName = workspaceName;
        // 更新标题前面的彩色图标
        // 根据项目名称生成一个彩色图标
        if (displayName) {
            const colorIcon = renderColorName(displayName);
            const existingChild = this.projectIconElement.firstChild;
            if (existingChild) {
                this.projectIconElement.replaceChild(colorIcon, existingChild);
            } else {
                this.projectIconElement.appendChild(colorIcon);
            }
        } else {
            displayName = localize('noWorkspace', "未打开文件夹");
        }
        this.projectNameElement.textContent = displayName;

        // 设置标题属性，以便在鼠标悬停时显示完整名称
        this.element.title = this.getTooltip();

        // 确保元素可点击
        this.element.style.pointerEvents = 'auto';
        this.element.style.cursor = 'pointer';
        this._onDidChange.fire();
    }

    private getTooltip(): string {
        let workspaceName = '';
        if (this.labelService && this.contextService) {
            workspaceName = this.labelService.getWorkspaceLabel(this.contextService.getWorkspace());
        }

        const displayName = workspaceName || localize('noWorkspace', "未打开文件夹");
        return localize('projectSelectorTooltip', "选择项目 - {0}", displayName);
    }

    private async showProjectSelector(_e: MouseEvent | KeyboardEvent): Promise<void> {
        // 确保必要的服务可用
        if (!this.hostService) {
            console.error('缺少必要的服务，无法显示项目选择器菜单');
            return;
        }

        // 检查是否已经存在菜单
        const existingMenu = document.querySelector('.project-selector-menu') as HTMLElement;
        if (existingMenu) {
            this.removeWindowDom(existingMenu);
            return;
        }

        // 创建菜单容器
        const menuContainer = document.createElement('div');
        menuContainer.className = 'project-selector-menu';
        menuContainer.id = 'joycoder-menu-container';

        // 确保不会被用于拖动窗口
        (menuContainer.style as any)['-webkit-app-region'] = 'no-drag';


        // 设置菜单位置
        const rect = this.element.getBoundingClientRect();
        menuContainer.style.top = (rect.bottom + 5) + 'px';
        menuContainer.style.left = rect.left + 'px';

        // 检查当前窗口是否有打开的文件夹或工作区
        const hasWorkspace = this.contextService?.getWorkbenchState() !== 1; // WorkbenchState.EMPTY = 1

        // 添加常用操作
        this.addActionMenuItem(menuContainer, localize('openFolder', "打开文件夹"), joyiconsLibray.fileAdd, async () => {
            // 隐藏菜单
            this.hideMenu();

            if (!this.fileDialogService || !this.contextService) {
                console.error('缺少必要的服务，无法打开文件夹');
                return;
            }

            try {
                // 根据当前窗口状态决定是在当前窗口打开还是在新窗口打开
                await this.fileDialogService.pickFolderAndOpen({
                    forceNewWindow: hasWorkspace, // 如果当前窗口已有文件，则在新窗口中打开
                });
            } catch (error) {
                console.error('打开文件夹时出错:', error);
            }
        });


        this.addActionMenuItem(menuContainer, localize({ key: 'miOpen', comment: ['denotes a mnemonic'] }, "Open..."), joyiconsLibray.file, () => {
            this.hideMenu();
            this.commandService?.executeCommand(OpenFileFolderAction.ID);
        });
        this.addActionMenuItem(menuContainer, localize('manageProject', "Manage Projects..."), joyiconsLibray.fileManage, async () => {
            this.hideMenu();
            this.commandService?.executeCommand(CLOUDDEV_OPENPROJECT);
        });
        this.addWindowDom(menuContainer);

        // 监听点击弹窗外部关闭弹窗
        this.addOutsideClickListener(menuContainer);

        this.newProjectService?.getProjectList().then(async (projectList) => {
            // 判断当前窗口的项目是否在项目列表中
            let list = this.getCurrentProjectFromStorage();
            list = this.showOpenList(list, projectList)
            if (list?.length) {
                this.addSeparator(menuContainer);
                const openProjectTitle = document.createElement('div');
                openProjectTitle.className = 'menu-section-title';
                openProjectTitle.textContent = localize('openProjects', "Open Projects");
                menuContainer.appendChild(openProjectTitle);
                list.forEach(project => {
                    this.addRecentMenuItem(menuContainer, project, true, () => {
                        this.hideMenu();
                    });
                })
            }
            // 直接显示项目列表
            if (projectList) {
                const filterProjects = projectList?.filter(project => !list.some(item => item.id === project.id));
                if (filterProjects?.length > 0) {
                    const recentlyOpened = await this.workspacesService?.getRecentlyOpened();
                    const recentList = recentlyOpened?.workspaces || [];
                    if (recentList.length > 0) {
                        let showProjects: any[] = [];

                        filterProjects.forEach(project => {
                            recentList.forEach((recent) => {
                                const isProject = this.isProjectInRecent(recent, project);
                                if (isProject) {
                                    showProjects.push({
                                        ...project,
                                        name: project.name,
                                        description: project.description || 'no description',
                                        recent
                                    })
                                }
                            })
                        })
                        // 过滤掉name相同的数据
                        showProjects = showProjects.filter((item, index, self) =>
                            index === self.findIndex((t) => (
                                t.name === item.name
                                && t.description === item.description
                            )))
                        if (showProjects.length > 0) {
                            // 判断是否显示最近打开的项目
                            this.addSeparator(menuContainer);
                            const recentTitle = document.createElement('div');
                            recentTitle.className = 'menu-section-title';
                            recentTitle.textContent = localize('recent', "Recent Projects");
                            menuContainer.appendChild(recentTitle);
                            // 添加"最近"标题
                            showProjects.forEach(project => {
                                this.addRecentMenuItem(menuContainer, project, false, () => {
                                    this.openRecent(project.recent, project);
                                    this.hideMenu();
                                });
                            })
                        }
                    }
                }
            }
            this.addWindowDom(menuContainer);
        })
        // 添加点击事件监听器，点击菜单外部时关闭菜单
        const timer = setTimeout(() => {
            document.addEventListener('click', this.handleDocumentClick);
            document.addEventListener('keydown', this.handleKeyDown);
            clearTimeout(timer);
        }, 0);
    }

    /**
     * 监听点击弹窗外部关闭弹窗
     */
    private addOutsideClickListener(menuContainer: HTMLElement): void {
        const handleOutsideClick = (event: MouseEvent) => {
            if (
                !menuContainer.contains(event.target as Node) &&
                !this.element.contains(event.target as Node)
            ) {
                this.hideMenu();
                document.removeEventListener('mousedown', handleOutsideClick, true);
            }
        };
        // 使用 setTimeout 保证事件绑定在弹窗渲染后
        setTimeout(() => {
            document.addEventListener('mousedown', handleOutsideClick, true);
        }, 0);
        window.addEventListener('blur', () => {
            this.hideMenu();
        });
    }

    // private isProjectInCurrentWorkspace(currentWorkspace: any, project: any): boolean {
    //     const authority = currentWorkspace?.uri?.authority;
    //     const list = authority.split('+');
    //     if (list?.[1]) {
    //         return list[1] === `${project.name}_${project.id}`;
    //     }
    //     return authority?.includes(project.name) || currentWorkspace.name === project.name;
    // }

    private isProjectInRecent(recent: any, project: any): boolean {
        if (!recent) return false;
        if (recent.folderUri.scheme === 'vscode-remote' && project.devMode === 1) {
            const authority = recent.folderUri?.authority;
            const lowCaseStr = project.name?.toLowerCase();
            return authority?.includes(lowCaseStr) || authority?.includes(project.name)
        }
        if (recent.folderUri.scheme === 'file' && project.devMode === 2) {
            return recent.folderUri.path?.includes(project.name)
        }
        return false
    }

    private addWindowDom(targetDom: HTMLElement) {
        if (!targetDom) return;
        const domId = targetDom.id;
        const workbench = document.querySelector('.monaco-workbench');
        let parent: HTMLElement | null = null;
        if (workbench) {
            parent = workbench as HTMLElement;
        } else {
            parent = document.body;
        }
        if (domId) {
            const exist = parent.querySelector(`#${domId}`);
            if (exist) {
                parent.replaceChild(targetDom, exist);
                return;
            }
        }
        parent.appendChild(targetDom);
    }
    private removeWindowDom(targetDom: HTMLElement) {
        if (!targetDom) return;
        const workbench = document.querySelector('.monaco-workbench');
        if (workbench) {
            workbench.removeChild(targetDom);
        } else {
            document.body.removeChild(targetDom);
        }
    }
    /**
     * 检查是否是当前工作区
     */
    // private isCurrentWorkspace(recent: IRecent): boolean {
    //     if (!this.contextService) {
    //         return false;
    //     }

    //     const currentWorkspace = this.contextService.getWorkspace();

    //     if (isRecentFolder(recent)) {
    //         return currentWorkspace.folders.some(folder =>
    //             folder.uri.toString() === recent.folderUri.toString());
    //     }

    //     if (isRecentWorkspace(recent)) {
    //         return currentWorkspace.configuration?.toString() === recent.workspace.configPath.toString();
    //     }

    //     return false;
    // }

    /**
     * 获取最近项目的路径
     */
    // private getRecentPath(recent: IRecent): string {
    //     if (!this.labelService) {
    //         return '';
    //     }

    //     if (isRecentFolder(recent)) {
    //         return this.labelService.getUriLabel(recent.folderUri);
    //     }

    //     if (isRecentWorkspace(recent)) {
    //         return this.labelService.getUriLabel(recent.workspace.configPath);
    //     }

    //     return this.labelService.getUriLabel(recent.fileUri);
    // }

    /**
     * 添加操作菜单项
     */
    private addActionMenuItem(container: HTMLElement, label: string, iconName: string, onClick: () => void): void {
        const menuItem = document.createElement('div');
        menuItem.className = 'menu-item action-item';

        // 添加图标
        const icon = document.createElement('div');
        // icon.className = `codicon codicon-${iconName}`;
        icon.className = `joyicon joyicon-${iconName}`;
        icon.style.marginRight = '8px';
        icon.style.width = '16px';
        icon.style.height = '16px';
        icon.style.display = 'flex';
        icon.style.alignItems = 'center';
        icon.style.justifyContent = 'center';
        menuItem.appendChild(icon);

        // 添加标签
        const labelElement = document.createElement('div');
        labelElement.textContent = label;
        labelElement.style.overflow = 'hidden';
        labelElement.style.textOverflow = 'ellipsis';
        labelElement.style.flex = '1';
        menuItem.appendChild(labelElement);

        // 添加悬停效果
        menuItem.addEventListener('mouseenter', () => {
            menuItem.classList.add('hover');
        });

        menuItem.addEventListener('mouseleave', () => {
            menuItem.classList.remove('hover');
        });

        // 添加点击事件
        menuItem.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            onClick();
        });

        container.appendChild(menuItem);
    }

    /**
     * 添加最近项目菜单项
     */
    private addRecentMenuItem(container: HTMLElement, project: any, isCurrentWorkspace: boolean, onClick: () => void): void {
        const menuItem = document.createElement('div');
        menuItem.className = 'menu-item recent-item';

        // 创建左侧容器，用于放置图标
        const leftContainer = document.createElement('div');
        leftContainer.className = 'item-left';
        leftContainer.style.display = 'flex';
        // leftContainer.style.alignItems = 'center';
        leftContainer.style.alignItems = 'flex-start';
        leftContainer.style.justifyContent = 'center';
        // leftContainer.style.width = '20px';
        // leftContainer.style.height = '20px';
        leftContainer.style.height = '100%';
        leftContainer.style.marginRight = '8px';
        leftContainer.style.fontSize = '18px';

        // 根据项目名称生成一个彩色图标
        const colorIcon = renderColorName(project.abbrName, project.bgColor);

        leftContainer.appendChild(colorIcon);
        menuItem.appendChild(leftContainer);

        // 创建中间容器，用于放置标签和路径
        const middleContainer = document.createElement('div');
        middleContainer.className = 'item-middle';
        middleContainer.style.display = 'flex';
        middleContainer.style.flexDirection = 'column';
        middleContainer.style.flex = '1';
        middleContainer.style.overflow = 'hidden';

        // 添加标签
        const labelElement = document.createElement('div');
        labelElement.textContent = project.name;
        labelElement.style.overflow = 'hidden';
        labelElement.style.textOverflow = 'ellipsis';
        labelElement.style.whiteSpace = 'nowrap';
        labelElement.style.color = 'var(--joycode-commandCenter-foreground)';
        labelElement.style.fontSize = '12px';
        labelElement.style.fontWeight = '500';
        labelElement.style.lineHeight = '18px';
        middleContainer.appendChild(labelElement);

        // 添加路径
        const pathElement = document.createElement('div');
        pathElement.textContent = project.description || 'no description';
        pathElement.style.fontSize = '12px';
        pathElement.style.fontWeight = '500';
        pathElement.style.lineHeight = '18px';
        pathElement.style.color = 'var(--joycode-descriptionForeground)';
        pathElement.style.overflow = 'hidden';
        pathElement.style.textOverflow = 'ellipsis';
        pathElement.style.whiteSpace = 'nowrap';
        middleContainer.appendChild(pathElement);

        menuItem.appendChild(middleContainer);

        // 如果是当前工作区，添加勾选标记
        // TODO 设计图暂无当前项目标识符，为了减少DOM元素渲染，暂时先注释
        /* if (isCurrentWorkspace) {
            const rightContainer = document.createElement('div');
            rightContainer.className = 'item-right';
            rightContainer.style.display = 'flex';
            rightContainer.style.alignItems = 'center';
            rightContainer.style.marginLeft = '8px';

            const checkIcon = document.createElement('div');
            checkIcon.className = 'codicon codicon-check';
            checkIcon.style.color = 'var(--vscode-menu-foreground)';
            rightContainer.appendChild(checkIcon);

            menuItem.appendChild(rightContainer);
        } */

        // 添加悬停效果
        /* menuItem.addEventListener('mouseenter', () => {
            menuItem.classList.add('hover');
            menuItem.style.backgroundColor = 'var(--vscode-menu-selectionBackground, #04395e)';
            menuItem.style.color = 'var(--vscode-menu-selectionForeground, #ffffff)';

            // 更新路径颜色
            pathElement.style.color = 'var(--vscode-menu-selectionForeground, #ffffff)';

            // 更新勾选标记颜色
            const checkIcon = menuItem.querySelector('.codicon-check');
            if (checkIcon) {
                (checkIcon as HTMLElement).style.color = 'var(--vscode-menu-selectionForeground, #ffffff)';
            }
        });

        menuItem.addEventListener('mouseleave', () => {
            menuItem.classList.remove('hover');
            menuItem.style.backgroundColor = '';
            menuItem.style.color = '';

            // 恢复路径颜色
            pathElement.style.color = 'var(--vscode-descriptionForeground)';

            // 恢复勾选标记颜色
            const checkIcon = menuItem.querySelector('.codicon-check');
            if (checkIcon) {
                (checkIcon as HTMLElement).style.color = 'var(--vscode-menu-foreground)';
            }
        }); */

        // 添加点击事件
        menuItem.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            onClick();
        });

        container.appendChild(menuItem);
    }



    /**
     * 添加分隔符
     */
    private addSeparator(container: HTMLElement): void {
        const separator = document.createElement('div');
        separator.className = 'menu-separator';
        container.appendChild(separator);
    }

    /**
     * 处理文档点击事件
     */
    private handleDocumentClick = (e: MouseEvent) => {
        const menu = document.querySelector('.project-selector-menu') as HTMLElement;
        if (menu && !menu.contains(e.target as Node) && !this.element.contains(e.target as Node)) {
            this.hideMenu();
        }
    };

    /**
     * 处理键盘事件
     */
    private handleKeyDown = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            this.hideMenu();
        }
    };

    /**
     * 隐藏菜单
     */
    private hideMenu(): void {
        const menu = document.querySelector('.project-selector-menu') as HTMLElement;
        if (menu && menu.parentNode) {
            menu.parentNode.removeChild(menu);
            document.removeEventListener('click', this.handleDocumentClick);
            document.removeEventListener('keydown', this.handleKeyDown);
            return;
        }
    }

    /**
     * 获取最近项目的标签
     */
    // private getRecentLabel(recent: IRecent): string {
    //     if (!this.labelService) {
    //         return '';
    //     }

    //     if (isRecentFolder(recent)) {
    //         return this.labelService.getWorkspaceLabel(recent.folderUri);
    //     }

    //     if (isRecentWorkspace(recent)) {
    //         return this.labelService.getWorkspaceLabel(recent.workspace);
    //     }

    //     return this.labelService.getUriLabel(recent.fileUri);
    // }

    /**
     * 打开最近的项目
     */
    private openRecent(recent: IRecent, project: any = {}): void {
        if (!this.hostService) {
            return;
        }
        if (isRecentFolder(recent)) {
            const uriWithProjectId = recent.folderUri.with({ query: `projectId=${project.id}` });
            this.hostService.openWindow([{ folderUri: uriWithProjectId }], { forceNewWindow: false });
        } else if (isRecentWorkspace(recent)) {
            const uriWithProjectId = recent.workspace.configPath.with({ query: `projectId=${project.id}` });
            this.hostService.openWindow([{ workspaceUri: uriWithProjectId }], { forceNewWindow: false });
        } else {
            const uriWithProjectId = recent.fileUri.with({ query: `projectId=${project.id}` });
            this.hostService.openWindow([{ fileUri: uriWithProjectId }], { forceNewWindow: false });
        }

        // 隐藏菜单
        this.hideMenu();
    }

    /**
     * 销毁
     */
    override dispose(): void {
        super.dispose();
        this.disposables.dispose();

        // 移除菜单
        this.hideMenu();

        // 移除事件监听器
        document.removeEventListener('click', this.handleDocumentClick);
        document.removeEventListener('keydown', this.handleKeyDown);
    }
}

// 不在这里注册命令，而是在 ProjectSelectorContribution 中注册


/** 生成彩色图标 **/
function renderColorName(abbrName: string, bgColor?: string) {
    // 根据项目名称生成一个彩色图标
    const colorIcon = document.createElement('div');
    colorIcon.className = 'color-icon';
    colorIcon.style.width = '20px';
    colorIcon.style.height = '20px';
    colorIcon.style.borderRadius = '36px';
    colorIcon.style.display = 'flex';
    colorIcon.style.alignItems = 'center';
    colorIcon.style.justifyContent = 'center';
    colorIcon.style.fontWeight = 'bold';
    colorIcon.style.color = '#ffffff';
    if (abbrName && bgColor) {
        colorIcon.style.backgroundColor = bgColor;
        colorIcon.textContent = abbrName;
        return colorIcon;
    }

    // 根据标签生成背景颜色和文本
    const colors = ['#4CAF50', '#FFC107', '#2196F3', '#E91E63', '#9C27B0', '#FF5722'];
    const colorIndex = Math.abs(abbrName?.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)) % colors.length;
    colorIcon.style.backgroundColor = colors[colorIndex];
    colorIcon.textContent = abbrName?.substring(0, 1).toUpperCase();;
    return colorIcon;
}

/** 生成joy-icon；字体图标 **/
function renderJoyIcon(id: string) {
    const icon = document.createElement('span');
    icon.className = `joyicon joyicon-${id}`;
    return icon;
}

export const joyiconsLibray = {
    chevronDown: 'xiajiantoutianchong',
    // chevronLeft: 'chevron-left',
    // chevronRight: 'youjiantoutianchong:before',
    // chevronUp: 'chevron-up',
    file: 'file',
    fileAdd: 'file-add',
    fileManage: 'file-manage',

    gitBranch: 'git-branch',
    remoteExplorer: 'remote-explorer',
};

