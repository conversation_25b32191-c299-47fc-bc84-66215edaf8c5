/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Registry } from '../../../../platform/registry/common/platform.js';
import { Extensions as WorkbenchExtensions, IWorkbenchContributionsRegistry } from '../../../common/contributions.js';
import { LifecyclePhase } from '../../../../workbench/services/lifecycle/common/lifecycle.js';
import { MenuId, MenuRegistry, Action2, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';

import { EditorPaneDescriptor, IEditorPaneRegistry } from '../../../../workbench/browser/editor.js';
import { EditorExtensions } from '../../../common/editor.js';
import { localize } from '../../../../nls.js';
import { JoyCoderWelcomePane } from './welcomePane.js';
import { JoyCoderWelcomeInput } from './welcomeInput.js';
import { IJoyCoderWelcomeService, JoyCoderWelcomeContribution } from './welcomeService.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';

// 确保扩展导入服务被加载并注册
import './extensionImportService.js';


// 注册欢迎界面贡献，在工作台准备好后初始化
Registry.as<IWorkbenchContributionsRegistry>(WorkbenchExtensions.Workbench).registerWorkbenchContribution(
	JoyCoderWelcomeContribution,
	LifecyclePhase.Restored
);

// 注册欢迎界面编辑器面板
Registry.as<IEditorPaneRegistry>(EditorExtensions.EditorPane).registerEditorPane(
	EditorPaneDescriptor.create(
		JoyCoderWelcomePane,
		JoyCoderWelcomePane.ID,
		localize('joyCodeWelcomePane', "JoyCode 欢迎指南")
	),
	[
		new SyncDescriptor(JoyCoderWelcomeInput)
	]
);

// 注册打开欢迎指南的操作
class OpenWelcomeGuideAction extends Action2 {
	public static readonly ID = 'joyCoder.openWelcomeGuide';
	public static readonly LABEL = localize('openWelcomeGuide', "打开 JoyCode 欢迎指南");

	constructor() {
		super({
			id: OpenWelcomeGuideAction.ID,
			title: { value: OpenWelcomeGuideAction.LABEL, original: 'Open JoyCode Welcome Guide' },
			category: { value: localize('joyCode', "JoyCode"), original: 'JoyCode' },
			f1: true
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const welcomeService = accessor.get(IJoyCoderWelcomeService);
		await welcomeService.openWelcomePage();
	}
}

// 注册Action
registerAction2(OpenWelcomeGuideAction);

// 在帮助菜单中添加打开欢迎指南的选项
MenuRegistry.appendMenuItem(MenuId.MenubarHelpMenu, {
	group: '1_welcome',
	command: {
		id: OpenWelcomeGuideAction.ID,
		title: localize('openWelcomeGuide', "打开 JoyCode 欢迎指南")
	},
	order: 1
});
