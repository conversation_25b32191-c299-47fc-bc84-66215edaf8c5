/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import {
	Action2,
	registerAction2,
} from "../../../../../platform/actions/common/actions.js";
import { ServicesAccessor } from "../../../../../platform/instantiation/common/instantiation.js";
import { localize } from "../../../../../nls.js";
import { Categories } from "../../../../../platform/action/common/actionCommonCategories.js";
import { ICommandService } from "../../../../../platform/commands/common/commands.js";
// import { $ } from "../../../../../base/browser/dom.js";
import '../media/doubleShiftDialog.css';

// 命令ID
export const SHOW_GLOBAL_LOADING_COMMAND_ID = "workbench.action.showGlobalLoading";
export const TEST_SHOW_GLOBAL_LOADING_COMMAND_ID_OPEN = "workbench.action.testShowGlobalLoadingOpen";
export const TEST_SHOW_GLOBAL_LOADING_COMMAND_ID_CLOSE = "workbench.action.testShowGlobalLoadingClose";

// 注册显示全局Loading命令
let globalLoadingOverlay: GlobalLoadingOverlay | undefined = undefined;

// 参考 doubleShiftDialog 的实现，定义全局 loading overlay 类
class GlobalLoadingOverlay {
	private static readonly ID = 'joycoder-global-loading-overlay';
	private overlay: HTMLElement | null = null;
	private textNode: HTMLElement | null = null;
	public visible: boolean = false;

	constructor() {
		this.create();
	}

	private create(): void {
		// 若已存在则不重复创建
		if (document.getElementById(GlobalLoadingOverlay.ID)) {
			this.overlay = document.getElementById(GlobalLoadingOverlay.ID);
			this.textNode = this.overlay?.querySelector('.joycoder-global-loading-text') as HTMLElement;
			return;
		}
		this.overlay = document.createElement('div');
		this.overlay.id = GlobalLoadingOverlay.ID;
		this.overlay.className = 'double-shift-dialog-overlay';
		this.overlay.style.display = 'none';

		// loading 内容容器
		const loadingBox = document.createElement('div');
		loadingBox.style.background = 'var(--vscode-editor-background,#18181B)';
		loadingBox.style.borderRadius = '12px';
		loadingBox.style.boxShadow = '0 4px 16px rgba(0,0,0,0.11)';
		loadingBox.style.padding = '32px 32px 24px 32px';
		loadingBox.style.display = 'flex';
		loadingBox.style.flexDirection = 'column';
		loadingBox.style.alignItems = 'center';
		loadingBox.style.justifyContent = 'center';
		loadingBox.style.minWidth = '180px';
		loadingBox.style.minHeight = '100px';
		loadingBox.style.position = 'relative';

		// loading 动画
		const spinner = document.createElement('div');
		spinner.className = 'joycoder-global-loading-spinner';
		spinner.style.width = '48px';
		spinner.style.height = '48px';
		spinner.style.border = '4px solid var(--vscode-progressBar-background,#0078d4)';
		spinner.style.borderTop = '4px solid #fff';
		spinner.style.borderRadius = '50%';
		spinner.style.animation = 'joycoder-spin 1s linear infinite';
		spinner.style.marginBottom = '18px';
		loadingBox.appendChild(spinner);

		// loading 文字
		const text = document.createElement('div');
		text.className = 'joycoder-global-loading-text';
		text.style.color = 'var(--vscode-editor-foreground,#fff)';
		text.textContent = localize("loading", "loading...");
		this.textNode = text;
		loadingBox.appendChild(text);

		// 关闭按钮
		const closeBtn = document.createElement('button');
		closeBtn.className = 'joycoder-loading-close-btn';
		closeBtn.textContent = '×';
		closeBtn.onclick = () => this.hide();
		closeBtn.style.position = 'absolute';
		closeBtn.style.top = '8px';
		closeBtn.style.right = '8px';
		closeBtn.style.background = 'none';
		closeBtn.style.border = 'none';
		closeBtn.style.color = 'var(--vscode-editor-foreground,#555)';
		closeBtn.style.cursor = 'pointer';
		closeBtn.style.fontSize = '18px';
		closeBtn.style.fontWeight = 'bold';
		closeBtn.style.opacity = '0.7';
		closeBtn.style.transition = 'opacity 0.2s';
		closeBtn.onmouseenter = () => { closeBtn.style.opacity = '1'; };
		closeBtn.onmouseleave = () => { closeBtn.style.opacity = '0.7'; };
		loadingBox.appendChild(closeBtn);

		this.overlay.appendChild(loadingBox);

		const workbench = document.querySelector('.monaco-workbench');
		if (workbench) {
			workbench.appendChild(this.overlay);
		} else {
			document.body.appendChild(this.overlay);
		}

		// 注入动画样式（如未注入）
		if (!document.getElementById('joycoder-global-loading-spinner-style')) {
			const style = document.createElement('style');
			style.id = 'joycoder-global-loading-spinner-style';
			style.textContent = `
				@keyframes joycoder-spin {
					0% { transform: rotate(0deg);}
					100% { transform: rotate(360deg);}
				}
			`;
			document.head.appendChild(style);
		}
	}

	/**
	 * 展示 loading，支持自定义文本
	 * @param text 可选，自定义 loading 文本
	 */
	public show(text?: string): void {
		if (!this.overlay) return;
		if (this.textNode) {
			this.textNode.textContent = text || localize("loading", "loading...");
		}
		this.overlay.style.display = 'flex';
		this.visible = true;
	}

	public hide(): void {
		if (!this.overlay) return;
		this.overlay.style.display = 'none';
		this.visible = false;
	}
}

registerAction2(
	class ShowGlobalLoadingAction extends Action2 {
		constructor() {
			super({
				id: SHOW_GLOBAL_LOADING_COMMAND_ID,
				title: {
					value: localize("showGlobalLoading", "Show Global Loading"),
					original: "Show Global Loading",
				},
				category: Categories.View,
				f1: true,
			});
		}

		async run(accessor: ServicesAccessor, isLoading?: boolean, loadingText?: string): Promise<void> {
			if (isLoading) {
				if (!globalLoadingOverlay) {
					globalLoadingOverlay = new GlobalLoadingOverlay();
				}
				globalLoadingOverlay.show(loadingText);
			} else {
				if (globalLoadingOverlay) {
					globalLoadingOverlay.hide();
				}
			}
		}
	}
);

// 注册测试命令，便于开发调试
registerAction2(
	class TestShowGlobalLoadingOpenAction extends Action2 {
		constructor() {
			super({
				id: TEST_SHOW_GLOBAL_LOADING_COMMAND_ID_OPEN,
				title: {
					value: localize(
						"testShowGlobalLoadingOpen",
						"Test Show Global Loading (Open)"
					),
					original: "Test Show Global Loading (Open)",
				},
				category: Categories.View,
				f1: true,
			});
		}

		async run(accessor: ServicesAccessor): Promise<void> {
			const commandService = accessor.get(ICommandService);
			await commandService.executeCommand(SHOW_GLOBAL_LOADING_COMMAND_ID, true, "Test loading...");
		}
	}
);

registerAction2(
	class TestShowGlobalLoadingCloseAction extends Action2 {
		constructor() {
			super({
				id: TEST_SHOW_GLOBAL_LOADING_COMMAND_ID_CLOSE,
				title: {
					value: localize(
						"testShowGlobalLoadingClose",
						"Test Show Global Loading (Close)"
					),
					original: "Test Show Global Loading (Close)",
				},
				category: Categories.View,
				f1: true,
			});
		}

		async run(accessor: ServicesAccessor): Promise<void> {
			const commandService = accessor.get(ICommandService);
			await commandService.executeCommand(
				SHOW_GLOBAL_LOADING_COMMAND_ID,
				false
			);
		}
	}
);
