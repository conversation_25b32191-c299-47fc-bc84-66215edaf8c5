/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { registerAction2, Action2, MenuId } from '../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import * as nls from '../../../../nls.js';
import { ILoginService } from '../../../../platform/login/common/login.js';
import { ContextKeyExpr, IContextKeyService, RawContextKey } from '../../../../platform/contextkey/common/contextkey.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { URI } from '../../../../base/common/uri.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import "./joycoder-login-area.css";

// 上下文键：表示未登录状态
export const NOT_LOGGED_IN_KEY = 'joycoderNotLoggedIn';
export const NOT_LOGGED_IN_CONTEXT_KEY = new RawContextKey<boolean>(NOT_LOGGED_IN_KEY, true, nls.localize('joycoderNotLoggedIn', "用户未登录"));

// 命令ID
export const LOGIN_ACTION_ID = 'workbench.action.joycoderLogin';
// 命令 ID 定义
export const IS_LOGGED_IN_COMMAND_ID = 'workbench.action.joycoderIsLoggedIn';
export const GET_LOGIN_INFO_COMMAND_ID = 'workbench.action.joycoderGetLoginInfo';
export const LOGOUT_ACTION_ID = 'workbench.action.joycoderLogout';

// 登录按钮SVG代码
const LOGIN_BUTTON_SVG = `<svg width="80" height="26" viewBox="0 0 80 26" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="vscodeBlue" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#007ACC; stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0066B8; stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect x="1" y="1" rx="5" ry="5" width="78" height="24" fill="url(#vscodeBlue)" stroke="#005BA1" stroke-width="1.5"/>
  <text x="40" y="17" text-anchor="middle" font-family="Segoe UI, sans-serif" font-size="13" fill="white" font-weight="bold">
    去登录
  </text>
</svg>`;

// 转换SVG为data URI
const encodeBase64 = (str: string) => {
	return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (_, p1) => String.fromCharCode(parseInt(p1, 16))));
};
const LOGIN_BUTTON_DATA_URI = URI.parse(`data:image/svg+xml;base64,${encodeBase64(LOGIN_BUTTON_SVG)}`);

// 导入登录服务类
import { LoginService } from '../../../../platform/login/browser/loginService.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';

// 直接调用登录服务的函数
export async function directLogin(loginUrl?: string, mainProcessServiceParam?: any): Promise<boolean> {
	try {
		// 优先使用传入的mainProcessService参数，如果没有则尝试使用全局变量
		const mainProcessService = mainProcessServiceParam || (self as any).mainProcessService;
		if (mainProcessService) {
			const loginService = new LoginService(mainProcessService);

			// 先检查是否已登录
			const isLoggedIn = await loginService.isLoggedIn();

			// 如果已登录，则不需要再跳转浏览器
			if (isLoggedIn) {
				return true; // 返回true表示已登录，不需要再登录
			}

			// 如果未登录，才执行登录操作
			const result = await loginService.login();
			return result;
		}

		// 如果无法获取登录服务，使用提供的登录URL或默认值
		// 确保loginUrl是一个简单的字符串值
		const finalLoginUrl = (typeof loginUrl === 'string' && loginUrl) || 'https://joycode.jd.com/login?ideAppName=joycoder&fromIde=ide';

		if (!loginUrl) {
			console.warn('未提供登录URL，使用默认值');
		}

		const newWindow = window.open(finalLoginUrl, '_blank');
		return !!newWindow;
	} catch (error) {
		console.error('登录失败:', error);
		return false;
	}
}

// 存储登出回调的映射
export const callbackMap = new Map<string, Function>();

// 注册登录图标
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: LOGIN_ACTION_ID,
			title: {
				value: nls.localize('joycoder.login', "去登录"),
				original: 'Login Account'
			},
			f1: true,
			icon: {
				dark: LOGIN_BUTTON_DATA_URI,
				light: LOGIN_BUTTON_DATA_URI
			},
			menu: [
				{
					id: MenuId.LayoutControlMenuSubmenu,
					// 使用z_end组，与原来的设置按钮位置一致
					group: 'z_end',
					when: ContextKeyExpr.equals('joycoderNotLoggedIn', true)
				},
				{
					id: MenuId.LayoutControlMenu,
					// 使用z_end组，与原来的设置按钮位置一致
					group: 'z_end',
					when: ContextKeyExpr.equals('joycoderNotLoggedIn', true)
				},
				// 添加到命令面板，便于测试
				{
					id: MenuId.CommandPalette
				}
			]

		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		// 获取必要的服务 - 在同步部分获取所有需要的服务和值
		const notificationService = accessor.get(INotificationService);
		const mainProcessService = accessor.get(IMainProcessService);
		const loginService = accessor.get(ILoginService);
		const productService = accessor.get(IProductService);

		// 获取登录URL - 在同步部分获取并转换为简单字符串
		const loginUrl = productService.joyCoderLoginUrl ? String(productService.joyCoderLoginUrl) : undefined;

		// 将主进程服务存储到全局变量
		if (mainProcessService) {
			(self as any).mainProcessService = mainProcessService;
		}

		// 创建一个本地函数来处理登录逻辑，避免在异步操作中使用accessor
		const performLogin = async () => {
			try {
				// 先检查是否已登录
				const isLoggedIn = await loginService.isLoggedIn();

				// 如果已登录，则不需要再跳转浏览器
				if (isLoggedIn) {
					notificationService.notify({
						severity: 1, // Info级别
						message: '您已经登录，无需再次登录',
						sticky: false // 设置为非固定，允许自动消失
					});
					return;
				}

				// 如果未登录，才执行登录操作
				// 传递登录URL和mainProcessService，避免依赖全局变量
				const result = await directLogin(loginUrl, mainProcessService);

				// 显示登录结果通知
				if (result) {
					notificationService.notify({
						severity: 1, // Info级别
						message: '登录操作已启动，请在浏览器中完成登录',
						sticky: false // 设置为非固定，允许自动消失
					});
				} else {
					notificationService.error('启动登录操作失败，请稍后重试');
				}
			} catch (error) {
				console.error('登录过程中发生错误:', error);
				notificationService.error('登录失败，请稍后重试');
			}
		};

		// 执行登录逻辑
		await performLogin();
	}
});

// 注册检查登录状态命令
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: IS_LOGGED_IN_COMMAND_ID,
			title: {
				value: nls.localize('joycoder.isLoggedIn', "检查登录状态"),
				original: 'Check Login Status'
			},
			f1: true
		});
	}

	async run(accessor: ServicesAccessor): Promise<boolean> {
		// 获取登录服务并检查登录状态
		const loginService = accessor.get(ILoginService);
		return loginService.isLoggedIn();
	}
});

// 注册获取登录信息命令
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: GET_LOGIN_INFO_COMMAND_ID,
			title: {
				value: nls.localize('joycoder.getLoginInfo', "获取登录信息"),
				original: 'Get Login Information'
			},
			f1: true
		});
	}

	async run(accessor: ServicesAccessor): Promise<any> {
		// 获取登录服务并获取登录信息
		const loginService = accessor.get(ILoginService);
		return loginService.getLoginInfo();
	}
});

// 注册登出命令
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: LOGOUT_ACTION_ID,
			title: {
				value: nls.localize('joycoder.logout', "退出登录"),
				original: 'Logout Account'
			},
			f1: true
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		// 获取登录服务并调用登出方法
		const loginService = accessor.get(ILoginService);
		await loginService.logout();
	}
});

// 初始化登录状态监听
export class LoginStatusTracker extends Disposable {
	private readonly notLoggedInKey: any;
	protected readonly loginService: ILoginService;

	private _isLoggedIn: boolean | undefined;

	constructor(
		@IContextKeyService private readonly contextKeyService: IContextKeyService,
		@ILoginService loginService: ILoginService,
		@INotificationService private readonly notificationService?: INotificationService
	) {
		super();

		// 存储服务引用
		this.loginService = loginService;

		// 创建上下文键实例
		this.notLoggedInKey = NOT_LOGGED_IN_CONTEXT_KEY.bindTo(this.contextKeyService);

		// 初始化登录状态
		this.initializeLoginStatus();

		// 监听登录状态变化
		this._register(this.loginService.onDidChangeLoginStatus((isLoggedIn) => {
			this.updateLoginStatus(isLoggedIn);
			let message = isLoggedIn ? '登录成功' : '登出成功';
			let severity = isLoggedIn ? 1 : 2;
			const option = {
				severity,
				message,
				sticky: false // 设置为非固定，允许自动消失
			}
			if (this._isLoggedIn === true && isLoggedIn === false) {
				this.notificationService?.notify(option);
			}

			if (this._isLoggedIn === false && isLoggedIn === true) {
				this.notificationService?.notify(option);
			}

			this._isLoggedIn = isLoggedIn;
		}));
	}

	private async initializeLoginStatus(): Promise<void> {
		try {
			// 检查登录状态
			const isLoggedIn = await this.loginService.isLoggedIn();

			// 根据登录状态设置上下文键
			this.notLoggedInKey.set(!isLoggedIn); // 未登录时为true，已登录时为false
		} catch (error) {
			this.notLoggedInKey.set(true); // 出错时默认为未登录状态

			// 如果有通知服务，显示错误
			if (this.notificationService) {
				this.notificationService.error('检查登录状态失败，请刷新页面或重启应用');
			}
		}
	}

	// 更新登录状态，可以直接传入状态或者重新查询
	private async updateLoginStatus(knownStatus?: boolean): Promise<void> {
		try {
			// 如果提供了状态，直接使用，否则查询
			const isLoggedIn = knownStatus !== undefined ? knownStatus : await this.loginService.isLoggedIn();
			const userInfo = await this.loginService.getLoginInfo();
			for (const [callbackId, callback] of callbackMap.entries()) {
				if (typeof callback === 'function') {
					try {
						// 执行回调函数
						callback({
							userInfo: userInfo,
							isLoggedIn,
						});
						console.log('扩展回调callbackId：', callbackId);
						// 执行完后从Map中移除，移除以后无法监听其他扩展的退出登录事件，暂时先不移除
						// !isLoggedIn && callbackMap.delete(callbackId);
					} catch (error) {
						console.error('执行登出回调函数失败:', error);
					}
				}
			}
			// 根据登录状态设置上下文键
			this.notLoggedInKey.set(!isLoggedIn); // 未登录时为true，已登录时为false
		} catch (error) {
			console.error('更新登录状态出错:', error);
			this.notLoggedInKey.set(true); // 出错时默认为未登录状态
		}
	}

	// 手动强制设置为未登录状态，用于处理特殊情况
	public forceSetNotLoggedIn(): void {
		this.notLoggedInKey.set(true);
	}

	// 手动强制设置为已登录状态，用于处理特殊情况
	public forceSetLoggedIn(): void {
		this.notLoggedInKey.set(false);
	}
}
