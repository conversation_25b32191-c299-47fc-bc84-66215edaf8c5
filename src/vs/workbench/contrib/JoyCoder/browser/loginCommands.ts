/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { CommandsRegistry } from '../../../../platform/commands/common/commands.js';
import { ILoginService } from '../../../../platform/login/common/login.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { IS_LOGGED_IN_COMMAND_ID, GET_LOGIN_INFO_COMMAND_ID, LOGOUT_ACTION_ID, callbackMap } from './loginAction.js';



// 注册登录命令
export function registerLoginCommands() {
	// 注意：登录命令(LOGIN_ACTION_ID)已经在loginAction.ts中通过registerAction2注册
	// 不要重复注册，否则会抛出错误

	// 检查登录状态命令
	CommandsRegistry.registerCommand(IS_LOGGED_IN_COMMAND_ID, async (accessor: ServicesAccessor, callbackId?: string) => {
		const loginService = accessor.get(ILoginService);
		const commandService = accessor.get(ICommandService);
		// 存储回调ID和对应的函数
		if (callbackId) {
			// 只需要存储callbackId，登出时会使用它来执行命令
			callbackMap.set(callbackId, async (params: any) => {
				// 参数会传递给joycoder.callback.${callbackId}命令
				console.log(`登出时执行回调，callbackId: ${callbackId}`, params);
				try {
					await commandService.executeCommand(`${callbackId}`, params);
				} catch (cmdError) {
					console.error(`执行命令 ${callbackId} 失败:`, cmdError);
				}
			});
		}
		return loginService.isLoggedIn();
	});


	// 获取登录信息命令
	CommandsRegistry.registerCommand(GET_LOGIN_INFO_COMMAND_ID, async (accessor: ServicesAccessor) => {
		const loginService = accessor.get(ILoginService);
		return loginService.getLoginInfo();
	});

	// 登出命令
	CommandsRegistry.registerCommand(LOGOUT_ACTION_ID, async (accessor: ServicesAccessor) => {
		const loginService = accessor.get(ILoginService);
		return loginService.logout();
	});
}
