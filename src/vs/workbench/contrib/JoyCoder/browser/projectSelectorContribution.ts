/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { IWorkbenchContribution, registerWorkbenchContribution2, WorkbenchPhase } from '../../../common/contributions.js';
import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { ProjectSelectorControl } from './projectSelector.js';
import { registerAction2, Action2 } from '../../../../platform/actions/common/actions.js';
import { Categories } from '../../../../platform/action/common/actionCommonCategories.js';
import { localize } from '../../../../nls.js';
import { isWindows } from '../../../../base/common/platform.js';

// 全局变量，用于跟踪项目选择器是否已经创建
let projectSelectorCreated = false;
let projectSelectorElement: HTMLElement | null = null;

/**
 * 项目选择器贡献
 *
 * 这个类负责在标题栏中添加项目选择器
 */
export class ProjectSelectorContribution extends Disposable implements IWorkbenchContribution {

    private projectSelectorControl: ProjectSelectorControl | null = null;

    constructor(
        @IInstantiationService private readonly instantiationService: IInstantiationService
    ) {
        super();

        // 检查全局变量，确保项目选择器只创建一次
        if (projectSelectorCreated) {
            return;
        }

        // 标记项目选择器已创建
        projectSelectorCreated = true;

        // 使用延迟初始化，确保DOM已经加载
        this.waitForTitlebar();

        // 注册命令
        this.registerCommands();
    }

    /**
     * 等待标题栏元素的出现
     */
    private waitForTitlebar(): void {

        // 尝试使用requestAnimationFrame来等待DOM加载
        const checkTitlebar = () => {
            const titlebarContainer = document.querySelector('.titlebar-container') as HTMLElement;
            if (titlebarContainer) {
                this.initProjectSelector();
                return;
            }
            requestAnimationFrame(checkTitlebar);
        };

        // 开始检查
        requestAnimationFrame(checkTitlebar);
    }

    /**
     * 初始化项目选择器
     */
    private initProjectSelector(): void {
        // 查找标题栏容器
        const titlebarContainer = document.querySelector('.titlebar-container') as HTMLElement;
        if (!titlebarContainer) {
            console.error('找不到标题栏容器，无法添加项目选择器');
            return;
        }

        // 查找或创建标题栏中间容器
        let titlebarCenter = titlebarContainer.querySelector('.titlebar-center') as HTMLElement;
        if (!titlebarCenter) {
            titlebarCenter = document.createElement('div');
            titlebarCenter.className = 'titlebar-center';
            titlebarCenter.style.display = 'flex';
            titlebarCenter.style.alignItems = 'center';
            titlebarCenter.style.height = '100%';
            titlebarCenter.style.justifyContent = 'center';
            titlebarContainer.appendChild(titlebarCenter);
        }

        // 创建一个专门的容器来放置项目选择器
        const selectorContainer = document.createElement('div');
        selectorContainer.className = 'project-selector-container';
        selectorContainer.style.display = 'flex';
        selectorContainer.style.alignItems = 'center';
        selectorContainer.style.height = '100%';
        // win下需要调整一下位置
        if (isWindows) {
            selectorContainer.style.marginLeft = '-30px';
        }

        // 确保容器不会被用于拖动窗口
        (selectorContainer.style as any)['-webkit-app-region'] = 'no-drag';
        selectorContainer.style.zIndex = '2500';

        // 将容器插入到标题栏中间的第一个位置
        if (titlebarCenter.firstChild) {
            titlebarCenter.insertBefore(selectorContainer, titlebarCenter.firstChild);
        } else {
            titlebarCenter.appendChild(selectorContainer);
        }

        // 创建项目选择器并添加到容器
        // 只传递 container（和可选 hoverDelegate），禁止多传服务依赖参数，否则会与依赖注入冲突！
        this.projectSelectorControl = this.instantiationService.createInstance(
            ProjectSelectorControl,
            selectorContainer
        );

        // 保存项目选择器元素的引用
        if (this.projectSelectorControl) {
            projectSelectorElement = selectorContainer.querySelector('.project-selector') as HTMLElement;

            // 确保项目选择器元素不会被用于拖动窗口
            if (projectSelectorElement) {
                (projectSelectorElement.style as any)['-webkit-app-region'] = 'no-drag';
                projectSelectorElement.style.zIndex = '2500';
                projectSelectorElement.style.pointerEvents = 'auto';
                projectSelectorElement.style.cursor = 'pointer';
            }
        }
    }

    /**
     * 注册命令
     */
    private registerCommands(): void {
        // 注册命令以显示项目选择器
        registerAction2(class ShowProjectSelectorAction extends Action2 {
            constructor() {
                super({
                    id: 'workbench.action.showProjectSelector',
                    title: { value: localize('showProjectSelector', "显示项目选择器"), original: 'Show Project Selector' },
                    category: Categories.View,
                    f1: true
                });
            }

            async run(): Promise<void> {
                // 查找项目选择器元素并点击它
                const element = projectSelectorElement || document.querySelector('.project-selector') as HTMLElement;
                if (element) {
                    element.click();
                }
            }
        });
    }
}

// 定义一个唯一ID
const PROJECT_SELECTOR_CONTRIBUTION_ID = 'workbench.contrib.projectSelector';

// 注册贡献
registerWorkbenchContribution2(
    PROJECT_SELECTOR_CONTRIBUTION_ID,
    ProjectSelectorContribution,
    WorkbenchPhase.BlockRestore
);
