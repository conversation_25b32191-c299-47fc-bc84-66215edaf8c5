/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { useState, useEffect, useRef } from "react";
import { useAccessor } from "../util/services.js";
import { URI } from "../../../../../../../base/common/uri.js";
import { StepHeader } from "./components/StepHeader.js";
import { StepContent } from "./components/StepContent.js";
import { StepButtons } from "./components/StepButtons.js";
import { Icon } from "./components/Icon.js";
import { LOGO_BASE64, ICON_DATA_BASE64, STYLES } from "./source.js";
import { CSSTransition, SwitchTransition } from "react-transition-group";

// 为CSSTransition添加自定义类名
const TRANSITION_CLASSES = {
	enter: "step-enter",
	enterActive: "step-enter-active",
	exit: "step-exit",
	exitActive: "step-exit-active",
};

// 自定义CSS样式，添加到组件中
const TRANSITION_STYLES = `
.step-enter {
  opacity: 0;
  position: absolute;
  width: 100%;
}
.step-enter-active {
  opacity: 1;
  transition: opacity 600ms ease-in-out;
  transition-delay: 50ms;
}
.step-exit {
  opacity: 1;
  position: absolute;
  width: 100%;
}
.step-exit-active {
  opacity: 0;
  transition: opacity 600ms ease-in-out;
}`;

// 响应式样式钩子
const useResponsiveStyles = () => {
	const [windowWidth, setWindowWidth] = useState<number>(
		typeof window !== "undefined" ? window.innerWidth : 1200
	);

	useEffect(() => {
		const handleResize = () => {
			setWindowWidth(window.innerWidth);
		};

		window.addEventListener("resize", handleResize);
		return () => {
			window.removeEventListener("resize", handleResize);
		};
	}, []);

	// 根据窗口大小调整样式
	const responsiveStyles = {
		container: {
			...STYLES.container,
			width: windowWidth < 768 ? "95%" : "85%",
			padding: windowWidth < 768 ? "24px 16px" : "32px",
		},
		logo: {
			...STYLES.logo,
			width: windowWidth < 768 ? "60px" : "80px",
			height: windowWidth < 768 ? "60px" : "80px",
		},
		importButton: {
			...STYLES.importButton,
			fontSize: windowWidth < 768 ? "14px" : "16px",
			padding: windowWidth < 768 ? "8px 16px" : "10px 20px",
		},
	};

	return responsiveStyles;
};

/**
 * 扩展导入API接口
 */
export interface IExtensionImportAPI {
	// API是否可用
	isAvailable: boolean;

	// 获取VSCode扩展目录路径
	getVSCodeExtensionsPath(): Promise<string>;

	// 获取指定路径下的扩展列表
	getExtensionsInPath(extensionsPath: string): Promise<any[]>;

	// 导入扩展
	importExtensions(extensionsPath: string): Promise<{
		succeeded: string[];
		failed: string[];
		notFound: string[];
	}>;

	// 一键导入扩展
	oneClickImport(): Promise<{
		succeeded: string[];
		failed: string[];
		notFound: string[];
	}>;

	// 从上传的文件导入扩展
	importFromFiles(files: File[]): Promise<{
		succeeded: string[];
		failed: string[];
		notFound: string[];
	}>;

	// 导入VS Code设置文件
	importSettings(): Promise<boolean>;

	// 导入VS Code键盘绑定文件
	importKeybindings(): Promise<boolean>;

	// 一键导入所有VS Code配置文件
	importAllConfigurations(): Promise<{
		settings: boolean;
		keybindings: boolean;
	}>;
}

/**
 * 导入步骤枚举
 */
enum ImportStep {
	HOME = 0,
	IMPORT_EXTENSIONS = 1,
	INSTALL_COMMAND = 2,
	COMPLETED = 3,
}

/**
 * 创建新的进度指示器组件
 */
const ProgressDots: React.FC<{
	totalSteps: number;
	currentStep: number;
	onStepClick: (step: number) => void;
}> = ({ totalSteps, currentStep, onStepClick }) => {
	return (
		<div style={STYLES.welcomeProgress}>
			{Array.from({ length: totalSteps }).map((_, index) => (
				<div
					key={index}
					style={{
						...STYLES.progressDot,
						...(currentStep === index ? STYLES.activeDot : {}),
					}}
					onClick={() => onStepClick(index)}
				/>
			))}
		</div>
	);
};

/**
 * 扩展导入区域组件
 */
export const WelcomePage: React.FC<{
	importEnabled: boolean;
	extensionImportAPI?: IExtensionImportAPI;
	onImportExtensions?: (path: string) => void;
	onClose?: () => void;
}> = ({ importEnabled, extensionImportAPI, onImportExtensions, onClose }) => {
	// 获取响应式样式
	const responsiveStyles = useResponsiveStyles();

	// 当前步骤状态
	const [currentStep, setCurrentStep] = useState<ImportStep>(ImportStep.HOME);
	// 添加登录状态
	const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
	const [installationSuccess, setInstallationSuccess] =
		useState<boolean>(false);
	const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);
	const isMounted = useRef<boolean>(true);
	const currentStepRef = useRef(currentStep);
	// 在state更新时同步更新ref
	useEffect(() => {
		currentStepRef.current = currentStep;
	}, [currentStep]);

	// 为每个步骤创建单独的ref
	const nodeRefs = {
		[ImportStep.HOME]: useRef(null),
		[ImportStep.IMPORT_EXTENSIONS]: useRef(null),
		[ImportStep.INSTALL_COMMAND]: useRef(null),
		[ImportStep.COMPLETED]: useRef(null),
	};

	const accessor = useAccessor();
	const comandService = accessor.get("ICommandService");

	// 组件卸载时清理轮询
	useEffect(() => {
		isMounted.current = true;
		return () => {
			isMounted.current = false;
			stopPolling();
		};
	}, []);

	// 获取用户主目录的函数
	const getHomeDirectory = async (accessor: any): Promise<string> => {
		try {
			// 尝试从环境服务获取用户主目录
			const environmentService = accessor.get('IEnvironmentService');
			if (environmentService && environmentService.userHome) {
				return environmentService.userHome.fsPath;
			}

			// 备用方案：使用操作系统服务
			const osService = accessor.get('IOSService');
			if (osService && osService.homeDir) {
				return osService.homeDir;
			}

			// 如果以上方法都失败，使用默认路径
			// 避免使用process.env，因为在浏览器环境中不可用
			const isWindows = isWindowsOS();
			if (isWindows) {
				return 'C:\\Users\\<USER>\\Users\\Administrator';
			} else {
				return '/Users/<USER>';
			}
		}
	};

	// 检测是否为Windows系统的函数
	const isWindowsOS = (): boolean => {
		// 使用更可靠的方法检测Windows系统
		try {
			// 首先尝试使用userAgent
			const userAgent = navigator.userAgent.toLowerCase();
			if (userAgent.indexOf('windows') !== -1) {
				return true;
			}

			// 检测是否为Mac系统
			if (userAgent.indexOf('macintosh') !== -1 || userAgent.indexOf('mac os') !== -1) {
				return false;
			}

			// 检测是否为Linux系统
			if (userAgent.indexOf('linux') !== -1) {
				return false;
			}

			// 如果无法通过userAgent判断，尝试其他方法
			// 检查是否存在Windows特有的对象
			if (typeof window !== 'undefined' && ('ActiveXObject' in window || 'MSInputMethodContext' in window)) {
				return true;
			}

			// 如果无法确定，根据常见的操作系统分布，默认返回false
			// 这样在Mac和Linux环境下会使用正确的命令
			return false;
		} catch (error) {
			// 默认返回false，这样在Mac和Linux环境下会使用正确的命令
			return false;
		}
	};

	// 组件初始化时开始轮询检查命令行是否已安装
	useEffect(() => {
		const fileService = accessor.get("IFileService");

		if (fileService) {
			// 立即检查一次
			checkCliInstallation(fileService).then((isInstalled) => {
				if (isInstalled) {
					setInstallationSuccess(true);
					return; // 如果已安装，不需要启动轮询
				}

				// 如果未安装，启动轮询
				startPollingCliInstallation(fileService);
			});
		}

		// 清理函数
		return () => {
			stopPolling();
		};
	}, []);

	// 导入状态
	const [isImporting, setIsImporting] = useState(false);

	// 检查命令行安装状态的函数
	const checkCliInstallation = async (fileService: any): Promise<boolean> => {
		if (!fileService) return false;

		try {
			// 根据操作系统检查不同的路径
			const isWindows = isWindowsOS();
			let cliPath;

			if (isWindows) {
				// 在Windows系统中，检查环境变量中是否存在joycode.cmd
				// 由于无法直接检查环境变量，我们检查几个可能的安装位置

				// 获取用户主目录
				const homeDir = await getHomeDirectory(accessor);

				// 构建可能的路径列表，包括更多可能的安装位置
				const possiblePaths = [
					"C:\\Windows\\System32\\joycode.cmd",
					"C:\\Program Files\\JoyCode\\bin\\joycode.cmd",
					"C:\\Program Files (x86)\\JoyCode\\bin\\joycode.cmd",
					`${homeDir}\\AppData\\Local\\Programs\\JoyCode\\bin\\joycode.cmd`,
					`${homeDir}\\AppData\\Local\\JoyCode\\bin\\joycode.cmd`,
					`${homeDir}\\JoyCode\\bin\\joycode.cmd`
				];

				// 检查所有可能的路径
				for (const path of possiblePaths) {
					try {
						const exists = await fileService.exists(URI.file(path));
						if (exists) {
							return true;
						}
					} catch (error) {
						console.error(`检查路径出错 (${path}):`, error);
					}
				}

				// 如果没有找到，返回false
				return false;
			} else {
				// macOS系统检查/usr/local/bin/joycode
				cliPath = "/usr/local/bin/joycode";
				const exists = await fileService.exists(URI.file(cliPath));
				return exists;
			}
		} catch (error) {
			console.error("检查命令行工具时出错:", error);
			return false;
		}
	};

	// 停止轮询函数
	const stopPolling = () => {
		if (pollIntervalRef.current) {
			clearInterval(pollIntervalRef.current);
			pollIntervalRef.current = null;
		}
	};

	// 启动轮询检查安装状态
	const startPollingCliInstallation = (fileService: any) => {
		// 清除可能存在的轮询
		stopPolling();

		// 记录轮询开始时间
		const startTime = Date.now();
		// 最大轮询时间（30秒）
		const maxPollingTime = 30000;
		// 轮询次数计数
		let pollCount = 0;

		// 开始新的轮询
		pollIntervalRef.current = setInterval(async () => {
			// 检查组件是否已卸载
			if (!isMounted.current) {
				stopPolling();
				return;
			}

			// 增加轮询计数
			pollCount++;

			// 检查是否超过最大轮询时间
			const elapsedTime = Date.now() - startTime;
			if (elapsedTime > maxPollingTime) {

				// 获取通知服务
				const notificationService = accessor.get('INotificationService');
				if (notificationService) {
					notificationService.info('命令行工具安装可能需要重启应用程序才能生效。如果安装后无法使用，请尝试重启JoyCode。');
				}

				// 停止轮询，但不自动进入下一步
				stopPolling();
				return;
			}

			// 检查安装状态
			const isInstalled = await checkCliInstallation(fileService);

			if (isInstalled) {
				// 安装成功
				setInstallationSuccess(true);

				// 获取通知服务
				const notificationService = accessor.get('INotificationService');
				if (notificationService) {
					notificationService.info('命令行工具安装成功！');
				}

				if (currentStepRef.current === ImportStep.INSTALL_COMMAND) {
					goToNextStep();
				}
				// 停止轮询
				stopPolling();
			}
		}, 1000); // 每秒检查一次
	};

	// 处理安装命令行工具的方法
	const handleInstallCommandLine = async () => {
		// 检查命令服务是否可用
		if (!comandService) {
			console.error("命令服务不可用");
			return;
		}

		try {
			// 获取通知服务
			const notificationService = accessor.get('INotificationService');

			// 根据操作系统执行不同的安装命令
			const isWindows = isWindowsOS();

			// 显示安装中通知
			if (notificationService) {
				notificationService.info('正在安装命令行工具，请稍候...');
			}

			if (isWindows) {
				// Windows系统使用Windows专用命令
				await comandService.executeCommand("workbench.action.installWindowsCommandLine");
			} else {
				// macOS系统使用macOS专用命令
				await comandService.executeCommand("workbench.action.installCommandLine");
			}

			// 获取文件服务
			const fileService = accessor.get("IFileService");
			if (fileService) {
				// 立即重新启动轮询检查
				startPollingCliInstallation(fileService);
			}
		} catch (error) {
			console.error("安装命令行工具失败:", error);

			// 获取通知服务
			const notificationService = accessor.get('INotificationService');
			if (notificationService) {
				// 提供更详细的错误信息
				const isWindows = isWindowsOS();
				if (isWindows) {
					notificationService.error('安装命令行工具失败，请重试或联系技术支持。');
				} else {
					notificationService.error('安装命令行工具失败，请确保您有足够的权限安装到 /usr/local/bin 目录。');
				}
			}
		}
	};

	// 一键导入VS Code扩展（自动检测）
	const handleOneClickImport = async () => {
		if (!extensionImportAPI || !extensionImportAPI.oneClickImport) {
			return;
		}

		try {
			// 设置导入状态
			setIsImporting(true);

			// 先检查VS Code是否已安装
			if (extensionImportAPI.getVSCodeExtensionsPath) {
				const vscodeExtPath = await extensionImportAPI.getVSCodeExtensionsPath();

				// 检查VS Code配置目录是否存在
				const vscodeUserConfigPath = vscodeExtPath.replace(/\/extensions$/, '');
				const settingsPath = `${vscodeUserConfigPath}/settings.json`;
				const keybindingsPath = `${vscodeUserConfigPath}/keybindings.json`;

				// 使用URI检查文件是否存在
				const fileService = accessor.get('IFileService');
				if (fileService) {
					const settingsExists = await fileService.exists(URI.file(settingsPath));
					const keybindingsExists = await fileService.exists(URI.file(keybindingsPath));
					const extensionsExists = await fileService.exists(URI.file(vscodeExtPath));

					// 如果所有文件都不存在，提示未安装VS Code
					if (!settingsExists && !keybindingsExists && !extensionsExists) {
						const notificationService = accessor.get('INotificationService');
						if (notificationService) {
							notificationService.info('未检测到VS Code，无需导入配置');
						}

						// 直接跳过导入步骤
						setIsImporting(false);
						if (currentStep === ImportStep.IMPORT_EXTENSIONS) {
							goToNextStep();
						}
						return;
					}
				}
			}

			// 调用导入扩展API
			await extensionImportAPI.oneClickImport();

			// 同时导入配置文件
			if (extensionImportAPI.importAllConfigurations) {
				try {
					const configResult =
						await extensionImportAPI.importAllConfigurations();
				} catch (configError) {
					console.error("配置文件导入失败:", configError);
				}
			}
		} catch (error) {
			console.error("导入失败:", error);
		} finally {
			setIsImporting(false);
			if (currentStep === ImportStep.IMPORT_EXTENSIONS) {
				goToNextStep();
			}
		}
	};

	// 直接跳转到特定步骤
	const goToStep = (step: ImportStep) => {
		setCurrentStep(step);
	};

	// 处理步骤导航
	const goToNextStep = () => {
		setCurrentStep((prevStep) => {
			const nextStep = prevStep + 1;
			return nextStep < Object.keys(ImportStep).length / 2
				? nextStep
				: prevStep;
		});
	};

	// 跳过导入步骤
	const handleSkipImport = () => {
		setCurrentStep(ImportStep.INSTALL_COMMAND);
	};

	// 完成引导
	const handleComplete = () => {
		// 获取命令服务
		const commandService = accessor.get('ICommandService');

		if (commandService) {
			// 打开侧边栏并激活 JoyCoder-left-view 视图
			try {
				// 使用现有的命令打开侧边栏
				commandService.executeCommand('joycoder.joycoder.showAuxiliaryBar')
			} catch (error) {
				console.error('尝试激活侧边栏时出错');
			}
		}

		// 关闭欢迎页面
		if (onClose) {
			onClose();
		}
	};

	// 检查登录状态
	useEffect(() => {
		const checkLoginStatus = async () => {
			const loginService = accessor.get("ILoginService");
			if (loginService) {
				const loginStatus = await loginService.isLoggedIn();
				setIsLoggedIn(loginStatus);
			}
		};
		checkLoginStatus();
	}, []);

	// 监听登录状态变化
	useEffect(() => {
		const loginService = accessor.get("ILoginService");
		if (loginService) {
			const disposable = loginService.onDidChangeLoginStatus((status) => {
				setIsLoggedIn(status);
				// 如果用户已登录，自动关闭欢迎页面
				if (status) {
					handleComplete();
				}
			});

			// 组件卸载时取消订阅
			return () => {
				disposable.dispose();
			};
		}
	}, []); // 改为空数组，只在组件挂载时执行一次

	const loginIDE = async () => {
		const loginService = accessor.get("ILoginService");
		if (loginService) {
			const loginStatus = await loginService.isLoggedIn();
			if (!loginStatus) {
				loginService.login();
			} else {
				handleComplete();
			}
		}
	};

	// 渲染首页步骤
	const renderHomeStep = () => {
		return (
			<StepContent>
				<img
					src={LOGO_BASE64}
					alt="JoyCode Logo"
					style={responsiveStyles.logo}
				/>
				<StepHeader title="Hi，JoyCode ！" />
				<StepButtons onNext={goToNextStep} showPrevious={false} />
			</StepContent>
		);
	};

	// 渲染安装命令行步骤
	const renderInstallCommandStep = () => {
		return (
			<StepContent>
				<img
					src={LOGO_BASE64}
					alt="JoyCode Logo"
					style={responsiveStyles.logo}
				/>
				<StepHeader
					title="注册命令行"
					subtitle={
						installationSuccess
							? "您已经注册了joycode命令行"
							: "在 Terminal 中使用 'joycode' 命令行"
					}
				/>
				{!installationSuccess && (
					<>
						<div>
							<button
								style={{
									...STYLES.importButtonPrimary,
									display: "flex",
									alignItems: "center",
									justifyContent: "center",
								}}
								onClick={handleInstallCommandLine}
								onMouseEnter={(e) => {
									(e.target as HTMLButtonElement).style.background =
										"rgba(36, 123, 255, 0.8)";
								}}
								onMouseLeave={(e) => {
									(e.target as HTMLButtonElement).style.background =
										"rgba(36, 123, 255, 1)";
								}}
							>
								安装 'joycode' 命令行
							</button>
						</div>
					</>
				)}
				<div style={STYLES.skipButtonContainer}>
					<button style={STYLES.skipButton} onClick={() => goToNextStep()}>
						{installationSuccess ? "下一步" : "跳过"}
					</button>
				</div>
			</StepContent>
		);
	};

	// 将ImportAnimation修改为React组件
	const ImportAnimation: React.FC<{ status: boolean }> = ({ status }) => {
		const [activeIndex, setActiveIndex] = useState<number>(-1);
		const [completed, setCompleted] = useState(false);
		const totalArrows = 6;

		// 动画效果
		useEffect(() => {
			if (status) {
				// 重置状态
				setCompleted(false);

				// 箭头移动效果的间隔时间
				const intervalTime = 150;
				let currentIndex = 0;

				// 创建箭头移动效果
				const intervalId = setInterval(() => {
					// 只高亮一个箭头，从左到右移动
					setActiveIndex(currentIndex);

					// 移动到下一个箭头
					currentIndex = (currentIndex + 1) % totalArrows;
				}, intervalTime);

				return () => {
					clearInterval(intervalId);
					// 重置状态
					setActiveIndex(-1);
				};
			} else if (completed) {
				// 如果已经完成导入但状态变为false，重置completed状态
				setCompleted(false);
				setActiveIndex(-1);
			} else if (activeIndex >= 0 && !status) {
				// 导入结束后，停止在当前位置
				setCompleted(true);
			}
		}, [status]);

		return (
			<div style={STYLES.animationContainer}>
				{Array.from({ length: totalArrows }).map((_, index) => (
					<div
						key={index}
						style={{
							...STYLES.progressColumn,
							...(index === activeIndex || (completed && index <= activeIndex)
								? STYLES.progressColumnActive
								: {}),
						}}
					/>
				))}
			</div>
		);
	};

	// 渲染导入扩展步骤
	const renderImportStep = () => {
		return (
			<StepContent>
				<div style={STYLES.importStepIconContainer}>
					<Icon src={ICON_DATA_BASE64} size={80} />
					<ImportAnimation status={isImporting} />
					<Icon src={LOGO_BASE64} size={80} />
				</div>

				<div style={STYLES.importTitleContainer}>
					<h2 style={STYLES.importTitle}>导入配置</h2>
				</div>

				<div style={STYLES.importButtonContainer}>
					<button
						style={{
							...STYLES.importButtonPrimary,
							...(isImporting ? STYLES.importButtonDisabled : {}),
						}}
						onClick={handleOneClickImport}
						disabled={isImporting || !extensionImportAPI?.oneClickImport}
						onMouseEnter={(e) => {
							if (!isImporting && extensionImportAPI?.oneClickImport) {
								(e.target as HTMLButtonElement).style.background =
									"rgba(36, 123, 255, 0.8)";
							}
						}}
						onMouseLeave={(e) => {
							if (!isImporting && extensionImportAPI?.oneClickImport) {
								(e.target as HTMLButtonElement).style.background =
									"rgba(36, 123, 255, 1)";
							}
						}}
					>
						{isImporting ? "导入中..." : "从VS Code导入"}
					</button>
				</div>
				<div style={STYLES.skipButtonContainer}>
					<button style={STYLES.skipButton} onClick={handleSkipImport}>
						跳过
					</button>
				</div>
			</StepContent>
		);
	};

	// 渲染完成步骤
	const renderCompletedStep = () => {
		interface PrimaryButtonProps {
			onClick: () => void;
			children: React.ReactNode;
		}

		const PrimaryButton: React.FC<PrimaryButtonProps> = ({
			onClick,
			children,
		}) => (
			<button
				style={{
					...STYLES.importButtonPrimary,
					display: "flex",
					alignItems: "center",
					justifyContent: "center",
				}}
				onClick={onClick}
				onMouseEnter={(e) => {
					(e.target as HTMLButtonElement).style.background =
						"rgba(36, 123, 255, 0.8)";
				}}
				onMouseLeave={(e) => {
					(e.target as HTMLButtonElement).style.background =
						"rgba(36, 123, 255, 1)";
				}}
			>
				{children}
			</button>
		);

		return (
			<StepContent>
				<img
					src={LOGO_BASE64}
					alt="JoyCode Logo"
					style={responsiveStyles.logo}
				/>
				<StepHeader
					title={isLoggedIn ? "欢迎回来" : "登录 JoyCode"}
					subtitle={
						isLoggedIn
							? "您已成功登录 JoyCode，让我们开始愉快的编程之旅吧！"
							: "为了给您提供 AI 辅助编程能力，需要您登录 JoyCode"
					}
				/>
				{!isLoggedIn ? (
					<>
						<div>
							<PrimaryButton onClick={() => loginIDE()}>登录</PrimaryButton>
						</div>
						<div style={STYLES.skipButtonContainer}>
							<button style={STYLES.skipButton} onClick={handleComplete}>
								跳过
							</button>
						</div>
					</>
				) : (
					<div>
						<PrimaryButton onClick={handleComplete}>开始使用</PrimaryButton>
					</div>
				)}
			</StepContent>
		);
	};

	// 渲染导入区域
	return (
		<div style={STYLES.fullscreenContainer}>
			{/* 添加过渡动画的CSS */}
			<style>{TRANSITION_STYLES}</style>
			{/* <div style={STYLES.backdrop}></div> */}
			<div style={responsiveStyles.container}>
				{/* 步骤标题和指示器 */}
				<div style={{ textAlign: "center", position: "relative" }}>
					<ProgressDots
						totalSteps={Object.keys(ImportStep).length / 2}
						currentStep={currentStep}
						onStepClick={goToStep}
					/>
				</div>

				{/* 内容区域 - 使用SwitchTransition实现先消失再出现的效果 */}
				<div style={STYLES.transitionContainer}>
					<SwitchTransition mode="out-in">
						<CSSTransition
							key={currentStep}
							timeout={{
								enter: 650, // 600ms动画 + 50ms延迟
								exit: 600, // 600ms动画
							}}
							classNames={TRANSITION_CLASSES}
							nodeRef={nodeRefs[currentStep]}
						>
							<div ref={nodeRefs[currentStep]} style={STYLES.stepTransition}>
								{currentStep === ImportStep.HOME && renderHomeStep()}
								{currentStep === ImportStep.IMPORT_EXTENSIONS &&
									renderImportStep()}
								{currentStep === ImportStep.INSTALL_COMMAND &&
									renderInstallCommandStep()}
								{currentStep === ImportStep.COMPLETED && renderCompletedStep()}
							</div>
						</CSSTransition>
					</SwitchTransition>
				</div>
			</div>
		</div>
	);
};
