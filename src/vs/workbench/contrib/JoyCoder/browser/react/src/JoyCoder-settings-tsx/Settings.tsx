/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, {
	useCallback,
	useEffect,
	useMemo,
	useRef,
	useState,
} from "react";
import { InputBox } from "../../../../../../../base/browser/ui/inputbox/inputBox.js";
import {
	ProviderName,
	SettingName,
	displayInfoOfSettingName,
	providerN<PERSON>s,
	JoyCoderModelInfo,
	featureFlagNames,
	displayInfoOfFeatureFlag,
	customSettingNamesOfProvider,
	// RefreshableProviderName,
	// refreshableProviderNames,
	displayInfoOfProviderName,
	defaultProviderSettings,
} from "../../../../../../../platform/JoyCoder/common/joyCoderSettingsTypes.js";
import ErrorBoundary from "../sidebar-tsx/ErrorBoundary.js";
import {
	JoyCoderInputBox,
	JoyCoderSelectBox,
	JoyCoderSwitch,
} from "../util/inputs.js";
import {
	useAccessor,
	useIsDark,
	useSettingsState,
} from "../util/services.js";
import { X, Check } from "lucide-react";
import { ChatMarkdownRender } from "../markdown/ChatMarkdownRender.js";

// CSS变量定义
const styles = {
	container: {
		backgroundColor: "var(--vscode-editor-background)",
		padding: "1.5rem",
		borderRadius: "8px",
		marginBottom: "1.5rem",
		boxShadow: "0 2px 8px rgba(0, 0, 0, 0.15)",
		border: "1px solid var(--vscode-editorWidget-border)",
	},
	header: {
		display: "flex",
		alignItems: "center",
		gap: "0.5rem",
		marginBottom: "1rem",
	},
	title: {
		fontSize: "1.5rem",
		fontWeight: "600",
		color: "var(--vscode-foreground)",
	},
	infoText: {
		color: "var(--vscode-descriptionForeground)",
		marginBottom: "1.5rem",
	},
	userName: {
		color: "var(--vscode-foreground)",
		fontWeight: "500",
	},
	buttonContainer: {
		display: "flex",
		gap: "0.5rem",
	},
	button: {
		display: "flex",
		alignItems: "center",
		gap: "0.5rem",
		backgroundColor: "var(--vscode-button-background)",
		color: "var(--vscode-button-foreground)",
		padding: "0.5rem 1rem",
		borderRadius: "4px",
		border: "none",
		cursor: "pointer",
		fontWeight: "500",
		transition: "background-color 0.2s",
	},
	buttonHover: {
		backgroundColor: "var(--vscode-button-hoverBackground)",
	},
	icon: {
		width: "1rem",
		height: "1rem",
	}
};

const SubtleButton = ({
	onClick,
	text,
	icon,
	disabled,
}: {
	onClick: () => void;
	text: string;
	icon: React.ReactNode;
	disabled: boolean;
}) => {
	return (
		<div className="flex items-center px-3 rounded-sm overflow-hidden gap-2 hover:bg-black/10 dark:hover:bg-gray-300/10">
			<button
				className="flex items-center"
				disabled={disabled}
				onClick={onClick}
			>
				{icon}
			</button>
			<span className="opacity-50">{text}</span>
		</div>
	);
};

// Removed RefreshModelButton and RefreshableModels components

const AddModelMenu = ({ onSubmit }: { onSubmit: () => void }) => {
	const accessor = useAccessor();
	const settingsStateService = accessor.get("IJoyCoderSettingsService");

	const settingsState = useSettingsState();

	const providerNameRef = useRef<ProviderName | null>(null);
	const modelNameRef = useRef<string | null>(null);

	const [errorString, setErrorString] = useState("");

	const providerOptions = useMemo(
		() =>
			providerNames.map((providerName) => ({
				text: displayInfoOfProviderName(providerName).title,
				value: providerName,
			})),
		[providerNames]
	);

	return (
		<>
			<div className="flex items-center gap-4">
				{/* provider */}
				<div className="max-w-40 w-full border border-vscode-editorwidget-border">
					<JoyCoderSelectBox
						onCreateInstance={useCallback(() => {
							providerNameRef.current = providerOptions[0].value;
						}, [providerOptions])} // initialize state
						onChangeSelection={useCallback((providerName: ProviderName) => {
							providerNameRef.current = providerName;
						}, [])}
						options={providerOptions}
					/>
				</div>

				{/* model */}
				<div className="max-w-40 w-full border border-vscode-editorwidget-border">
					<JoyCoderInputBox
						placeholder="Model Name"
						onChangeText={useCallback((modelName) => {
							modelNameRef.current = modelName;
						}, [])}
						multiline={false}
					/>
				</div>

				{/* button */}
				<div className="max-w-40">
					<button
						className="px-3 py-1 bg-black/10 dark:bg-gray-200/10 rounded-sm overflow-hidden"
						onClick={() => {
							const providerName = providerNameRef.current;
							const modelName = modelNameRef.current;

							if (providerName === null) {
								setErrorString("Please select a provider.");
								return;
							}
							if (!modelName) {
								setErrorString("Please enter a model name.");
								return;
							}
							// if model already exists here
							if (
								settingsState.settingsOfProvider[providerName].models.find(
									(m) => m.modelName === modelName
								)
							) {
								setErrorString(
									`This model already exists under ${providerName}.`
								);
								return;
							}

							settingsStateService.addModel(providerName, modelName);
							onSubmit();
						}}
					>
						Add model
					</button>
				</div>

				{!errorString ? null : (
					<div className="text-red-500 truncate whitespace-nowrap">
						{errorString}
					</div>
				)}
			</div>
		</>
	);
};

const AddModelMenuFull = () => {
	const [open, setOpen] = useState(false);

	return (
		<div className="hover:bg-black/10 dark:hover:bg-gray-300/10 py-1 my-4 pb-1 px-3 rounded-sm overflow-hidden ">
			{open ? (
				<AddModelMenu
					onSubmit={() => {
						setOpen(false);
					}}
				/>
			) : (
				<button
					className="px-3 py-1 bg-black/10 dark:bg-gray-200/10 rounded-sm overflow-hidden"
					onClick={() => setOpen(true)}
				>
					Add Model
				</button>
			)}
		</div>
	);
};

export const ModelDump = () => {
	const accessor = useAccessor();
	const settingsStateService = accessor.get("IJoyCoderSettingsService");

	const settingsState = useSettingsState();

	// a dump of all the enabled providers' models
	const modelDump: (JoyCoderModelInfo & {
		providerName: ProviderName;
		providerEnabled: boolean;
	})[] = [];
	for (let providerName of providerNames) {
		const providerSettings = settingsState.settingsOfProvider[providerName];
		// if (!providerSettings.enabled) continue
		modelDump.push(
			...providerSettings.models.map((model) => ({
				...model,
				providerName,
				providerEnabled: !!providerSettings._enabled,
			}))
		);
	}

	// sort by hidden
	modelDump.sort((a, b) => {
		return Number(b.providerEnabled) - Number(a.providerEnabled);
	});

	return (
		<div className="">
			{modelDump.map((m, i) => {
				const {
					isHidden,
					isDefault,
					isAutodetected,
					modelName,
					providerName,
					providerEnabled,
				} = m;

				const isNewProviderName =
					(i > 0 ? modelDump[i - 1] : undefined)?.providerName !== providerName;

				const disabled = !providerEnabled;

				return (
					<div
						key={`${modelName}${providerName}`}
						className={`flex items-center justify-between gap-4 hover:bg-black/10 dark:hover:bg-gray-300/10 py-1 px-3 rounded-sm overflow-hidden cursor-default truncate ${
							isNewProviderName ? "mt-4" : ""
						}`}
					>
						{/* left part is width:full */}
						<div className={`w-full flex items-center gap-4`}>
							<span className="min-w-40">
								{isNewProviderName
									? displayInfoOfProviderName(providerName).title
									: ""}
							</span>
							<span>{modelName}</span>
							{/* <span>{`${modelName} (${providerName})`}</span> */}
						</div>
						{/* right part is anything that fits */}
						<div className="w-fit flex items-center gap-4">
							<span className="opacity-50">
								{isAutodetected
									? "(detected locally)"
									: isDefault
									? ""
									: "(custom model)"}
							</span>

							<JoyCoderSwitch
								value={disabled ? false : !isHidden}
								onChange={() => {
									settingsStateService.toggleModelHidden(
										providerName,
										modelName
									);
								}}
								disabled={disabled}
								size="sm"
							/>

							<div className={`w-5 flex items-center justify-center`}>
								{isDefault ? null : (
									<button
										onClick={() => {
											settingsStateService.deleteModel(providerName, modelName);
										}}
									>
										<X className="size-4" />
									</button>
								)}
							</div>
						</div>
					</div>
				);
			})}
		</div>
	);
};

// providers

const ProviderSetting = ({
	providerName,
	settingName,
}: {
	providerName: ProviderName;
	settingName: SettingName;
}) => {
	// const { title: providerTitle, } = displayInfoOfProviderName(providerName)

	const {
		title: settingTitle,
		placeholder,
		subTextMd,
	} = displayInfoOfSettingName(providerName, settingName);

	const accessor = useAccessor();
	const joyCoderSettingsService = accessor.get("IJoyCoderSettingsService");

	let weChangedTextRef = false;

	return (
		<ErrorBoundary>
			<div className="my-1">
				<JoyCoderInputBox
					// placeholder={`${providerTitle} ${settingTitle} (${placeholder}).`}
					placeholder={`${settingTitle} (${placeholder}).`}
					onChangeText={useCallback(
						(newVal) => {
							if (weChangedTextRef) return;
							joyCoderSettingsService.setSettingOfProvider(
								providerName,
								settingName,
								newVal
							);
						},
						[joyCoderSettingsService, providerName, settingName]
					)}
					// we are responsible for setting the initial value. always sync the instance whenever there's a change to state.
					onCreateInstance={useCallback(
						(instance: InputBox) => {
							const syncInstance = () => {
								const settingsAtProvider =
									joyCoderSettingsService.state.settingsOfProvider[
										providerName
									];
								const stateVal = settingsAtProvider[settingName as SettingName];

								// console.log('SYNCING TO', providerName, settingName, stateVal)
								weChangedTextRef = true;
								instance.value = stateVal as string;
								weChangedTextRef = false;

								const isEverySettingPresent = Object.keys(
									defaultProviderSettings[providerName]
								).every((key) => {
									return !!settingsAtProvider[
										key as keyof typeof settingsAtProvider
									];
								});

								const shouldEnable =
									isEverySettingPresent && !settingsAtProvider._enabled; // enable if all settings are present and not already enabled
								const shouldDisable =
									!isEverySettingPresent && settingsAtProvider._enabled;

								if (shouldEnable) {
									joyCoderSettingsService.setSettingOfProvider(
										providerName,
										"_enabled",
										true
									);
								}

								if (shouldDisable) {
									joyCoderSettingsService.setSettingOfProvider(
										providerName,
										"_enabled",
										false
									);
								}
							};
							syncInstance();
							const disposable =
								joyCoderSettingsService.onDidChangeState(syncInstance);
							return [disposable];
						},
						[joyCoderSettingsService, providerName, settingName]
					)}
					multiline={false}
				/>
				{subTextMd === undefined ? null : (
					<div className="py-1 px-3 opacity-50 text-xs">
						<ChatMarkdownRender string={subTextMd} />
					</div>
				)}
			</div>
		</ErrorBoundary>
	);
};

const SettingsForProvider = ({
	providerName,
}: {
	providerName: ProviderName;
}) => {
	// const voidSettingsState = useSettingsState()
	// const accessor = useAccessor()
	// const joyCoderSettingsService = accessor.get('IJoyCoderSettingsService')

	// const { enabled } = voidSettingsState.settingsOfProvider[providerName]
	const settingNames = customSettingNamesOfProvider(providerName);

	const { title: providerTitle } = displayInfoOfProviderName(providerName);

	return (
		<div className="my-4">
			<div className="flex items-center w-full gap-4">
				<h3 className="text-xl truncate">{providerTitle}</h3>

				{/* enable provider switch */}
				{/* <JoyCoderSwitch
				value={!!enabled}
				onChange={
					useCallback(() => {
						const enabledRef = joyCoderSettingsService.state.settingsOfProvider[providerName].enabled
						joyCoderSettingsService.setSettingOfProvider(providerName, 'enabled', !enabledRef)
					}, [joyCoderSettingsService, providerName])}
				size='sm+'
			/> */}
			</div>

			<div className="px-0">
				{/* settings besides models (e.g. api key) */}
				{settingNames.map((settingName, i) => {
					return (
						<ProviderSetting
							key={settingName}
							providerName={providerName}
							settingName={settingName}
						/>
					);
				})}
			</div>
		</div>
	);
};

export const JoyCoderProviderSettings = ({
	providerNames,
}: {
	providerNames: ProviderName[];
}) => {
	return (
		<>
			{providerNames.map((providerName) => (
				<SettingsForProvider key={providerName} providerName={providerName} />
			))}
		</>
	);
};

// export const JoyCoderFeatureFlagSettings = () => {
// 	const accessor = useAccessor()
// 	const joyCoderSettingsService = accessor.get('IJoyCoderSettingsService')

// 	const voidSettingsState = useSettingsState()

// 	return <>
// 		{featureFlagNames.map((flagName) => {
// 			const value = voidSettingsState.featureFlagSettings[flagName]
// 			const { description } = displayInfoOfFeatureFlag(flagName)
// 			return <div key={flagName} className='hover:bg-black/10 hover:dark:bg-gray-200/10 rounded-sm overflow-hidden py-1 px-3 my-1'>
// 				<div className='flex items-center'>
// 					<JoyCoderCheckBox
// 						label=''
// 						value={value}
// 						onClick={() => { joyCoderSettingsService.setFeatureFlag(flagName, !value) }}
// 					/>
// 					<h4 className='text-sm'>{description}</h4>
// 				</div>
// 			</div>
// 		})}
// 	</>
// }
export const JoyCoderFeatureFlagSettings = () => {
	const accessor = useAccessor();
	const joyCoderSettingsService = accessor.get("IJoyCoderSettingsService");

	const voidSettingsState = useSettingsState();

	return featureFlagNames.map((flagName) => {
		// right now this is just `enabled_autoRefreshModels`
		const enabled = voidSettingsState.featureFlagSettings[flagName];
		const { description } = displayInfoOfFeatureFlag(flagName);

		return (
			<SubtleButton
				key={flagName}
				onClick={() => {
					joyCoderSettingsService.setFeatureFlag(flagName, !enabled);
				}}
				text={description}
				icon={
					enabled ? (
						<Check className="stroke-green-500 size-3" />
					) : (
						<X className="stroke-red-500 size-3" />
					)
				}
				disabled={false}
			/>
		);
	});
};

// 账户功能组件
const AccountSection = () => {
	const accessor = useAccessor();
	const loginService = accessor.get("ILoginService");
	const [loginInfo, setLoginInfo] = useState<any>({});
	const [isLoggedIn, setIsLoggedIn] = useState(false);
	const [isHovered, setIsHovered] = useState(false);

	useEffect(() => {
		// 初始化获取登录信息和状态
		const initLogin = async () => {
			try {
				const info = await loginService.getLoginInfo();
				setLoginInfo(info || {});

				const status = await loginService.isLoggedIn();
				setIsLoggedIn(!!status);
			} catch (error) {
				console.error("初始化登录状态失败:", error);
			}
		};

		initLogin();

		// 监听登录状态变化事件
		const disposable = loginService.onDidChangeLoginStatus((status) => {
			setIsLoggedIn(!!status);

			// 当登录状态变化时，重新获取登录信息
			if (status) {
				loginService.getLoginInfo().then(info => {
					setLoginInfo(info || {});
				});
			} else {
				setLoginInfo({});
			}
		});

		// 组件卸载时取消订阅
		return () => {
			disposable.dispose();
		};
	}, [loginService]);

	const login = useCallback(async () => {
		try {
			await loginService.login();
			// 状态会通过事件自动更新
		} catch (error) {
			console.error("登录失败:", error);
		}
	}, [loginService]);

	const logout = useCallback(async () => {
		try {
			await loginService.logout();
			// 状态会通过事件自动更新
		} catch (error) {
			console.error("登出失败:", error);
		}
	}, [loginService]);

	return (
		<div style={styles.container}>
			<div style={styles.header}>
				<h2 style={styles.title}>账户</h2>
			</div>
			<div style={styles.infoText}>
				{isLoggedIn ? (
					<p>
						您已经使用 <span style={styles.userName}>{loginInfo?.userName}</span>{" "}
						登录。
					</p>
				) : (
					<p>
						您当前未登录，请点击下方按钮登录。
					</p>
				)}
			</div>
			<div style={styles.buttonContainer}>
				{isLoggedIn ? (
					<button
						style={{
							...styles.button,
							...(isHovered ? styles.buttonHover : {})
						}}
						onClick={logout}
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							style={styles.icon}
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
						>
							<path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
							<polyline points="16 17 21 12 16 7" />
							<line x1="21" y1="12" x2="9" y2="12" />
						</svg>
						登出
					</button>
				) : (
					<button
						style={{
							...styles.button,
							...(isHovered ? styles.buttonHover : {})
						}}
						onClick={login}
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							style={styles.icon}
							viewBox="0 0 24 24"
							fill="none"
							stroke="currentColor"
							strokeWidth="2"
						>
							<path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4" />
							<polyline points="10 17 15 12 10 7" />
							<line x1="15" y1="12" x2="3" y2="12" />
						</svg>
						登录
					</button>
				)}
			</div>
		</div>
	);
};

// full settings

export const Settings = () => {
	const isDark = useIsDark();

	return (
		<div className={`@@void-scope ${isDark ? "dark" : ""}`}>
			<div style={{ padding: "2rem", width: "100%", height: "100%" }}>
				<div style={{ maxWidth: "768px", margin: "0 auto" }}>
					<h1 style={{ fontSize: "1.5rem", marginBottom: "2rem" }}>JoyCode 设置</h1>
					<AccountSection />
				</div>
			</div>
		</div>
	);
};
