// Base64编码的logo图片
export const LOGO_BASE64 =
	"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAgEAAAIACAYAAAAbur/EAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAADOTSURBVHgB7d1fbFVXlufxfUxoKJXBlLofTEFSLqkeShipqC4lCnkpkJwYpNZMoiikpjVShfxBXTMPQMhLv4Ch1eqXAEbqkWb4n4dpdYiiSuYFHKyO6yWgREqlJIympRrVTWHAb80fP0DA3Dm/e73hxrHxvdfn3LP2Pt+PZJkk3YWxD2evvdbaazsHAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACiNxKGU+vr6Vt27d69vetptSBK3Kv1XP6pWa5/79M/VanVV/f8y6XMAgpQkyY1q9cGNmX+qzPzr9HPydZJU0/+WfLVkSfXG1atXv3IoJYKAyPnF/sEDtyld5H+WLvAbtLA/WuQBQAGD+yp9R9zQ5zRA+F0aQFQIDuJHEBAZLfrffPNNuuAnv0z/UQv/BgcAbahnEqppUOA+VtZgcnJizCEqBAER6O1du8m5B+min6Sf3SYHADlQUJC+a8bSgOBj55aOTU5WKg5BIwgIlBb+NGX3n9Mf4Wuk9gEUQaWD9NN71erSjwgIwkQQEBAWfgCGjaUf75EhCAtBgHGq8d+9ey9d9F26+JPqBxCE0+ny8h49BPYRBBjV29vX59w3v06Srl3s+gEEqpJ+7J+cvHrawSSCAGPqTX7VfY5dP4B4VNL32mnn/uI9SgW2EAQYweIPoAQqBAO2EAQUjMUfQAlVFAwsX/4XRyqVyg2HwhAEFEQNf3fu3Duc/vI1BwDlVHH0DBSKIKAAvb0/3EfDHwA8VFmyxL3EmOLOIwjooJnU/6n0l30OADDbaeeW7qdfoHOWOOROqf/vfe/7/5T+8n+mH+z+AWBuG5x78GJ398qbU1O3yQp0AEFAzrT7v39/+mz6yy0OALAQbZQUCPR1d//lH6ambtA4mCOCgJyw+weARSEr0AEEATlYs2bNhnv3Hnzq2P0DwGLUsgIrVqxclQYCIw6ZozEwY6tXr92Zfhqi8x8AMlVxbulmmgaz1eWQmdWr1xxOF/9hAgAAyFxfktz7/Q9/+MMXHTJDOSADuuxnxYrvf1qtOh5OAMjP8mo1+VV39wqXlgd+57BoBAGLVL/t757q/z91AIAOSDbRJ5ANegIWQcf/ksT9lvQ/AHRe+v79qlpd+hJ9Au0jCGhTb++aX7vadCsAQIEqNAy2j8bANmj2vyMAAAAL+lSSrZdm0SoyAS2qBwDJkAMAWFIhI9A6goAWEAAAgGkVAoHWEAQ0iQAAAIJQIRBoHkFAEwgAACAoFQKB5hAELIBTAAAQpAqBwMIIAh5DFwFNT7vfOwBAcDRHYNmypZsrlQrXEc+DiYHz0HGTavXBWcc1wAAQqt779x/0Tk3d/thhTgQBc2gYBdznAAAh28BdA/MjCJhDd/f3f5t+2uAAABFINnV3r6ykgcAfHL6FiYGzzEwD3OQAANFIEjesPi+Hb6ExsEFv75rX0k+nHAAgRpXly5f+nEbBR8gEzJiZO73PAQBi1Xf37jds9BrQEzCju/v7OgrY5wAAEUt+umJF982pqamLDmQCZKYPoM8BAEqgax+3DtaVvidg5jjgnxwAoEzGJievbnYlV/pywEwZgIFAAFAufZQFSl4OoAwAAGVGWaC05QDKAAAAV/KyQGnLATNTAfscAKDM+rq7V349NXX7K1dCpcwEMBQIAOAlibuxbNnSH5dxiFApMwFp1KcsAM2AAABZfv9+9e7U1K0xVzKlawykGRAAMFuSVHeWsUmwVEFA/QecvOYAAGhQrSo7fK90o+NLlgm4RxYAADCf13p7125yJVKaIGAmzfOaAwBgXtVSZQNKlAkoX5oHANCyTWXKBpQiCCALAABoVpI82OlKoiSZgG9+7QAAaEK1mrxYlpMCJQkCOBEAAGjF/ddcCUQfBMxMB+xzAAA0SXMD+vr6oh8qV4ZMAA2BAICWaG7A3bv3XnORizoImOnw7HMAALQu+n6yyDMBVRoCAQBtSbMBG2I/LviEi5RqOXfv3n+xmv4UAYRp5cqV7rnnNrqNGze6p5560q1du9b19PTU/tvNmzfdrVu33KVL4258/LK7cOGCu3LligOylCTVF9NPYy5S0V4lzHXBQLi08O/Z83btcysUEBw7doKAAJmJ/ZrhmIOAT9NPmxyAYGzZMugOHNjvnnxyrVusY8eO1wICggEsVldX9aVr16595CIUZRBQH/Jw708OQBDa3fkv5MqVCffuu4fcmTNnHLAIY5OTVze7CEUaBFAKAELw5JNPpov/bvfqq9tcnpQV2Lt3yAHtiLkkEGUQsHr12t9Wq7VmDgAGqeHvrbfedDt2vFn7dSeoX+Dll1+pNRMCrUs2T05OjLnIRHlEkAAAsEu7/i++uOjeeeftjgUAsn59v/vwww86+nsiJnEeOY8uCCjTFZBASFTv1yI8PHyosIVYgcCRI4cd0Kq0JBDl5nKJi0x39wqNCd7gAJiguv+pUydqO3/9umg/+clPaiWBL7/80gEtWN7Ts/Lj27dvT7qIRJcJSJKEAAAwQLt9dfyPjo5k3vW/WAcODNWyAkArHjyI79h5VEGAjgZWq1WCAKBgavorou7fiv37hxzQmuomF5mogoCurmkCAKBA2vGfPz9S22lbb8DT12otQwHrkl+6yEQVBKRZgE0OQMep1q+mP32ElGZXuQJolq4Xrg+ji0dsQcDPHICO0W5fu/7PP78Q5K5aX7OFZkWEZHqTi0hsjYGbHICO8HV/fQ7Ztm2vOKBZSfIgqrJzNEHAmjV99AMAHaDd8+efXwyi7t8M+gLQoqj6Ap5wkahWp/scgNyo1q+O+tgWTY4KokV9LiLRZAIePOBoIJAHX/dX13+Mu2b9+egLQLNiaw6MJhOQJO5n6Q8HQEaKuOSnKOvXr3NXrlxxQDO6ur7RprPiIhBNEOAiS9EARdKOf3j4cLpDXuusef/9M+7y5cu1431ZBScrVvQ4oFnVatLnIkEQAOAhLf5aXC2m/T/77II7ePBQ7bOsW7eudiMhUIA+F4kogoC+vr5Vd+7cW+UAtMXX/S0uqleuTLi9e/e5c+dGHGBDtc9FIoog4N49sgBAOyzX/XXT37Fjx93Ro8drvwbsSH7kIhFFEDA9fZ8sANAi7fqV+rdY99fi/+67h1j8YZJOCLhIRNITEE9qBsib9br/vn1D7tKlcQcY1uciEUkQkPSlgYADMD+dhd+zZ7fZuv+uXbsfNv0B1qkXrVKp3HCBi+l0AIA5hFD3V+ofCMmdO7WSAEGADZQDgLlo1291xj91f4RsyZI4TqSRCQAiZL3uv2vX20zoQ9CmpxOCAAC2qO4/PHzI5OKvZj81/VH3B+wgCAAiYL3ur7S/0v8AbIklCIhmcAPQKi3+77zzNsN+ALSMTAAQKKX89+8fcuvX9ztrqPsDYSAIAAJjue4/+5IfALYRBACBULpfaX+l/61Run/v3qHaNb8AwkEQAARAx/245AdA1ggCAMOU8h8ePmzykh/t+g8ePEzdHwgYQQBgkPVhP9T9gTgQBACGWK7765IfLf7U/YF4EAQABoRwyQ91fyA+BAFAwbZsGXQHDuw3W/dX1z+LPxAnggCgINT9ARSNIADoMA372bNnd+2aX2tU99+1azeLP1ASBAFAh1iu+3s67qdAAEA5dDkAudOuf3T0E5MX/TRSaeLzzy/UxhIrYwEgbgQBQI60qH744Qczi6q9xr/5KGjR171tm72SBYDsEAQAOdAu+tSpE7WF1GLjXzMUtBw5cijNDFwM9s8A4PEIAoAMKdWvjv/R0ZHa0b8YKBh4lM2gRADEhCAAyIia/r744qL5un+7VCJQv4CCHABxIAgAFsnX/Q8cGIpy8Z9NQY5KBPQLAOEjCADapNS4Fv+Q6/7t8v0CKntQIgDCRRAAtMjX/ZUaL3vDXH9/P0cKgYARBAAtaKz74xF/pNDi7YcA5kcQADShPkTnYmnq/u1QiUDfH/oFgHAQBACPsX59/8O6f0jDfork+wUoEQD2EQQAc9BuX7va8+dHGJTTJn+kUN9HggHAJoIAYBY1/anuT307G/o+MoIYsIkgAJjh6/6xDvspUuMIYpVYANhAEIDS88N+qPvnT99flVjoFwBsIAhAafm6v8VhP1euTLjPPrvgsqL/PUsYQQzYQBCA0vHDfizW/W/duuUOHjzkBgZeyDQIeP/9M+7ll18xFwwwghgoFkEASkU3+42OfmKy7n/s2HH39NPPunffPVQLBrKmoOKZZ551u3a9bSoY8P0C9XIMJQKgkwgCUAq+7n/q1AlzdX8tztql7907lMviP5vPCuizJfXGTEYQA51EEICoaTHRomK17q/FWB9Zpv6b+72v1DICzzyz0Z09O+IsYQQx0DkEAYiW6v665U6LiiW+7q/UfKcX/9kUDLz++hsmSwR+BLFKOADyQRCA6Ogcuo6hWa/7W6LSgIISBSedKEk0S8GASjiUCIB8EAQgKkohKwCwNpCm3pS3sWN1/3YpOBkYGDTXL9B4pJBgAMgOQQCioB2/6shKIVty6dL4w7q/Uu8haOwXsHikkBHEQHaecEDgtDO0Nu1Pu33tqpX+D5WCAZUItAuv78BtfH/9kUIAi0cmAEFTx7+a/ywFAKqrq+4fcgDQSKUBDS/SnwtAXAgCECylhJUBsNL85+v+eQ37KZLPbOjPZ61fAED7CAIQJKWnraSE/bCfkOr+7fL9Atu3v2GuXwBA6wgCEBydAFCDWNG0O1a3fxHDfop27tyIyRHEAFpDEICgqARQ9AkAP+wnprp/u6yOIAbQHIIABENn/4suAWgHrHP0Mdb929V4pLBsGREgdAQBCIKOAZ48ecIVxdf967XwuOv+7dL3Rd8jSgRAOAgCYJ4fBFTEMUAtZlrUylj3b5fVEcQAvosgAOapCbDTAYCv++t8PPXu9lgdQQzgEYIAmKZpdZ2+UlaLlr/kh53s4vh+AQUDlAgAewgCYJb6ADQPoFN83V+LFot/tsbHx00fKaxnm7iYCOVDEACz9uzZ3ZEygBYlP+yHun++/JFCayOI9ZzplkKuLEbZEATAJJUB9JE3nfNX3Z/Fv3NUIrA6gljPnJpQO5mBAopEEACT8n4J+92/Jv6R+i+G7xewViJQVkDlgc8/v8iVxYgeQQDMyfva2kuXxkn9G+KPFCogsxYMaDgVJQLEjCAApuhlm2cZ4MyZM6W46CdEKs1YHEGs51H9AhpXTTCA2BAEwJRt217JLQugAGDnTjr/LWscQayMjSU6qqp+AUoEiAlBAMzIMwugmf8KABAGBQPPPz9osl9AJQL1C+guCyB0BAEw47nnNuaSBdCOcufO3Q7haRxBbIme0/PnR+gXQPAIAmBGHicCtIt8/fU3KQEEzvKRQvULcKQQoSIIgAlbtgzmkgXYtWs3TYCRsDyCmCOFCBVBAEzI4+WpFDLHAOPjRxCfPTviLPH9AqOjI5QIEAyCABROL8ytWwddlrRTPHr0uEO8bt266Szq7+9nBDGCQRCAwqkhMGvcZY+i+RHEnb4FE2gFQQAKNziYfRaAO+xhgUoEGjJEvwCsIghA4bIuBagZELCEEcSwiiAAhcq6FKAsAM2AsKrxSCHBACwgCEChNm7MNgiwNlQGmIuOFDKCGBYQBKBQWWcCzp4954AQMIIYFhAEoFBZvvx0PwAnAhAaRhCjSAQBKIzOU69cudJlRUEAECr1C2jQECOI0UkEAShM1mOCaQhE6BQUM4IYnUQQgMJkmfrUKFnuCEC7lEWyeGWxmgcpESBPBAEoTJaZAGsXyiAsuofg5ZdfMTdkSo2zjCBGnggCUBj1BGSFUgAWy99SqCuLrV1O5EcQUyJA1ggCEIWJCUoByIaCgddff6MWEFgsEahfQFdvA1kgCEBhskxv3rzJ0UBkS6UBXVls7TIqBQOnTp2gRIBMEAQgCvQEIC/vvnvIDQwMmusXaBxBDLSLIACF6enJbkYAkKfGfgFrAac/UpjHldyIH0EACpPloCCOB6IT9JypRGCxX0CNg2QF0CqCAABokUoDAwMvmLuwymcF6BVAswgCAKANahZUv4BKBJb6BXxWgPIAmkEQAACL4PsFtm9/w0yJgPIAmkUQAAAZ0Ohha/0CKg8QCOBxCAIAIEMqDVgaQUwggMchCACAjDUeKbQw0ppAAPMhCACAnCgYUFbAQomAQABzIQgAgJw1jiAuEoEAZiMIAIAOsXCkUIEAtxHCIwgAgA7y/QK6j6CoEsE//MOQW78+u6u8ES6CAAAowPj4eGFHCjWy++TJE5mO7kaYCAKAEtDwGNjkjxQeO3bcdZKeiSNHDjuUG0EAYJB2aFmma+vXznLTnFUqEezdO9TxfoEtWwbdW2+96VBeBAGAMXopf/HFxdoLOkt+lOzw8CEumDHK9wt0skSgRkGeh/IiCACM0C5du/UDB4ZyrdXWswIXCAYM6+SRQj1rehZQTgQBQMG0EGuHro9O1u4VDIyOjtTOjdMgZpM/Uph3VkABKKWiciIIAAqihVe7fu3Ki3oB62tQOnh09BPOjhulEsHAwAu1C4ryNDx8mGCwhAgCgAJo9626v5WmrHqn+KFaOYJgwJ5bt27VrirOszygZ4AmwfIhCAA6yNf9tfu2uOvywYDKBAyTsUflgTwDgR073iQbUDIEAUAHaPEvou7frv7+fnf+/AjNgwblGQgoACAbUC4EAUCOfN1fi3+IjVecJLApz0CAbEC5EAQAOdBL1FrdfzEaTxLABgUCn312wWVNz65+3igHggAgYxryo257q3X/dvmTBDQP2qFmwTyOD2Y9qAp2EQQAGfF1/1OnTkQ9q5+TBHbo1MCuXbtd1pgbUB4EAcAiqVaumnmodf92+WCg3uxIv0BRVBLI4/KhwUGyAWVAEAC0ydf9VSsvcw21fuyR5sEiqT8g67LAr35FlqcMCAKANmjRV9NfbHX/xeAkQXFUFsj6tICea0oC8SMIAFrg6/5a6Fj856ZgQN8jThJ0li4dyvq0ACWB+BEEAE3QzvbUqROlq/u3S/0CnCTovKx7A7Zu3eIQN4IA4DEa6/4cm2odJwk6S5cMqTSQFf38KO3EjSAAmIeG/FD3zwYnCTrn6NFsswEEv3EjCABmUbpfc/M17pfFP1ucJMhf1iWB/v51DvEiCABmaFHyl/xwg16+OEmQH5UDsmwQfO655xziRRCA0vN1fy1KsTb95TFaNgucJMhHlkGASjlkxOJFEIBSa6z7W6Ld3KVL4y4rOj72zDMba5+t4SRB9i5cyPao4FNPka2JFUEASqlem75osu6vDu+BgcHa5yxduXLF7dr1di0YyOP2ucXiJEF29PPN8pTAunWUx2JFEIBSUa3f1/2tXfKjF/fLL78yczPcFZcX/W/r91FAYLFMwEmCbGSZSerpoRwQK4IAlIJ2+9r1q+vfWt1fC7EWZC3Mndyh10sEz5oNBjhJsDjj49kFAWvXxnsrZtkRBCBqvulPdX/V/y3x894HBl4otFbvgwF9LRaDgflOEqxc2eMwvyx/lmQC4kUQgGhpJzk6+onJYT9aeJ9++tna7W9Z1m4XQ1+LshEWmwdl9kmCLBem27dvuthkOzmQTEysCAIQHX/Jj+W6v1LwVhb/Ro3Ng9ZPEmS5MN28ae9nsVh59pUgHgQBiIZ2+0oZW7zkR6lZNfx1uu7frhBOEmQZ4FkMyBYry8CGTEC8CAIQBV/3V8rYksa6f9ZH/jrB+kmCrPz5z/HtmmMMbJA9ggBEwWLdXzPcrdX922X9JMFi6GfDgomyIggAMqb0+fPPD7q9e4eiW1ysnyRoR5bn6YHQEAQAGdGiqNS5PmJfWKyfJGgFQQDKjCAAWCRf99cOOYSmv6xYP0nQrMuXLzugrAgCgEVorPuXlfWTBAvJcrIeEBqCAKANWuy06MVY929XiCcJ9HVSDkCZEQQALdCC4ev+DGOZW0gnCT777DMHlBlBANAE7fa161fXf5nq/ouhYEDzEdQvYTVborkSmjHBMByUFUEA8Bi+6U91f9X/0Rp9/9QvMTAwaLZ5UDMmNGVy2zZbg6aATiAIAOahHb8WrxiG/RQthDsJjhw5VLuTgGAAZUIQgEJYTr/6S36o+2fPBwMKriw25DUGA9bunwDyQBCAjtJo3wMHhmr3w1uj3b4WqFAu+QmZjuWpv8Jq86CCAZUIdCEV/QKIGUEAOsZf8vPWW286Sxrr/jFMwAuJ9ZMEahxUwEowgFgRBCB3SqsqvWrxkp96Bzt1/6Lp56AMjIIxi3wwwEkCxIYgALnR4q+Uqj6yvPs9C77uX9+BUve34ObNm66/v99ZxkkCxIYgAJnzdX+9LK01VynlTN3fFj0vvlS0Zcugs46TBIgJQQAy0/gyt1r31/Aa6v52KEgcHf3EZKloIZwkQAyecEAGtIM7cGC/ubS/aNFnxr8tWjQVMMawePqTBHrODh48THkJQSETgEXxdf9Tp06YrvsTANig3b467S2WihaLkwQIEZkAtEUvuT17dtdefNbU6/67qfkbosVfJaIdO94MLu3fKv2d0IdOnJw58wGZAZhGEICWWH6Za7ev+f5Hjx5n52/Io0t67JWK8qQ+h0fBAH0osIlyAJqmF5rVJi4t/hr2w3l/O3ypqJ4eL1cA4HGSANYRBGBBIbzMafyzQ6WiWOv+7eIkAayiHIB56WWu8/4hnN1G8SyVivwIYmtBKycJYA2ZAHyHP+8/OjpCAICmqFSk+RAWSkXKCOlUCHcSAAsjCMC3aCdn5WUO+xpLRRaeFy34jVdAh3AngYJtBd38fUMRCAJQ41/mSv/zMsJCtHv190JYqXH7AODSpfFZ//5KrWH0mWc2mpwWqb9vCrrVdEvzIDqNIKDkLL7MYVdjqcjS86KFvzEDMBf9N5UHrAYDnCRAEQgCSsq/zFWXZPFHM6yWinQ8dKEAoJEPBur/P/b6BXwwoEBr/XrbtyoifAQBJdT4MgcWoiDx/PkRc6UiNQDqaGi7x0M1UdJy86CuVdb3neZB5IkgoET0Mleqkbo/mtFYKrK2I9UCPjAwWMsCLJZKA5wkQFkRBJSAXuD+ZV7WyW1ongJEBYoWS0Xa8T9K5Wd7xj6EkwT6O6wyHpAVgoCI+Ze5UooWX+awRwuMSkUqGVmi50WLs0ZD59nUZ/0kgYJ4lfFoHkRWCAIiFcLLHJ2zUPnHl4oszofQYqzUfyfvhWg8SXD27IizhpMEyApBQGR4mcdjYiK7+nRPz9zPgp8PYbFUpLq/0vP1Wn0x43X1+77++htu+/Y3TJ8kqP/86BdA6wgCIsHLHI8ze4HwpSKL8yG02Pq6v54dC86dGzHdPFgP/mkeROu4QChw/mWupiFr9LLcu3df7QWK1mUZMPmFwdIlP7MpO6Ru/6NHj5vNFCmbpQ99D/VhLeDWe0Af+hrPnPnAAQshCAgUL/P4Zbnj1GKl2rHKRBZPiGjRCuk6aD3fCm7feusNc303okBg69YtDlhI4iLQ27vm0/TTJlcSutnvwIH9Jl/mejk2W/O/fj27RW716viOPiq4+/d/v+xipnS/GkWtpP3boSzLnj27TWbjsqKAVOUQNEo2T05OjLnA0RMQEF/3P3XqhNm6f0i7Oev0fbRYf86Cv+zHUt2/XY0nCUL/s6B8KAcEwN8yZjHtWG/i2s3LLye6GCemAU8xl4oUDCioUUZAR3QZzIUQEAQY50e3Wnuh+Je5Uv/Iz8REPKcpWikVhcw3DxIMIASUAwyzGgDoZa5hPwQA+RsfD78nQFmi558fLF2pyN9JoJ6HWMs6CB9BgFEWA4D6rWsbqft30Nmz51yoGuv+KmuUlYJlfQ8sjiEGCAIMshYANL7MGfbTWSE2B/orfrULplekrrF5kGAAlhAEGKTjRhYCAF7mNpw7F042wN8LkcUVvzHiJAGsIQgwxk/8KhovcztCmLjoS0XcC9Ecf5LA6hhilAdBgDFF3xXOy9wey4uEnw9Bqag9vnmQYABFIQgwRBmAosoAvMzt0XwIBYW6GMYaXyqKYdiPBZwkQFGYE2DItm2vuE7Ty1y7ftL+tmgwlMXroLkXIl/6u/j++x9EP4YYdpAJMEInAjp5pate4NT97alfCXuxdjOktQBAvQkDA4OUinLGSQJ0EkGAEZ0MAHiZ2+OPhVocDuVLRdu3v0GpqIM4SYBOoBxgRH//Ope3GG5si431eyH0vLAbLRZ3EiBPBAFG9Pf3u7zwMrdJL/QdO96k7o+mNN5JYLFchDARBBihdHDWeJnbpNLP8PBhszu6so/5tUzPjhqICQCQFYIAI7JeELRjYMa/LXqBa/ffyf6Pdpw8eYKjosZokzA8fMj8s4Pw0BgYITX8qaGIAMAG7dqUvlXTXwgvcQWk+lq3beOIWtEaZ0UQACAPZAIipEYzvcgPHjzMbq5AeoGr4c9i3X8hen6OHNHO81meo4JYnRWBuJAJMCLrKWFqHtLuQSnEPPoN8Hhbtgy60dFPgn+J++eo6HHWZWJ5VgTiQxBgxM2bN10eGl/iBAP50wtcqfRTp05EdZRLwYwWJkoE+Vm/vt/srAjEiyDAiAsX8j27r5c4dd78+MatUOr+7fAlgvoiRUCZFd8zcv78CHV/dBxBgBHj45dd3vxLnB1ddnzj1ujoSGlmvdfT1ZSasqBn54svLpocFoVyIAgw4uzZc65TGoMBdh7t06KvF3hZm7f051dWgAWsdb7uT+MfikYQYISO83V6nK8/CsaOrjW+7q/vW9lf4HqGlMomu9Qc/+xQ94cVBAGGaLRvEThJ0JzGS36sZVB0ukTDoYris0s8Q3NTsBh7zwjCRBBgiDIBRV7uw0mCuTXW/a29wJVB0uL/zDP1K6E1JKpIPEPf5p8dlY3K0jOCsBAEGFNUNqARJwkeUb3bat1fz8rTT9cXf0/jonmGbIhlVgTiRhBgjDIBjS/1opT9JIF2/DqyZXFgi54R3TGv8dBzjYbWv7cQCDQ+Q2XKCsQ6KwJxIggwSC/xrCcItqtsJwka6/4a3mKJFn9d7NPM5T56horsEWikZ6gMPSdlmBWB+BAEGKTdXf1FbyMQkNhPEviBLRYvavF1fz0TrfSMKKOk/x8rF0mpJq6+ithGEJdxVgTiQRBglHZ61gIBifEkga/7WzvvrsV7rrp/KxQ0DAwMmnmOtGDGNILY8qyIM2fOOGAhBAGGWQ0EJIYucMsXtZw7N1JbvOer+7dCz9HAwAu1pkErfJmpXjcP7/mxPCviUc/IYQcshCDAOL3AdfzLQqPXXELsArd8UYuv+2/f/kam1/cqkNDxQX1YCirVQR9SZsn6rIhme0YAjyAgENoRKrq3tJvzLJwkaGY3ZvmiFr3AtUC3WvdvlZ4f/R7WniM/gthqMBnKrIgi54wgTAQBAVF07xcKiyUCHwzoRdlMZ32Wf4bH/X6NA1us1v07ma73z5G1rEBjMKkMgRWhzYoAWkEQEKB6ze9Zcy9xr7+/v7bb7mSKd3j48Jy/l3ZtVge2aNHXCzyLun+7v78vNVk5QSAKBtQrUHSJIORZEUCzEheB3t41n6afNrmSUiq13qBnczBJfYrd4e/UKU+ePOG2bs1ux6eASC/FiYkrtZe2dnAWz2vrBa6F11LqVovtnj27TR5x089UO91OLXb+vH/oz47+HOq3yIL+bilgRKNk8+TkxJgLHEFAJPQX/tVXXzF7Blsv8KNHj3/rZa4dVpmuoa3X/Xebrtv6xjdrAaUP8PI89uaPL1p8JvV3xgdDzSIIyFscQQDlgEhol225edC/YJWa981f4+OXXRk01v2tN2750yiW+wXyKBHEPCsCeByCgMj4pq8QThKU4VIVvbiLrPu3y58isHY0NesRxGWZFQHMh3JA5PSSqzfNcZFJJ2nHv2/fkLt0adyFzmq/gDIV7d6aqNMk+/cPRd0zQjkgb5QDEADrJwli0ziwJYYAQCwfKWx1BDGzIoBvIxNQMqp56oPMQLbaadwKldVnaL5TKOJPi+zY8aa5tL+eHT03apzNMu1PJiBvcWQClrgIdHevfC391OewoC+//LJWa5Rf/OKvHRZPL3CN+b1woRy7N/8MaTG1dN2yvhYt9D09K90f//j/Hi6o2vH/y7/879px1GXLljlLFLj87d/+V/fpp2Pu7t27Lks9PT2ZNTr6QAWNkvempm5VXODIBJSY5bPhIVDKtp4iL++cdj1DJ08eNxUMiHauWrQ0ebCssyLIBOSNOQFmEAQsjl4WapLKcnBPzFTrV9MfddtHrA+ssqKTsyIIAvJGYyAioZ3s66+/MXNzHc2D8/EXtTz//CABwCyNI4jxXSHNikC5EATgIdV5OUnwXQxsaZ7lgVVFCXVWBMqBIADf0birK3swoF0bA1ta448U6vtW5udHz46yRsoe8ezAKoIAzEsLn8W75ztBL3B/3r/MjX+LMT4+XsrMUoyzIhAvggA8lvUxxFnTjo2BLdnyI4hjL6X4nhEFPjw7CAVBAJrSGAzE+IJrrPtTz86enp/6AhlnMOnr/vSMIDQEAWiJXuba1cWU4tWiRN2/M6yOIG5XfSz3Rur+CBZBANrimwdDfpn7un/ZB/4UIfTmU9X66RlBDAgCsCihvcy1W9OMdv8Cp3ZbrNCaT5kVgdgQBCATIbzM9QLX18e0P1sa+00sp9SZFYEYEQQgM9ZPEvib5Fq5ehadofn+uoPA2g1/jfTMPPXUkw6ICUEAMmf9JIHm2x85cqgWDOiCGRRH8+0//PCD2oe1S4hm03Nz/vyIGx4+VPu6gRgQBCA31k8S6KV+6tSJ2gcv9c7Sjv/AgaHaBTcWb/l7HF2WpK9bFyYBoSMIQO6snyRQNkAvdXZ4naHF84svLmZ2131R3nnnbUpLCB5BADrG+kkCv8PTDpVgIHva8WvR1OJpufbfCl9aUjmDZwYhIghAx1k/SaAdql7qoe9UrdDi7+v+WjRjVA9wyCYhPAQBKIT1kwRarOo1a9K97fJ1fy3+odX926Vskv68PDMIBUEACsVJgvho8Y+l7t8OnhmEhCAAJoRykoB07+Np0Rsd/SSqun+7eGYQAoIAmGL9JIFvHuTF/m2+7l8/bhln3b9djUcKyx4YwR6CAJgUykkCvdjLHAzoz66AqEx1/3YpO6IsCf0CsIQgAKZZP0mgF3sZG8F83X90dKQWEFmi+wcUPK5evdZcRqmxX4BMEiwgCIB5IZwk8C/2MgQDWvSt1v11uY8u+VHwKHpeBgZeqAUFluiZoawECwgCEAwfDAwMDNbuc7emMRiIMTXu6/71hctW3V8nS3S9r675nX0Tof5ZQYHFILIeUI0wghiFIQhAcMbHx2svfMsnCR4tluHv8vRnUMOfxbq/fv4qF+ljocDQB5Hbt79h6rlRNoURxCgKQQCCxUmCfDXW/a2dd9fuXrt+/fxbnS9x7tyIyefGZ5K40AqdRBCA4DXWfWengi0I8SSBhvxo2I/Fur9+zqr7q/6/GHpuLDadcqEVOokgAFHwdV/1C3CSoH2+7q9xv9YWf+34VdfXzzmrYM/yxEpGEKMTCAIQFU4StEc7Tn/Jj7W6vxZnX/fXzzcPVidWxt5siuLFEgR87YAGPhioLxw2mwctvNx93V/pZ2uLjK/762fYqV1645AqS2JrNoUdZAIQtXoK2W7zYFEv98ZLflSmsMQP+8mi7t8uy0cKGUGMLCUuAr29a06nn37tgAXoJVpv0LM5316LzpkzH+S289XCoaa/HTveNLmIqHNfu/+80v7t6O/vN3kngoJaBStnzswdqCioVMCQ1e+lYBqNks2TkxNjLnAEASgdvRxfffUV0wNa9NI9e/ac++CDDxY9GEmL/datW9y2ba+49ev7TS7+Cnq0+7d4nbRnNYD0sxJmB06+yTOr34MgYDaCADN6e9cOOVfd54AWKBjYs2e3udn3s+kFrAFJWiD1Weny+QIDLfBPPfVkbfe6bt262qJvuaFMfzYt/lZPdMxmOYDU9/DgwcO1YMA3emYVsPjmTDRa+uPJyUrFBS6KIGD16jW7qlV32AFtCCUYmIvvc+jpWRlUjViBjOr9R48eNznbYSGWnxk9E1lnK1Sm0aRFPLJ8+dIfVCqVGy5wS1wEurtX/jT99KID2qBFSC+5CxfUIf9cbUENhb5WfSxbtsyFQjvW3/zmv9e+53fv3nUh8s/MxMRELeti6ZnJ42v5t38bc2NjYw6PTExc+XsXgShOB3R1dQUfjaF41k8ShM6nlOvfXzuNf4vhjxTWmxnjfWYuXrTbq1GQiotEFEFAkiypOCAj1u8kCI2+h0old/K8f6eptGFxBHFWLl267NAoqbhIRNET0NfXt+rOnXv/4YCMhXCSwKrQ6/7t0jNz8uTxWlNmDNSMqnHceCRJqh9fv34tihJ0FJkANWckSUJJAJlT2trq4BjLtPhr2E+Wc/5DoWfG8lXXrTp27ITDbPFkAmKaGFhxQE6s30lgha/7q0ZetsV/NqsjiFsVawlnkSouEtEEAdVq9Q8OyJn1OwmK4gfWxFz3b1fImSR9zbE0cWapWk2+cpGIJgigHIBO4iRBnZ/zr+8Fi//8fPCo2nooz0v9Z8v4lbksWVKNZr2JKRMQTWSGcJT5JEFj3R/NUZNdKM+Lfr5kAb4rSdyNq1evRrPeRDEsSLq7v59GZl27HFAAvdw1PEa7p9jvfa/X/be5jz/+P8EO+ymaf17kF7/4a2eNxlJroBPmklycmrr1notEFEcEvdWr1/5HmhFY5YAChTyG+HG0MOzbN0TaP2PWnpf5LiRCXZoJOHL9+tVoNpzRZAKku3vFlvRTnwMK5EfK6kpgzfMP/by4/jz/+I//xPCknFgaQayv5W/+5j8RADze/5qauk05wKIVK3p+nn7ivkuYEPKdBKKv/5//+X+4v/u7/1b7MyBfKhGoDp/uNGvZgU4/L/p5KwPwxz/+0WF+S5a4/bdv3550kYgtE9DruEgIxmj3rJe7dnrKDOgFb1nj4v/pp2PU/TtM5RYFj53MIukZVQaAAODx1BR47drV3S4iUfUEMD4YIVDj4LZtr5jrGVDNf2RkpHRjfi1TwPjhhx9kfjVwIwUcO3fu5mfehJjGBXtRBQHS27vmT46+AARAL3gfEBR1okAv/n/91zO1xZ+GP7sUMOr+iiyDAe3+d+3azc+9BWkmYPf161eHXUSiCwJWr147XK1WdzogID4g2LJlsPZZqeC8aMevFz8Lf3iyCAb0M1fTKuOvW7dkift5TDMCJMJMwNpNzlU/dUDAVAtWp/i6detqv9ZHq4GBdvk3b95KX/qfucuXL9cWf32Q9g2fzyDpGWmmb4DALxOVycmrP3aRiS4IUF/A3bv3/8S8AMRIGQO/C1RQ0NPT8/C/3bx5s7bAK83rf434+QZCf6JgxYqVtSZU0YmDP//5Cs9CNk6nQcB2F5noggDp7V2jTMAmBwBABrq6qi9du3btIxeZmK4SfihJ3McOAICMxBgASJRBwLJlS087AAAyoKOBLlJRBgGVSkXXPI45AAAWqVpNoswCSJRBwIxobnkCABRDUwInJ6+edpGKNghYvnzpR0mS3HAAALSpWnXRZgEk2iBgpiRANgAAsAhJ1OtIzOWA6CM4AECuKpOTE2MuYlEHATM/vDEHAEDr9rvIRR0ECDMDAACtUkOgc9NjLnLRBwGaGUCDIACgFSonT05OVlzklrjI3bhx405398rvOcYIAwCaNv3S1NRU9BvI6DMBsnz5E8NkAwAATTpdhiyARJ8JELIBAIDmlSMLIKXIBAjZAABAE0qTBZBSZAKEbAAAYGHlyQJIaTIBQjYAAPAYpcoCSGkyAUI2AAAwv3JlAaRUmQBRNiD9VHEAADyU7C9bFkBKFwToYqGurq7dDgCAuopz90+7EipdECDXrl3RxUJjDgAA50qZBZBSBgF109sdAKDkkrHJyaunXUmVqjGwkZo/urt7EkeTIACU2PTmsjUDNipxJoAmQQAot3I2AzZKXMmtWbNmw/S0+70DAJRJJS0D/NiVXGnLAd7t27cnKQsAQHnUh8ZNbyxzGcArdTnAm5ycGHKcFgCAkqiWvgzgEQQ8NL2dkcIAEL3T169fHXaoIQiYoagwDQI4NggA8aqkG779Dg+Vvieg0e3bt/7vihU9P0h/+awDAETD9wFQBvg2MgGzXL8+scvRHwAAkaEPYC4EAXNYvnzpS475AQAQiWQ/fQBzK/2cgPn09vb2JckTv69Wq6scACBIaRngozTD+5LDnMgEzENpo66u6mYHAAhSkrivli17gobvx6Ax8DHqg4RWfp3+8kUHAAhJxbnprRMTE5MO8yIIWMDU1O2vVqxYeTP95RYHAAhBRRcD0Qi4MIKAJqSBwEVGCwNAECoEAM0jCGjS1NStMQIBADCtQgDQGoKAFhAIAIBZFQKA1hEEtIhAAADMqRAAtIcgoA0KBGgWBAATKgQA7WNY0CL09q7dlCTutwwUAoDOq88BWLq5UqlwA2ybGBa0CJOTE2PV6v2fO0YMA0CnvUcAsHhkAjJQHzG8JM0IuA0OAJCzZH+6CRtyWDSCgAytXr12OC0N7HQAgMzpOuAkebD92rVrHzlkgsbADE1N3TpHwyAAZE/1/2p1evP169cvOmSGnoCM1a+rnP6xo08AADKRBgBHVP/nBED2yATkYGpq6sZf/dVfvjc9Xf1e+o/POgBAy5T+7+qq/pfr168N37hx445D5ggCcqIHVuWBmVsI1TDIMUIAaFoy5tz9raT/80UQkDPdQtjd/f2P08rLD5zj9AAAPE69+c/9/eTk1d8oq+qQK04HdJCGCzlXPZX+ss8BAGY7vXz50t2c/e8cgoACpMHAUBrp7mTSIAD4zv9ktwawOXQUQUBBNGAorcYMpb/8tQOAElLqP82O7q+fqkIRCAIKRjAAoGy0+Fer7sjy5U8Mk/ovFkGAEfXRw08cTksELzoAiBCLvz0EAcaQGQAQGxZ/uwgCjGoIBn7pOE0AIEjJWJJUP162bOlpFn+bCAKM6+vrW3Xnzr0X00hapwmYMwDAtHqznxvTzp9uf/sIAgKyZs2aDdPTbpcjOwDAHHb9ISIICNTM4KHXHAEBgMJotK/7nXNPnJ6crFQcgkMQEAEFBGkE/mK1mvwsDQw2OQDIgU/1p++Z37HjjwNBQGTUQ/DNN9ObqtUHmwgKACxG46Kfvk++osYfH4KAEpjJFKipsE+BQZK4DYwsBjBLJV30v0oX/K+rVZd+XjpGij9+BAElVT91cH9DV1ey6sGD6oY0SEiDguRH6V/+VWmQsKohSOhzAIJUP59fveF/nX6qpBsBjer9Ov07fyNJuioPHiz5avlyd4PUPgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAkfv/aCGSyN60yUYAAAAASUVORK5CYII=";
// 图标Base64
export const ICON_DATA_BASE64 = "data:image/png;base64,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";


export const STYLES = {
	container: {
		display: "flex",
		flexDirection: "column" as const,
		height: "100%",
		padding: "32px",
		color: "var(--vscode-editor-foreground)",
		fontFamily: "var(--vscode-font-family)",
		backgroundColor: "var(--vscode-editor-background)",
		position: "relative" as const,
		boxShadow: "0 8px 32px rgba(0, 0, 0, 0.25)",
		borderRadius: "10px",
		maxWidth: "920px",
		width: "85%",
		maxHeight: "85vh",
		margin: "0 auto",
	},
	backdrop: {
		position: "fixed" as const,
		top: 0,
		left: 0,
		width: "100vw",
		height: "100vh",
		backgroundColor: "rgba(0, 0, 0, 0.95)",
		backdropFilter: "blur(12px)",
		zIndex: 99999,
		transition: "opacity 0.4s cubic-bezier(0.33, 1, 0.68, 1)",
		opacity: 1,
	},
	fullscreenContainer: {
		display: "flex",
		flexDirection: "column" as const,
		alignItems: "center",
		justifyContent: "center",
		height: "100vh",
		width: "100vw",
		color: "#fff",
		fontFamily: "var(--vscode-font-family)",
		backgroundColor: "transparent",
		position: "fixed" as const,
		top: 0,
		left: 0,
		zIndex: 100000,
		overflow: "auto",
		transition: "opacity 0.4s cubic-bezier(0.33, 1, 0.68, 1)",
		opacity: 1,
	},
	stepContent: {
		display: "flex",
		flexDirection: "column" as const,
		alignItems: "center",
		justifyContent: "center",
		width: "100%",
		height: "100%",
		padding: "20px 0",
		textAlign: "center" as const,
		flex: 1,
	},
	welcomeCard: {
		backgroundColor: "var(--vscode-editor-background)",
		borderRadius: "10px",
		border: "1px solid var(--vscode-panel-border)",
		padding: "24px",
		maxWidth: "800px",
		width: "100%",
	},
	logo: {
		width: "80px",
		height: "80px",
		margin: "0 auto",
		display: "block",
		marginBottom: "24px",
	},
	titleContainer: {
		marginTop: "24px",
		marginBottom: "16px",
	},
	title: {
		fontSize: "24px",
		fontWeight: 600,
		color: "var(--vscode-editor-foreground)",
		lineHeight: "30px",
		margin: 0,
	},
	description: {
		fontSize: "16px",
		color: "var(--vscode-descriptionForeground)",
		lineHeight: "24px",
		marginTop: "16px",
		marginBottom: "24px",
	},
	buttonPrimary: {
		backgroundColor: "var(--vscode-button-background)",
		color: "var(--vscode-button-foreground)",
		border: "none",
		padding: "8px 16px",
		borderRadius: "4px",
		fontSize: "14px",
		fontWeight: 500,
		cursor: "pointer",
		margin: "8px",
	},
	buttonSecondary: {
		backgroundColor: "transparent",
		color: "var(--vscode-button-foreground)",
		border: "1px solid var(--vscode-button-background)",
		padding: "8px 16px",
		borderRadius: "4px",
		fontSize: "14px",
		fontWeight: 500,
		cursor: "pointer",
		margin: "8px",
	},
	importButton: {
		backgroundColor: "var(--vscode-button-background)",
		color: "var(--vscode-button-foreground)",
		border: "none",
		padding: "10px 20px",
		borderRadius: "4px",
		fontSize: "16px",
		fontWeight: 500,
		cursor: "pointer",
		margin: "24px 0",
	},
	navigationButtons: {
		display: "flex",
		justifyContent: "center",
		gap: "16px",
		marginTop: "24px",
	},
	result: {
		padding: "12px",
		marginTop: "16px",
		borderRadius: "4px",
		fontSize: "14px",
	},
	success: {
		backgroundColor: "rgba(0, 200, 0, 0.2)",
		color: "#00c000",
	},
	error: {
		backgroundColor: "rgba(255, 50, 50, 0.2)",
		color: "#ff3232",
	},
	closeButton: {
		position: "absolute" as const,
		top: "16px",
		right: "16px",
		background: "none",
		border: "none",
		color: "var(--vscode-descriptionForeground)",
		cursor: "pointer",
		width: "28px",
		height: "28px",
		display: "flex",
		alignItems: "center",
		justifyContent: "center",
		borderRadius: "50%",
		zIndex: 1,
		transition: "all 0.2s ease",
		opacity: 0.7,
	},
	closeIcon: {
		fontSize: "20px",
		fontWeight: "bold",
	},
	indicatorContainer: {
		position: "relative" as const,
		// marginBottom: "24px",
	},
	dataIcon: {
		marginRight: "8px",
	},
	welcomeProgress: {
		position: "fixed" as const,
		bottom: "48px",
		left: 0,
		width: "100%",
		height: "8px",
		display: "flex",
		gap: "8px",
		justifyContent: "center",
		alignItems: "center",
	},
	progressDot: {
		width: "8px",
		height: "8px",
		borderRadius: "50%",
		backgroundColor: "#868686",
		cursor: "pointer",
		transition: "all 0.2s ease-in",
	},
	activeDot: {
		width: "32px",
		height: "8px",
		borderRadius: "4px",
		backgroundColor: "#f8f8f8",
	},
	// 新增样式
	importStepIconContainer: {
		display: "flex",
		justifyContent: "center",
		gap: "32px",
		marginBottom: "24px",
		alignItems: "center",
	},
	importTitleContainer: {
		textAlign: "center" as const,
	},
	importTitle: {
		fontSize: "20px",
		fontWeight: 600,
		color: "var(--vscode-editor-foreground)",
		margin: "0 0 80px 0",
	},
	importButtonContainer: {
		display: "flex",
		justifyContent: "center",
	},
	importButtonPrimary: {
		width: "320px",
		height: "40px",
		background: "rgba(36, 123, 255, 1)",
		borderRadius: "92px",
		fontSize: "16px",
		fontFamily: "PingFang SC",
		fontWeight: "normal",
		color: "rgba(248, 248, 248, 1)",
		lineHeight: "40px",
		textAlign: "center" as const,
		cursor: "pointer",
		border: 0,
		transition: "background-color 0.2s ease",
		padding: 0,
	},
	importButtonDisabled: {
		opacity: 0.6,
		cursor: "not-allowed",
	},
	skipButtonContainer: {
		marginTop: "auto",
		display: "flex",
		justifyContent: "center",
	},
	skipButton: {
		backgroundColor: "transparent",
		color: "var(--vscode-descriptionForeground)",
		border: "none",
		padding: "8px 16px",
		fontSize: "14px",
		cursor: "pointer",
		marginTop: "16px",
	},
	// 动画相关样式
	animationContainer: {
		display: "flex",
		flexDirection: "row" as const,
		alignItems: "center",
		justifyContent: "center",
		width: "200px",
		gap: "1px",
		height: "45px",
	},
	progressBar: {
		width: "160px",
		height: "6px",
		backgroundColor: "#3C3C3C",
		borderRadius: "3px",
		overflow: "hidden",
		margin: "10px 0",
	},
	progressColumn: {
		width: "20px",
		height: "24px",
		position: "relative" as const,
		clipPath: "polygon(0% 0%, 60% 0%, 100% 50%, 60% 100%, 0% 100%, 40% 50%)",
		backgroundColor: "#3C3C3C",
		transition: "background-color 0.3s ease",
		marginLeft: "-3px",
	},
	progressColumnActive: {
		backgroundColor: "rgba(36, 123, 255, 1)",
	},
	progressFill: {
		height: "100%",
		backgroundColor: "rgba(36, 123, 255, 1)",
		borderRadius: "3px",
		transition: "width 0.3s ease",
	},
	progressIndeterminate: {
		height: "100%",
		backgroundColor: "rgba(36, 123, 255, 1)",
		borderRadius: "3px",
		position: "relative" as const,
		overflow: "hidden",
	},
	progressText: {
		fontSize: "14px",
		color: "#f8f8f8",
		marginTop: "6px",
	},
	// 添加过渡动画相关样式
	transitionContainer: {
		position: "relative" as const,
		width: "100%",
		height: "100%",
		flex: 1,
		overflow: "hidden",
	},
	stepTransition: {
		position: "absolute" as const,
		width: "100%",
		height: "100%",
		display: "flex",
		flexDirection: "column" as const,
		justifyContent: "center",
		alignItems: "center",
	},
};
