{
	"extends": "./tsconfig.base.json",
	"compilerOptions": {
		"jsx": "react",
		"esModuleInterop": true,
		"removeComments": false,
		"preserveConstEnums": true,
		"sourceMap": false,
		"allowJs": true,
		"resolveJsonModule": true,
		"isolatedModules": false,
		"outDir": "../out/vs",
		"types": [
			"@webgpu/types",
			"mocha",
			"semver",
			"sinon",
			"trusted-types",
			"winreg",
			"wicg-file-system-access"
		],
		"plugins": [
			{
				"name": "tsec",
				"exemptionConfig": "./tsec.exemptions.json"
			}
		]
	},
	"include": [
		"./*.ts",
		"./typings",
		"./vs/**/*.ts",
		"./vscode-dts/vscode.proposed.*.d.ts",
		"./vscode-dts/vscode.d.ts",
		"./bootstrap-amd.js",
		"./bootstrap-cli.js",
		"./bootstrap-fork.js",
		"./bootstrap-import.js",
		"./bootstrap-meta.js",
		"./bootstrap-node.js",
		"./bootstrap-server.js",
		"./bootstrap-window.js",
		"./cli.js",
		"./main.js",
		"./server-main.js",
		"./server-cli.js",
		"./vs/base/common/jsonc.js",
		"./vs/base/common/performance.js",
		"./vs/base/node/unc.js",
		"./vs/base/node/nls.js",
		"./vs/platform/environment/node/userDataPath.js",
		"./vs/base/parts/sandbox/electron-sandbox/preload-aux.js",
		"./vs/base/parts/sandbox/electron-sandbox/preload.js",
		"./vs/code/electron-sandbox/processExplorer/processExplorer.js",
		"./vs/code/electron-sandbox/workbench/workbench.js",
		"./vs/workbench/contrib/issue/electron-sandbox/issueReporter.js",
		// JoyCode added these:
		"./vs/workbench/contrib/JoyCoder/browser/**.tsx",
		"./vs/**/*.d.mts"
	]
}
