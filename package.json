{"name": "joycoder-ide", "version": "1.98.2", "distro": "de81a091068fb03e277543d4f4900db005edf9f3", "author": {"name": "JoyCode"}, "license": "MIT", "main": "./out/main.js", "type": "module", "private": true, "scripts": {"dev": "node scripts/start-dev.js", "build:inner-arm": "node update-product-urls.js inner && cd JoyCoder-release-scripts && ./mac-arm64.sh", "build:inner-intel": "node update-product-urls.js inner && cd JoyCoder-release-scripts && ./mac-x64.sh", "build:inner-win": "node update-product-urls.js inner && cd JoyCoder-release-scripts && .\\windows.bat", "build:inner-linux": "node update-product-urls.js inner && cd JoyCoder-release-scripts && ./linux-dev.sh", "build:dev-arm": "node update-product-urls.js prod && cd JoyCoder-release-scripts && ./mac-dev-arm64.sh", "build:dev-intel": "node update-product-urls.js prod && cd JoyCoder-release-scripts && ./mac-dev-x64.sh", "build:dev-linux": "node update-product-urls.js prod && cd JoyCoder-release-scripts && ./linux-dev.sh", "build:dev:no-extensions": "node update-product-urls.js prod && cd JoyCoder-release-scripts && ./mac-dev-no-extensions.sh", "build:dev-win": "node update-product-urls.js prod && cd JoyCoder-release-scripts && .\\windows-dev.bat", "buildreact": "cd ./src/vs/workbench/contrib/JoyCoder/browser/react/ && node build.js && cd ../../../../../../../", "build:win:full:ps": "cd JoyCoder-release-scripts && powershell -ExecutionPolicy Bypass -File .\\windows.ps1", "build:win-simple": "npm run gulp-dev vscode-win32-x64", "build:arm": "node update-product-urls.js prod && cd JoyCoder-release-scripts && ./mac-arm64.sh", "build:intel": "node update-product-urls.js prod && cd JoyCoder-release-scripts && ./mac-x64.sh", "build:win": "node update-product-urls.js prod && cd JoyCoder-release-scripts && .\\windows.bat", "build:linux": "node update-product-urls.js prod && cd JoyCoder-release-scripts && ./linux-dev.sh", "build:prev-arm": "node update-product-urls.js pre && cd JoyCoder-release-scripts && ./mac-dev-arm64.sh", "build:prev-intel": "node update-product-urls.js pre && cd JoyCoder-release-scripts && ./mac-dev-x64.sh", "build:prev-win": "node update-product-urls.js pre && cd JoyCoder-release-scripts && .\\windows-dev.bat", "build:prev-linux": "node update-product-urls.js pre &&cd JoyCoder-release-scripts && ./linux-dev.sh", "watchreact": "cd ./src/vs/workbench/contrib/JoyCoder/browser/react/ && node build.js --watch && cd ../../../../../../../", "update-commit": "node JoyCoder-release-scripts/update-commit.mjs", "test": "echo Please run any of the test scripts from the scripts folder.", "test-browser": "npx playwright install && node test/unit/browser/index.js", "test-browser-no-install": "node test/unit/browser/index.js", "test-node": "mocha test/unit/node/index.js --delay --ui=tdd --timeout=5000 --exit", "test-node-amd": "mocha test/unit/node/index.amd.js --delay --ui=tdd --timeout=5000 --exit", "test-extension": "vscode-test", "preinstall": "node build/npm/preinstall.js", "postinstall": "node build/npm/postinstall.js", "compile": "node ./node_modules/gulp/bin/gulp.js compile", "watch": "npm-run-all -lp watch-client watch-extensions", "watch-amd": "npm-run-all -lp watch-client-amd watch-extensions", "watchd": "deemon npm run watch", "watch-webd": "deemon npm run watch-web", "kill-watchd": "deemon --kill npm run watch", "kill-watch-webd": "deemon --kill npm run watch-web", "restart-watchd": "deemon --restart npm run watch", "restart-watch-webd": "deemon --restart npm run watch-web", "watch-client": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js watch-client", "watch-client-amd": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js watch-client-amd", "watch-clientd": "deemon npm run watch-client", "kill-watch-clientd": "deemon --kill npm run watch-client", "watch-extensions": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js watch-extensions watch-extension-media", "watch-extensionsd": "deemon npm run watch-extensions", "kill-watch-extensionsd": "deemon --kill npm run watch-extensions", "precommit": "node build/hygiene.js", "gulp": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js", "gulp-dev": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js --no-mangle", "build-arm64-nm": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js vscode-darwin-arm64 --no-mangle && ./JoyCoder-release-scripts/move-joycoder-extension.sh", "move:extension": "node ./JoyCoder-release-scripts/move-extension.mjs", "electron": "node build/lib/electron", "7z": "7z", "update-grammars": "node build/npm/update-all-grammars.mjs", "update-localization-extension": "node build/npm/update-localization-extension.js", "smoketest": "node build/lib/preLaunch.js && cd test/smoke && npm run compile && node test/index.js", "smoketest-no-compile": "cd test/smoke && node test/index.js", "download-builtin-extensions": "node build/lib/builtInExtensions.js", "download-builtin-extensions-cg": "node build/lib/builtInExtensionsCG.js", "monaco-compile-check": "tsc -p src/tsconfig.monaco.json --noEmit", "tsec-compile-check": "node node_modules/tsec/bin/tsec -p src/tsconfig.tsec.json", "vscode-dts-compile-check": "tsc -p src/tsconfig.vscode-dts.json && tsc -p src/tsconfig.vscode-proposed-dts.json", "valid-layers-check": "node build/lib/layersChecker.js", "update-distro": "node build/npm/update-distro.mjs", "web": "echo 'npm run web' is replaced by './scripts/code-server' or './scripts/code-web'", "compile-cli": "gulp compile-cli", "compile-web": "node ./node_modules/gulp/bin/gulp.js compile-web", "watch-web": "node ./node_modules/gulp/bin/gulp.js watch-web", "watch-cli": "node ./node_modules/gulp/bin/gulp.js watch-cli", "eslint": "node build/eslint", "stylelint": "node build/stylelint", "playwright-install": "npm exec playwright install", "compile-build": "node ./node_modules/gulp/bin/gulp.js compile-build", "compile-extensions-build": "node ./node_modules/gulp/bin/gulp.js compile-extensions-build", "minify-vscode": "node ./node_modules/gulp/bin/gulp.js minify-vscode", "minify-vscode-reh": "node ./node_modules/gulp/bin/gulp.js minify-vscode-reh", "minify-vscode-reh-web": "node ./node_modules/gulp/bin/gulp.js minify-vscode-reh-web", "hygiene": "node ./node_modules/gulp/bin/gulp.js hygiene", "core-ci": "node ./node_modules/gulp/bin/gulp.js core-ci", "core-ci-pr": "node ./node_modules/gulp/bin/gulp.js core-ci-pr", "extensions-ci": "node ./node_modules/gulp/bin/gulp.js extensions-ci", "extensions-ci-pr": "node ./node_modules/gulp/bin/gulp.js extensions-ci-pr", "perf": "node scripts/code-perf.js", "pkg:armmac": "create-dmg --volname \"JoyCode\" --window-size 500 300  --app-drop-link 380 150   \"JoyCode.dmg\"  \"../VSCode-darwin-arm64/JoyCode.app\"", "pkg:intelmac": "create-dmg --volname \"JoyCode\" --window-size 500 300  --app-drop-link 380 150   \"JoyCode.dmg\"  \"../VSCode-darwin-x64/JoyCode.app\"", "update-build-ts-version": "npm install typescript@next && tsc -p ./build/tsconfig.build.json"}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@google/generative-ai": "^0.21.0", "@microsoft/1ds-core-js": "^3.2.13", "@microsoft/1ds-post-js": "^3.2.13", "@parcel/watcher": "2.5.1", "@rrweb/record": "^2.0.0-alpha.17", "@rrweb/types": "^2.0.0-alpha.17", "@types/semver": "^7.5.8", "@vscode/deviceid": "^0.1.1", "@vscode/iconv-lite-umd": "0.7.0", "@vscode/policy-watcher": "^1.1.10", "@vscode/proxy-agent": "^0.32.0", "@vscode/ripgrep": "^1.15.10", "@vscode/spdlog": "^0.15.0", "@vscode/sqlite3": "5.1.8-vscode", "@vscode/sudo-prompt": "9.3.1", "@vscode/tree-sitter-wasm": "^0.1.3", "@vscode/vscode-languagedetection": "1.0.21", "@vscode/webview-ui-toolkit": "^1.4.0", "@vscode/windows-mutex": "^0.5.0", "@vscode/windows-process-tree": "^0.6.0", "@vscode/windows-registry": "^1.1.0", "@xterm/addon-clipboard": "^0.2.0-beta.81", "@xterm/addon-image": "^0.9.0-beta.98", "@xterm/addon-ligatures": "^0.10.0-beta.98", "@xterm/addon-progress": "^0.2.0-beta.4", "@xterm/addon-search": "^0.16.0-beta.98", "@xterm/addon-serialize": "^0.14.0-beta.98", "@xterm/addon-unicode11": "^0.9.0-beta.98", "@xterm/addon-webgl": "^0.19.0-beta.98", "@xterm/headless": "^5.6.0-beta.98", "@xterm/xterm": "^5.6.0-beta.98", "ajv": "^8.17.1", "cross-spawn": "^7.0.6", "diff": "^7.0.0", "electron-updater": "^6.3.9", "groq-sdk": "^0.9.0", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.2", "jschardet": "3.1.4", "kerberos": "2.1.1", "lucide-react": "^0.460.0", "minimist": "^1.2.6", "native-is-elevated": "0.7.0", "native-keymap": "^3.3.5", "native-watchdog": "^1.4.1", "node-pty": "1.1.0-beta31", "ollama": "^0.5.11", "open": "^8.4.2", "openai": "^4.76.1", "posthog-node": "^4.3.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-syntax-highlighter": "^15.6.1", "react-transition-group": "^4.4.5", "tas-client-umd": "0.2.0", "v8-inspect-profiler": "^0.1.1", "vscode-oniguruma": "1.7.0", "vscode-regexpp": "^3.1.0", "vscode-textmate": "9.2.0", "yauzl": "^3.0.0", "yazl": "^2.4.3"}, "devDependencies": {"@playwright/test": "^1.50.0", "@stylistic/eslint-plugin-ts": "^2.8.0", "@swc/core": "1.3.62", "@types/cookie": "^0.3.3", "@types/debug": "^4.1.5", "@types/diff": "^6.0.0", "@types/eslint": "^9.6.1", "@types/gulp-concat": "^0.0.37", "@types/gulp-svgmin": "^1.2.1", "@types/http-proxy-agent": "^2.0.1", "@types/kerberos": "^1.1.2", "@types/minimist": "^1.2.1", "@types/mocha": "^9.1.1", "@types/node": "20.x", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-transition-group": "^4.4.12", "@types/sinon": "^10.0.2", "@types/sinon-test": "^2.4.2", "@types/trusted-types": "^1.0.6", "@types/vscode-notebook-renderer": "^1.72.0", "@types/webpack": "^5.28.5", "@types/wicg-file-system-access": "^2020.9.6", "@types/windows-foreground-love": "^0.3.0", "@types/winreg": "^1.2.30", "@types/yauzl": "^2.10.0", "@types/yazl": "^2.4.2", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/experimental-utils": "^5.57.0", "@typescript-eslint/parser": "^6.21.0", "@typescript-eslint/utils": "^8.8.0", "@vscode/gulp-electron": "^1.36.0", "@vscode/l10n-dev": "0.0.35", "@vscode/telemetry-extractor": "^1.10.2", "@vscode/test-cli": "^0.0.6", "@vscode/test-electron": "^2.4.0", "@vscode/test-web": "^0.0.62", "@vscode/v8-heap-parser": "^0.1.0", "@vscode/vscode-perf": "^0.0.19", "@webgpu/types": "^0.1.44", "ansi-colors": "^3.2.3", "asar": "^3.0.3", "chromium-pickle-js": "^0.2.0", "cookie": "^0.7.2", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.9.1", "cssnano": "^6.0.3", "debounce": "^1.0.0", "deemon": "^1.8.0", "electron": "34.3.2", "eslint": "^9.11.1", "eslint-formatter-compact": "^8.40.0", "eslint-plugin-header": "3.1.1", "eslint-plugin-jsdoc": "^50.3.1", "eslint-plugin-local": "^6.0.0", "event-stream": "3.3.4", "fancy-log": "^1.3.3", "file-loader": "^6.2.0", "glob": "^5.0.13", "gulp": "^4.0.0", "gulp-azure-storage": "^0.12.1", "gulp-bom": "^3.0.0", "gulp-buffer": "0.0.2", "gulp-concat": "^2.6.1", "gulp-eslint": "^5.0.0", "gulp-filter": "^5.1.0", "gulp-flatmap": "^1.0.2", "gulp-gunzip": "^1.0.0", "gulp-gzip": "^1.4.2", "gulp-json-editor": "^2.5.0", "gulp-plumber": "^1.2.0", "gulp-rename": "^1.2.0", "gulp-replace": "^0.5.4", "gulp-sourcemaps": "^3.0.0", "gulp-svgmin": "^4.1.0", "gulp-untar": "^0.0.7", "husky": "^0.13.1", "innosetup": "^6.4.1", "istanbul-lib-coverage": "^3.2.0", "istanbul-lib-instrument": "^6.0.1", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.5", "lazy.js": "^0.4.2", "marked": "^15.0.0", "merge-options": "^1.0.1", "mime": "^1.4.1", "minimatch": "^3.0.4", "minimist": "^1.2.6", "mocha": "^10.8.2", "mocha-junit-reporter": "^2.2.1", "mocha-multi-reporters": "^1.5.1", "nodemon": "^3.1.9", "npm-run-all": "^4.1.5", "opn": "^6.0.0", "original-fs": "^1.2.0", "os-browserify": "^0.3.0", "p-all": "^1.0.0", "path-browserify": "^1.0.1", "postcss": "^8.4.33", "postcss-nesting": "^12.0.2", "pump": "^1.0.1", "rcedit": "^1.1.0", "rimraf": "^2.2.8", "scope-tailwind": "^1.0.5", "sinon": "^12.0.1", "sinon-test": "^3.1.3", "source-map": "0.6.1", "source-map-support": "^0.3.2", "style-loader": "^3.3.2", "tailwindcss": "^3.4.14", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsec": "0.2.7", "tslib": "^2.6.3", "tsup": "^8.3.5", "typescript": "^5.8.0-dev.20250207", "typescript-eslint": "^8.8.0", "util": "^0.12.4", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-stream": "^7.0.0", "xml2js": "^0.5.0", "yaserver": "^0.4.0"}, "engines": {"node": ">=20.18.3"}, "overrides": {"node-gyp-build": "4.8.1", "kerberos@2.1.1": {"node-addon-api": "7.1.0"}}, "repository": {"type": "git", "url": ""}, "bugs": {}, "optionalDependencies": {"windows-foreground-love": "0.5.0"}}