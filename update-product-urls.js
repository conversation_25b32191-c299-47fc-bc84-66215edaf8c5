#!/usr/bin/env node

/**
 * 更新product.json中的URL字段
 *
 * 使用方法:
 * node update-product-urls.js [pre|prod]
 *
 * 参数:
 * pre: 使用预发环境的URL值
 * prod: 使用生产环境的URL值
 * 不提供参数时默认使用生产环境的URL值
 *
 * 示例:
 * node update-product-urls.js pre  # 使用预发环境的URL
 * node update-product-urls.js prod # 使用生产环境的URL
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { rm } from 'fs/promises';

// 环境URL配置
const ENV_URLS = {
  // 预发环境
  pre: {
    updateUrl: "https://joycoder-api-inner-pr.jd.com/api/saas/ideVersion/v1/version/joycoder-ide",
    baseUrl: "https://joycoder-api-inner-pr.jd.com",
    loginUrl: "https://pre-joycoder.jd.com/login?ideAppName=JoyCode&fromIde=ide&redirect=0",
    // cloudUrl: "http://11.241.185.182", //11.174.207.32
    cloudUrl: "http://11.174.207.32", //11.174.207.32
    env: "pre"
  },
  // 生产环境
  prod: {
    updateUrl: "https://joycoder-api.jd.com/api/saas/ideVersion/v1/version/joycoder-ide",
    baseUrl: "https://joycoder-api.jd.com",
    loginUrl: "https://joycode.jd.com/login?ideAppName=JoyCode&fromIde=ide&redirect=0",
    cloudUrl: "https://clouddev-api.jdcloud.com",
    env: "prod"
  },
  // inner环境，内网生产环境 【预计2025年6月底，清空该环境】
  inner: {
    updateUrl: "https://joycoder-api-inner.jd.com/api/saas/ideVersion/v1/version/joycoder-ide",
    baseUrl: "https://joycoder-api-inner.jd.com",
    loginUrl: "https://inner-joycoder.jd.com/login?ideAppName=JoyCode&fromIde=ide&redirect=0",
    cloudUrl: "https://clouddev-api.jdcloud.com",
    env: "inner"
  }
};

// 解析命令行参数
function parseArgs() {
  // 默认使用生产环境
  let env = 'prod';

  // 获取命令行参数
  const arg = process.argv[2];

  // 如果提供了有效的环境参数，则使用对应的环境
  if (arg === 'pre' || arg === 'prod' || arg === 'inner') {
    env = arg;
  } else if (arg) {
    console.log(`无效的环境参数: ${arg}`);
    console.log('有效的参数是 "pre"、"prod" 或 "inner"');
    console.log('将使用默认的生产环境(prod)配置');
  }

  console.log(`当前使用的环境: ${env}`);

  // 返回选定环境的URL配置
  return ENV_URLS[env];
}

// 移除jd-inner-extensions文件夹
async function removeInnerExtensions() {
  try {
    // 获取当前文件的目录
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);

    // jd-inner-extensions文件夹路径
    const innerExtensionsPath = path.join(__dirname, 'jd-inner-extensions');

    // 检查文件夹是否存在
    if (fs.existsSync(innerExtensionsPath)) {
      console.log(`正在移除 ${innerExtensionsPath} 文件夹...`);
      await rm(innerExtensionsPath, { recursive: true, force: true });
      console.log(`成功移除 ${innerExtensionsPath} 文件夹`);
    } else {
      console.log(`${innerExtensionsPath} 文件夹不存在，无需移除`);
    }
  } catch (error) {
    console.error(`移除jd-inner-extensions文件夹时出错: ${error.message}`);
    // 继续执行，不因为移除文件夹失败而中断整个程序
  }
}

// 主函数
async function main() {
  try {
    // 移除jd-inner-extensions文件夹
    await removeInnerExtensions();

    // 解析命令行参数
    const args = parseArgs();

    // 显示将使用的URL值
    console.log('将使用以下URL值:');
    console.log(`  updateUrl: ${args.updateUrl}`);
    console.log(`  baseUrl: ${args.baseUrl}`);
    console.log(`  loginUrl: ${args.loginUrl}`);

    // 获取当前文件的目录
    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);

    // 读取product.json文件
    const productJsonPath = path.join(__dirname, 'product.json');
    console.log(`正在读取 ${productJsonPath}`);

    let productJson;
    try {
      const fileContent = fs.readFileSync(productJsonPath, 'utf8');
      productJson = JSON.parse(fileContent);
    } catch (error) {
      console.error(`无法读取或解析product.json文件: ${error.message}`);
      process.exit(1);
    }

    // 记录原始值
    console.log('原始值:');
    console.log(`  updateUrl: ${productJson.updateUrl || '未设置'}`);
    console.log(`  joyCoderBaseUrl: ${productJson.joyCoderBaseUrl || '未设置'}`);
    console.log(`  joyCoderLoginUrl: ${productJson.joyCoderLoginUrl || '未设置'}`);
    console.log(`  joyCoderEnv: ${productJson.joyCoderEnv || '未设置'}`);
    console.log(`  joyCoderCloudBaseUrl: ${productJson.joyCoderCloudBaseUrl || '未设置'}`);

    // 更新字段
    productJson.updateUrl = args.updateUrl;
    productJson.joyCoderBaseUrl = args.baseUrl;
    productJson.joyCoderLoginUrl = args.loginUrl;
    productJson.joyCoderEnv = args.env;
    productJson.joyCoderCloudBaseUrl = args.cloudUrl;

    // 记录新值
    console.log('\n新值:');
    console.log(`  updateUrl: ${productJson.updateUrl || '未设置'}`);
    console.log(`  joyCoderBaseUrl: ${productJson.joyCoderBaseUrl || '未设置'}`);
    console.log(`  joyCoderLoginUrl: ${productJson.joyCoderLoginUrl || '未设置'}`);
    console.log(`  joyCoderEnv: ${productJson.joyCoderEnv || '未设置'}`);
    console.log(`  joyCoderCloudBaseUrl: ${productJson.joyCoderCloudBaseUrl || '未设置'}`);

    // 写回文件
    try {
      // 使用格式化的JSON，保持缩进一致
      const updatedContent = JSON.stringify(productJson, null, 2);
      fs.writeFileSync(productJsonPath, updatedContent, 'utf8');
      console.log(`\n成功更新 ${productJsonPath}`);
    } catch (error) {
      console.error(`写入文件时出错: ${error.message}`);
      process.exit(1);
    }

  } catch (error) {
    console.error(`执行过程中出错: ${error.message}`);
    process.exit(1);
  }
}

// 执行主函数
main().catch(error => {
  console.error(`未处理的错误: ${error.message}`);
  process.exit(1);
});
