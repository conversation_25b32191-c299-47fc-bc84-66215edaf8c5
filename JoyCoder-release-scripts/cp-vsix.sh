
#!/bin/bash

# 检查是否提供了足够的参数
if [ $# -lt 2 ]; then
    echo "请提供两个参数：插件前缀和插件名称前缀。例如：./cp-vsix.sh joycoder-editor joycoder"
    exit 1
fi

PREFIX=$1
PLUGIN_PREFIX=$2
WORKDIR=../joycoder-extensions
TARGET_DIR=../jd-inner-extensions

# 检查工作目录是否存在，如果不存在则创建
if [ ! -d "$WORKDIR" ]; then
    echo "工作目录 $WORKDIR 不存在，正在创建..."
    mkdir -p "$WORKDIR"
fi

# 检查目标目录是否存在，如果不存在则创建
if [ ! -d "$TARGET_DIR" ]; then
    echo "目标目录 $TARGET_DIR 不存在，正在创建..."
    mkdir -p "$TARGET_DIR"
fi

echo "构建内置插件====="
	joycoder_editor_file=$(ls ${WORKDIR} | grep ${PREFIX} | head -n 1)

	if [ -z "$joycoder_editor_file" ]; then
		echo "未找到匹配的文件，跳过处理"
		exit 0
	fi

	joycoder_editor_unzip=${joycoder_editor_file%.vsix}

	echo "joycoder_editor_file: $joycoder_editor_file"
	echo "joycoder_editor_unzip: $joycoder_editor_unzip"

	if [ ! -f "${WORKDIR}/$joycoder_editor_file" ]; then
		echo "文件 ${WORKDIR}/$joycoder_editor_file 不存在，跳过处理"
		exit 0
	fi

	rm -rf ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}
	unzip ${WORKDIR}/$joycoder_editor_file -d ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}

	if [ ! -d "${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}" ]; then
		echo "解压失败，跳过后续处理"
		exit 0
	fi

	mv ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}/extension.vsixmanifest ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}/extension/.vsixmanifest

	rm -rf ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}/[Content_Types].xml

	cp -r ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}/extension/. ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}/

	rm -rf ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}/extension

	rm -rf ${WORKDIR}/extensions/${PLUGIN_PREFIX}.${joycoder_editor_unzip}
	rm -rf ${TARGET_DIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip}
	mv ${WORKDIR}/${PLUGIN_PREFIX}.${joycoder_editor_unzip} ${TARGET_DIR}

	echo "构建内置插件=====end"
