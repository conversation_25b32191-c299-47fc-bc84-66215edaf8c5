/* eslint-disable */

import fs from 'node:fs';
import path from 'node:path';
import { execSync } from 'node:child_process';
import { promisify } from 'node:util';
import os from 'node:os';
import { fileURLToPath } from 'node:url';

// ES模块中替代 __dirname 和 __filename
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const fsExists = promisify(fs.exists);
const fsReaddir = promisify(fs.readdir);
const fsMkdir = promisify(fs.mkdir);
const fsRm = promisify(fs.rm);
const fsCp = promisify(fs.cp);

// 获取系统信息
const platform = os.platform();
const arch = os.arch();
const isMac = platform === 'darwin';
const isWindows = platform === 'win32';
const isLinux = platform === 'linux';
const isMacArm = isMac && arch === 'arm64';
const isMacIntel = isMac && arch === 'x64';

console.log(`检测到系统: ${platform}, 架构: ${arch}`);

// 默认配置 - 根据平台设置不同的默认值
let DEFAULT_TARGET = "VSCode-darwin-arm64";
if (isMacIntel) {
	DEFAULT_TARGET = "VSCode-darwin-x64";
} else if (isWindows) {
	DEFAULT_TARGET = "VSCode-win32-x64";
} else if (isLinux) {
	DEFAULT_TARGET = "VSCode-linux-x64";
}

const DEFAULT_VSIX_PREFIX = "joycoder-editor-";
const DEFAULT_PLUGIN_PREFIX = "joycoder";
const DEFAULT_VSIXNAME = "joycoder.joycoder-editor";

/**
 * 处理VSIX文件
 * @param {string} vsixPrefix - VSIX文件前缀
 * @param {string} pluginPrefix - 插件前缀
 */
async function processVsix(vsixPrefix = DEFAULT_VSIX_PREFIX, pluginPrefix = DEFAULT_PLUGIN_PREFIX) {
	console.log(`处理VSIX文件，前缀: ${vsixPrefix}, 插件前缀: ${pluginPrefix}`);

	// 设置工作目录
	const workDir = path.resolve(__dirname, '..', 'joycoder-extensions');
	const targetDir = path.resolve(__dirname, '..', 'jd-inner-extensions');

	// 确保目标目录存在
	if (!await fsExists(targetDir)) {
		await fsMkdir(targetDir, { recursive: true });
	}

	// 查找匹配的VSIX文件
	const files = await fsReaddir(workDir);
	const joycoder_editor_file = files.find(file => file.includes(vsixPrefix));

	if (!joycoder_editor_file) {
		console.log("未找到匹配的文件，跳过处理");
		return;
	}

	const joycoder_editor_unzip = joycoder_editor_file.replace('.vsix', '');

	console.log(`joycoder_editor_file: ${joycoder_editor_file}`);
	console.log(`joycoder_editor_unzip: ${joycoder_editor_unzip}`);

	const vsixPath = path.join(workDir, joycoder_editor_file);
	if (!await fsExists(vsixPath)) {
		console.log(`文件 ${vsixPath} 不存在，跳过处理`);
		return;
	}

	// 解压VSIX文件
	const extractDir = path.join(workDir, `${pluginPrefix}.${joycoder_editor_unzip}`);

	// 删除旧的解压目录（如果存在）
	await forceCleanDirectory(extractDir);

	// 根据不同平台使用不同的解压方法
	try {
		// 创建解压目录
		await fsMkdir(extractDir, { recursive: true });

		if (isWindows) {
			// Windows 环境下尝试多种解压方法
			console.log("在 Windows 环境下解压 VSIX 文件");

			// 方法1: 使用 PowerShell 的 Expand-Archive 命令
			try {
				// 创建临时 .zip 文件
				const tempZipPath = path.join(workDir, `${joycoder_editor_unzip}.zip`);
				fs.copyFileSync(vsixPath, tempZipPath);

				// 使用 PowerShell 的 Expand-Archive 命令解压
				execSync(`powershell -command "Expand-Archive -Path '${tempZipPath}' -DestinationPath '${extractDir}' -Force"`, { stdio: 'inherit' });

				// 删除临时 .zip 文件
				fs.unlinkSync(tempZipPath);
				console.log("使用 PowerShell Expand-Archive 解压成功");
			} catch (psError) {
				console.warn(`PowerShell 解压失败: ${psError.message}`);

				// 方法2: 使用 7-Zip (如果安装了)
				try {
					console.log("尝试使用 7-Zip 解压...");
					execSync(`7z x "${vsixPath}" -o"${extractDir}" -y`, { stdio: 'inherit' });
					console.log("使用 7-Zip 解压成功");
				} catch (zipError) {
					console.warn(`7-Zip 解压失败: ${zipError.message}`);

					// 方法3: 使用 Node.js 内置的解压模块
					console.log("尝试使用 Node.js 内置解压方法...");
				}
			}
		} else {
			// Mac/Linux 使用 unzip 命令
			console.log("在 Mac/Linux 环境下解压 VSIX 文件");
			execSync(`unzip "${vsixPath}" -d "${extractDir}"`, { stdio: 'inherit' });
		}
	} catch (error) {
		console.error("解压失败:", error);
		return;
	}

	if (!await fsExists(extractDir)) {
		console.log("解压失败，跳过后续处理");
		return;
	}

	// 处理解压后的文件
	try {
		// 移动vsixmanifest文件
		const vsixManifestPath = path.join(extractDir, 'extension.vsixmanifest');
		const vsixManifestTargetPath = path.join(extractDir, 'extension', '.vsixmanifest');

		if (await fsExists(vsixManifestPath)) {
			// 确保目标目录存在
			const extensionDir = path.join(extractDir, 'extension');
			if (!await fsExists(extensionDir)) {
				await fsMkdir(extensionDir, { recursive: true });
			}
			fs.renameSync(vsixManifestPath, vsixManifestTargetPath);
		}

		// 删除[Content_Types].xml
		const contentTypesPath = path.join(extractDir, '[Content_Types].xml');
		if (await fsExists(contentTypesPath)) {
			await fsRm(contentTypesPath);
		}

		// 复制extension目录内容到解压根目录
		const extensionDir = path.join(extractDir, 'extension');
		if (await fsExists(extensionDir)) {
			const files = await fsReaddir(extensionDir);

			for (const file of files) {
				await fsCp(
					path.join(extensionDir, file),
					path.join(extractDir, file),
					{ recursive: true }
				);
			}

			// 删除extension目录 - 使用增强的强制清理函数
			await forceCleanDirectory(extensionDir);
		}

		// 删除旧的目标目录（如果存在）
		const targetExtractDir = path.join(targetDir, `${pluginPrefix}.${joycoder_editor_unzip}`);
		await forceCleanDirectory(targetExtractDir);

		// 移动处理后的目录到目标目录
		try {
			console.log(`尝试移动目录: ${extractDir} -> ${targetExtractDir}`);
			fs.renameSync(extractDir, targetExtractDir);
			console.log("目录移动成功");
		} catch (renameError) {
			console.warn(`重命名操作失败: ${renameError.message}，尝试使用复制+删除的方式`);

			// 备用方法：使用复制+删除的方式
			try {
				// 确保目标目录存在
				if (!await fsExists(path.dirname(targetExtractDir))) {
					await fsMkdir(path.dirname(targetExtractDir), { recursive: true });
				}

				// 复制目录内容
				await fsCp(extractDir, targetExtractDir, { recursive: true, force: true });
				console.log(`复制目录成功: ${extractDir} -> ${targetExtractDir}`);

				// 删除源目录 - 使用强制清理函数
				await forceCleanDirectory(extractDir);
			} catch (cpError) {
				throw new Error(`备用复制方法也失败: ${cpError.message}`);
			}
		}

		console.log("VSIX处理完成");
	} catch (error) {
		console.error("处理VSIX文件时出错:", error);
		throw error; // 重新抛出错误，确保主函数能捕获到
	}
}

/**
 * 移动扩展到目标目录
 * @param {string} targetDirName - 目标目录名称
 * @param {string} vsixName - VSIX名称
 */
async function moveExtension(targetDirName = DEFAULT_TARGET, vsixName = DEFAULT_VSIXNAME) {
	console.log(`目标目录: ${targetDirName}`);

	// 设置工作目录为项目根目录
	const rootDir = path.resolve(__dirname, '..');
	const sourceDir = path.join(rootDir, 'jd-inner-extensions');
	const desktopDir = path.dirname(rootDir);
	const targetBaseDir = path.join(desktopDir, targetDirName);

	// 根据不同的操作系统设置不同的目标路径
	let targetDir, targetDirYml;

	if (isMac) {
		// macOS路径
		targetDir = path.join(targetBaseDir, 'JoyCode.app/Contents/Resources/app/extensions');
		targetDirYml = path.join(targetBaseDir, 'JoyCode.app/Contents/Resources/app');
	} else if (isWindows) {
		// Windows路径
		targetDir = path.join(targetBaseDir, 'resources/app/extensions');
		targetDirYml = path.join(targetBaseDir, 'resources/app');
	} else if (isLinux) {
		// Linux路径
		targetDir = path.join(targetBaseDir, 'resources/app/extensions');
		targetDirYml = path.join(targetBaseDir, 'resources/app');
	} else {
		console.error("不支持的操作系统");
		process.exit(1);
	}

	console.log("路径信息:");
	console.log(`当前项目目录: ${rootDir}`);
	console.log(`目标父目录: ${desktopDir}`);
	console.log(`目标应用目录: ${targetBaseDir}`);
	console.log(`目标扩展目录: ${targetDir}`);
	console.log(`目标YML目录: ${targetDirYml}`);

	// 确保目标目录不在当前项目内
	if (targetBaseDir.startsWith(rootDir)) {
		console.log("错误: 目标目录不能在当前项目内");
		process.exit(1);
	}

	// 检查源目录是否存在
	if (!await fsExists(sourceDir)) {
		console.log(`错误: 源目录 ${sourceDir} 不存在`);
		console.log(`当前工作目录: ${rootDir}`);
		process.exit(1);
	}

	// 检查目标应用目录是否存在
	if (!await fsExists(targetBaseDir)) {
		console.log(`错误: 目标应用目录 ${targetBaseDir} 不存在`);
		console.log(`请确保已经构建了目标应用`);
		process.exit(1);
	}

	// 创建目标目录（如果不存在）
	await fsMkdir(targetDir, { recursive: true });
	await fsMkdir(targetDirYml, { recursive: true });

	// 删除目标目录中特定前缀的内容
	console.log("正在删除目标目录中的特定前缀旧内容...");
	const prefixesToDelete = [vsixName];

	for (const prefix of prefixesToDelete) {
		try {
			// 使用Node.js查找匹配的目录
			const dirs = await fsReaddir(targetDir);
			const matchingDirs = dirs.filter(dir => dir.startsWith(prefix));

			if (matchingDirs.length > 0) {
				for (const dir of matchingDirs) {
					const dirPath = path.join(targetDir, dir);
					await fsRm(dirPath, { recursive: true });
					console.log(`已删除: ${dirPath}`);
				}
			} else {
				console.log(`没有找到匹配前缀 ${prefix} 的目录`);
			}
		} catch (error) {
			console.error(`删除目录时出错:`, error);
		}
	}

	// 复制扩展文件夹内的内容
	console.log("正在复制扩展文件夹内容...");
	console.log(`源路径: ${sourceDir}`);
	console.log(`目标路径: ${targetDir}`);

	try {
		// 复制源目录内容到目标目录
		// 在Windows上使用不同的复制方式
		if (isWindows) {
			// Windows上使用robocopy命令，它有更好的权限处理
			console.log("在 Windows 环境下使用 robocopy 复制文件");

			try {
				// 使用robocopy命令复制目录
				// /E - 复制子目录，包括空目录
				// /COPY:DAT - 复制数据、属性和时间戳（不复制安全信息）
				// /R:3 - 重试3次
				// /W:1 - 重试间隔1秒
				// /NFL - 不显示文件名列表
				// /NDL - 不显示目录名列表
				execSync(`robocopy "${sourceDir}" "${targetDir}" /E /COPY:DAT /R:3 /W:1 /NFL /NDL`, { stdio: 'inherit' });
				console.log("使用 robocopy 复制成功");
			} catch (error) {
				// robocopy 返回特殊的退出代码，即使成功复制也可能返回非零值
				// 0 - 没有文件被复制（没有错误）
				// 1 - 文件被成功复制
				// 2 - 额外的文件或目录被检测到（没有错误）
				// 4 - 一些不匹配的文件或目录被检测到（没有错误）
				// 8 - 一些文件或目录不能被复制（有错误）
				// 16 - 严重错误
				// 因此，我们只在退出代码大于7时才认为是错误
				if (error.status > 7) {
					console.error("robocopy 复制失败，尝试使用备用方法");

					// 备用方法：使用 Node.js 的 fs.cp
					try {
						const files = await fsReaddir(sourceDir);
						for (const file of files) {
							const sourcePath = path.join(sourceDir, file);
							const targetPath = path.join(targetDir, file);

							// 如果目标路径已存在，先尝试删除
							if (await fsExists(targetPath)) {
								try {
									await fsRm(targetPath, { recursive: true, force: true });
								} catch (rmError) {
									console.warn(`无法删除目标路径 ${targetPath}，错误: ${rmError.message}`);
								}
							}

							// 复制文件或目录
							await fsCp(sourcePath, targetPath, { recursive: true, force: true });
						}
					} catch (cpError) {
						throw new Error(`复制失败: ${cpError.message}`);
					}
				} else {
					console.log("robocopy 完成，返回代码:", error.status);
				}
			}
		} else {
			// Mac/Linux使用标准复制
			await fsCp(sourceDir, targetDir, { recursive: true });
		}

		// 复制app-update.yml和dev-app-update.yml到TARGET_DIR_YML
		const appUpdatePath = path.join(rootDir, 'app-update.yml');
		const devAppUpdatePath = path.join(rootDir, 'dev-app-update.yml');

		if (await fsExists(appUpdatePath)) {
			await fsCp(appUpdatePath, path.join(targetDirYml, 'app-update.yml'));
			console.log(`已复制 app-update.yml 到 ${targetDirYml}`);
		} else {
			console.log(`警告: app-update.yml 不存在于 ${rootDir}`);
		}

		if (await fsExists(devAppUpdatePath)) {
			await fsCp(devAppUpdatePath, path.join(targetDirYml, 'dev-app-update.yml'));
			console.log(`已复制 dev-app-update.yml 到 ${targetDirYml}`);
		} else {
			console.log(`警告: dev-app-update.yml 不存在于 ${rootDir}`);
		}

		console.log("扩展复制成功！");
	} catch (error) {
		console.error("复制扩展时发生错误:", error);
		process.exit(1);
	}
}

/**
 * 强制清理目录函数
 * @param {string} dirPath - 要清理的目录路径
 */
async function forceCleanDirectory(dirPath) {
	console.log(`开始强制清理目录: ${dirPath}`);

	if (!await fsExists(dirPath)) {
		console.log(`目录不存在，无需清理: ${dirPath}`);
		return;
	}

	// 方法1: 使用 fs.rm 并增加重试机制
	for (let attempt = 1; attempt <= 3; attempt++) {
		try {
			console.log(`尝试使用 fs.rm 删除目录 (尝试 ${attempt}/3)...`);
			await fsRm(dirPath, { recursive: true, force: true, maxRetries: 5 });
			console.log(`使用 fs.rm 删除成功`);
			return;
		} catch (rmError) {
			console.warn(`使用 fs.rm 删除失败 (尝试 ${attempt}/3): ${rmError.message}`);
			// 短暂延迟后重试
			if (attempt < 3) {
				await new Promise(resolve => setTimeout(resolve, 500));
			}
		}
	}

	// 方法2: 使用系统命令，Windows 下使用更强力的删除命令
	try {
		console.log(`尝试使用系统命令删除目录...`);
		if (isWindows) {
			// 尝试使用 PowerShell 的 Remove-Item 命令，它有更好的处理非空目录的能力
			try {
				execSync(`powershell -command "Remove-Item -Path '${dirPath}' -Recurse -Force"`, { stdio: 'inherit' });
				console.log(`使用 PowerShell Remove-Item 删除成功`);
				return;
			} catch (psError) {
				console.warn(`PowerShell 删除失败: ${psError.message}`);
				// 回退到 rd 命令
				execSync(`rd /s /q "${dirPath}"`, { stdio: 'inherit' });
			}
		} else {
			execSync(`rm -rf "${dirPath}"`, { stdio: 'inherit' });
		}
		console.log(`使用系统命令删除成功`);
		return;
	} catch (cmdError) {
		console.warn(`使用系统命令删除失败: ${cmdError.message}`);
	}

	// 方法3: 增强的逐个删除文件和子目录
	try {
		console.log(`尝试增强的逐个删除文件和子目录...`);

		// 递归删除函数
		const recursiveDelete = async (currentPath) => {
			if (!await fsExists(currentPath)) {
				return;
			}

			try {
				const entries = await fsReaddir(currentPath);

				// 先处理所有文件，再处理目录
				const stats = await Promise.all(
					entries.map(async (entry) => {
						const entryPath = path.join(currentPath, entry);
						const stat = await fs.promises.stat(entryPath);
						return { path: entryPath, isDirectory: stat.isDirectory() };
					})
				);

				// 先处理文件
				for (const entry of stats.filter(e => !e.isDirectory)) {
					try {
						await fs.promises.unlink(entry.path);
						console.log(`删除文件: ${entry.path}`);
					} catch (e) {
						console.warn(`无法删除文件 ${entry.path}: ${e.message}`);
					}
				}

				// 再处理目录
				for (const entry of stats.filter(e => e.isDirectory)) {
					await recursiveDelete(entry.path);
				}

				// 最后删除当前目录
				await fs.promises.rmdir(currentPath);
				console.log(`删除目录: ${currentPath}`);
			} catch (e) {
				console.warn(`处理目录 ${currentPath} 时出错: ${e.message}`);

				// 如果是 Windows，尝试使用 PowerShell 删除单个目录
				if (isWindows) {
					try {
						execSync(`powershell -command "Remove-Item -Path '${currentPath}' -Force"`, { stdio: 'inherit' });
						console.log(`使用 PowerShell 删除单个目录成功: ${currentPath}`);
					} catch (psError) {
						console.warn(`PowerShell 删除单个目录失败: ${psError.message}`);
					}
				}
			}
		};

		// 开始递归删除
		await recursiveDelete(dirPath);

		// 检查目录是否已被删除
		if (!await fsExists(dirPath)) {
			console.log(`增强的逐个删除成功`);
			return;
		}
	} catch (emptyError) {
		console.error(`增强的逐个删除后仍然无法删除目录: ${emptyError.message}`);
	}

	// 方法4: 最后尝试使用 rimraf (如果可用)
	if (isWindows) {
		try {
			console.log(`尝试使用 rimraf 命令删除目录...`);
			// 使用 Node.js 的 child_process 执行 rimraf 命令
			execSync(`npx rimraf "${dirPath}"`, { stdio: 'inherit' });

			// 检查目录是否已被删除
			if (!await fsExists(dirPath)) {
				console.log(`使用 rimraf 删除成功`);
				return;
			}
		} catch (rimrafError) {
			console.warn(`使用 rimraf 删除失败: ${rimrafError.message}`);
		}
	}

	console.warn(`警告: 无法完全清理目录 ${dirPath}，可能需要手动删除`);
}

/**
 * 主函数
 */
async function main() {
	const args = process.argv.slice(2);

	// 解析命令行参数
	let vsixPrefix = DEFAULT_VSIX_PREFIX;
	let pluginPrefix = DEFAULT_PLUGIN_PREFIX;
	let targetDirName = DEFAULT_TARGET;
	let vsixName = DEFAULT_VSIXNAME;

	// 检查是否有清理参数
	if (args.includes('--clean')) {
		console.log("仅执行清理操作");

		// 设置工作目录
		const workDir = path.resolve(__dirname, '..', 'joycoder-extensions');
		const extractDir = path.join(workDir, `${DEFAULT_PLUGIN_PREFIX}.${DEFAULT_VSIX_PREFIX}2.7.0`);

		// 强制清理目录
		await forceCleanDirectory(extractDir);
		console.log("清理操作完成");
		return;
	}

	if (args.length >= 2) {
		vsixPrefix = args[0];
		pluginPrefix = args[1];
	}

	if (args.length >= 3) {
		targetDirName = args[2];
	}

	if (args.length >= 4) {
		vsixName = args[3];
	}

	try {
		// 根据不同的操作系统执行不同的处理逻辑
		console.log(`开始处理扩展文件，平台: ${platform}, 架构: ${arch}`);

		if (isMac) {
			console.log("在 macOS 环境下运行");
			if (isMacArm) {
				console.log("检测到 ARM 架构");
				// 如果没有指定目标目录，则使用 ARM 的默认目录
				if (args.length < 3) {
					targetDirName = "VSCode-darwin-arm64";
				}
			} else if (isMacIntel) {
				console.log("检测到 Intel 架构");
				// 如果没有指定目标目录，则使用 Intel 的默认目录
				if (args.length < 3) {
					targetDirName = "VSCode-darwin-x64";
				}
			}
		} else if (isWindows) {
			console.log("在 Windows 环境下运行");
			// 如果没有指定目标目录，则使用 Windows 的默认目录
			if (args.length < 3) {
				targetDirName = "VSCode-win32-x64";
			}
		} else if (isLinux) {
			console.log("在 Linux 环境下运行");
			// 如果没有指定目标目录，则使用 Linux 的默认目录
			if (args.length < 3) {
				targetDirName = "VSCode-linux-x64";
			}
		} else {
			console.warn(`不支持的操作系统: ${platform}`);
		}

		console.log(`使用目标目录: ${targetDirName}`);

		// 设置工作目录
		const workDir = path.resolve(__dirname, '..', 'joycoder-extensions');
		const extractDir = path.join(workDir, `${pluginPrefix}.${vsixPrefix}2.7.0`);

		// 先清理可能存在的旧目录
		await forceCleanDirectory(extractDir);

		// 处理VSIX文件
		await processVsix(vsixPrefix, pluginPrefix);

		// 移动扩展到目标目录
		await moveExtension(targetDirName, vsixName);

		// 最后再次清理源目录，确保不留下残余
		await forceCleanDirectory(extractDir);

		console.log("所有操作完成！");
	} catch (error) {
		console.error("执行过程中出错:", error);
		process.exit(1);
	}
}

// 设置更详细的错误处理
process.on('uncaughtException', (error) => {
	console.error("未捕获的异常:", error);
	process.exit(1);
});

process.on('unhandledRejection', (reason) => {
	console.error("未处理的Promise拒绝:", reason);
	process.exit(1);
});

// 执行主函数
console.log("开始执行脚本...");
main().then(() => {
	console.log("脚本执行完成");
}).catch(error => {
	console.error("脚本执行失败:", error);
	process.exit(1);
});
