# Do not run this unless you know what you're doing.
# Don't run this when JoyCode is open, or <PERSON> will confuse the two versions (run in terminal or VS Code).

set -e

./import_certificates.sh 20241218
# To fix /Volumes/JoyCode errors, DO NOT RUN IN VOID!!!
# To fix permission errors, sudo chmod -r +rwx ~/Desktop/void
# Run in sudo if have errors
# Build, sign and package arm64

 ./mac-sign.sh buildDev arm64 dev
# 2c1d27132f1f05b058f67bc8bb7cbe71e7bf2027
npm run update-commit bf58862d8d60ad8747b8588ad1b8c1ae956ddad6
./cp-vsix.sh joycoder-editor- joycoder
./cp-vsix.sh clouddev- jdcom
./move-joycoder-extension.sh VSCode-darwin-arm64 joycoder.joycoder-editor
./move-joycoder-extension.sh VSCode-darwin-arm64 jdcom.clouddev
# ./mac-sign.sh buildreh arm64
./mac-sign.sh sign arm64
./mac-sign.sh notarize arm64
 ./mac-sign.sh rawapp arm64
# ./mac-sign.sh hashrawapp arm64
# ./mac-sign.sh packagereh arm64

