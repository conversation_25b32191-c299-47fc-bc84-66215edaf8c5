# Keychain Access Entitlements

This document explains the changes made to the signing process to enable maximum keychain access permissions for the JoyCode application.

## Changes Made

1. Created custom entitlements files with the `com.apple.security.keychain` entitlement:
   - `custom-app-entitlements.plist` - For the main application
   - `custom-helper-gpu-entitlements.plist` - For the GPU helper
   - `custom-helper-renderer-entitlements.plist` - For the renderer helper
   - `custom-helper-plugin-entitlements.plist` - For the plugin helper

2. Modified the `mac-sign.sh` script to:
   - Copy these custom entitlements files to the location expected by sign.js
   - Add additional explicit codesigning steps with the custom entitlements

## Why These Changes Are Needed

The `com.apple.security.keychain` entitlement allows the application to access the keychain without prompting the user for permission each time. This is particularly useful for applications that need to store and retrieve sensitive information from the keychain.

Additionally, the following entitlements were added to enhance functionality:
- `com.apple.security.cs.allow-unsigned-executable-memory` - Allows the app to generate executable memory that doesn't have to be signed
- `com.apple.security.cs.disable-library-validation` - Allows the app to load arbitrary libraries

## How to Use

Simply run the signing process as usual:

```bash
./mac-sign.sh sign arm64
# or
./mac-sign.sh sign x64
```

The script will automatically use the custom entitlements files.

## Verification

After signing, you can verify that the entitlements were properly applied using:

```bash
codesign -d --entitlements :- /path/to/JoyCode.app
```

You should see the `com.apple.security.keychain` entitlement in the output.
