#!/usr/bin/env bash

set -e # Exit on error

echo "-------------------- Running $1 on x64 --------------------"
ARCH="x64"
DEV_PACKAGE=$2
if [ -n "$DEV_PACKAGE" ] && [ "$DEV_PACKAGE" = "dev" ]; then
    DEV_PACKAGE='--no-mangle'
    echo "-------------------- package dev --------------------"
fi


USER_HOME="$(dirname $(pwd))"
USER_HOME_UP="$(dirname $(dirname $(pwd)))"

# Required variables (store these values in mac-env.sh and copy them over to run this script):
ORIGINAL_DOTAPP_DIR="/Users/<USER>/Documents/Joycoder-IDE-workspace/VSCode-darwin-${ARCH}" # location of original (nothing is modified in this dir, just copied away from it)
ORIGINAL_REH_DIR="${USER_HOME}/vscode-reh-darwin-${ARCH}"
VOID_DIR="${USER_HOME}"
WORKING_DIR="${USER_HOME}/JoyCodeSign/JoyCodeSign-${ARCH}" # temp dir for all the work here
P12_FILE="${USER_HOME}/JoyCoder-release-scripts/developerID_application.p12"
P12_PASSWORD="********"
APPLE_ID="<EMAIL>" # see https://appleid.apple.com
APP_PASSWORD="nbrx-yoyx-tkto-dapl" # see https://appleid.apple.com
TEAM_ID="F6Q9F9N36U" # see https://developer.apple.com/account/resources/identifiers/list
CODESIGN_IDENTITY="Developer ID Application: Beijing Haiyi Tongzhan Information Technology Co., Ltd (F6Q9F9N36U)" #Developer ID Application: ... try `security find-identity -v -p codesigning`
KEYCHAIN_PROFILE_NAME="JoyCode" # this doesnt seem to do anything but is required


# Check if all required variables are set
if [ -z "$ORIGINAL_DOTAPP_DIR" ] || [ -z "$WORKING_DIR" ] || [ -z "$P12_FILE" ] || [ -z "$P12_PASSWORD" ] || [ -z "$APPLE_ID" ] || [ -z "$TEAM_ID" ] || [ -z "$APP_PASSWORD" ] || [ -z "$CODESIGN_IDENTITY" ]; then
    echo "Error: Make sure to set all variables."
    exit 1
fi


## computedx
KEYCHAIN_DIR="${WORKING_DIR}/1_Keychain"
KEYCHAIN="${KEYCHAIN_DIR}/buildagent.keychain"

SIGN_DIR="${WORKING_DIR}/2_Signed"
SIGNED_DOTAPP_DIR="${SIGN_DIR}/VSCode-darwin-${ARCH}"
SIGNED_DOTAPP="${SIGN_DIR}/VSCode-darwin-${ARCH}/JoyCode.app"

SIGNED_DMG_DIR="${SIGN_DIR}/VSCode-darwin-${ARCH}"
SIGNED_DMG="${SIGN_DIR}/VSCode-darwin-${ARCH}/JoyCode-Installer-darwin-${ARCH}.dmg"


copy_framework() {
    local framework="$1"
    local source_dir="$2"
    local target_dir="$3"

    echo "Copying framework: ${framework}"

    # 创建框架目录
    mkdir -p "${target_dir}/Contents/Frameworks/${framework}.framework"

    # 复制框架内容
    (cd "${source_dir}/Contents/Frameworks/${framework}.framework" && \
        tar cf - .) | (cd "${target_dir}/Contents/Frameworks/${framework}.framework" && tar xf -)

    # 重新创建版本符号链接
    if [ -d "${target_dir}/Contents/Frameworks/${framework}.framework/Versions" ]; then
        cd "${target_dir}/Contents/Frameworks/${framework}.framework"

        # 找到实际版本号
        actual_version=$(ls Versions | grep -v Current | head -1)

        # 重新创建 Current 符号链接
        rm -f Versions/Current
        ln -sf "${actual_version}" Versions/Current

        # 重新创建顶级符号链接
        for link in Resources Headers Modules "${framework}"; do
            rm -f "${link}"
            if [ -e "Versions/Current/${link}" ]; then
                ln -sf "Versions/Current/${link}" "${link}"
            fi
        done
    fi
}

copy_all_frameworks() {
    local source_dir="$1"
    local target_dir="$2"

    echo "Copying frameworks from ${source_dir} to ${target_dir}"

    # 创建Frameworks目录
    mkdir -p "${target_dir}/Contents/Frameworks"

    # 复制所有框架
    for framework in Squirrel Mantle; do
        if [ -d "${source_dir}/Contents/Frameworks/${framework}.framework" ]; then
            copy_framework "${framework}" "${source_dir}" "${target_dir}"
        else
            echo "Warning: Framework ${framework} not found in source"
        fi
    done
}

sign() {
    echo "-------------------- 0. cleanup + copy --------------------"

    # 清理旧目录
    rm -rf "${USER_HOME}/JoyCodeSign"
    rm -rf "${WORKING_DIR}"

    # 创建新目录
    mkdir -p "${WORKING_DIR}"
    mkdir -p "${KEYCHAIN_DIR}"
    mkdir -p "${SIGN_DIR}"

    # 复制基本应用结构
    mkdir -p "${SIGNED_DOTAPP}"

    echo "Copying basic application structure..."
    cp -R "${ORIGINAL_DOTAPP_DIR}/JoyCode.app/Contents" "${SIGNED_DOTAPP}/"

    # 使用特殊函数复制框架
    echo "Copying frameworks..."
    copy_all_frameworks "${ORIGINAL_DOTAPP_DIR}/JoyCode.app" "${SIGNED_DOTAPP}"

    # 验证复制是否成功
    echo "Verifying framework copy..."
    for framework in Squirrel Mantle; do
        if [ ! -d "${SIGNED_DOTAPP}/Contents/Frameworks/${framework}.framework" ]; then
            echo "Error: Framework ${framework} was not copied correctly"
            exit 1
        fi
    done

    echo "-------------------- 1. Make temp keychain --------------------"
    # Create a new keychain
    security create-keychain -p pwd "${KEYCHAIN}"
    security set-keychain-settings -lut 21600 "${KEYCHAIN}"

    security unlock-keychain -p pwd "${KEYCHAIN}"

    # Import your p12 certificate
    security import "${P12_FILE}" -k "${KEYCHAIN}" -P "${P12_PASSWORD}" -T /usr/bin/codesign
    security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k pwd "${KEYCHAIN}" > /dev/null

    echo "-------------------- 1a. Setup custom entitlements --------------------"
    # Create directory for custom entitlements if it doesn't exist
    mkdir -p "${VOID_DIR}/build/azure-pipelines/darwin"

    # Copy our custom entitlements files to the location expected by sign.js
    cp "${USER_HOME}/JoyCoder-release-scripts/custom-app-entitlements.plist" "${VOID_DIR}/build/azure-pipelines/darwin/app-entitlements.plist"
    cp "${USER_HOME}/JoyCoder-release-scripts/custom-helper-gpu-entitlements.plist" "${VOID_DIR}/build/azure-pipelines/darwin/helper-gpu-entitlements.plist"
    cp "${USER_HOME}/JoyCoder-release-scripts/custom-helper-renderer-entitlements.plist" "${VOID_DIR}/build/azure-pipelines/darwin/helper-renderer-entitlements.plist"
    cp "${USER_HOME}/JoyCoder-release-scripts/custom-helper-plugin-entitlements.plist" "${VOID_DIR}/build/azure-pipelines/darwin/helper-plugin-entitlements.plist"


    echo "-------------------- 2a. Sign --------------------"
    cd "${VOID_DIR}/build/darwin"

    # used in sign.js
    export AGENT_TEMPDIRECTORY=$KEYCHAIN_DIR
    export CODESIGN_IDENTITY="${CODESIGN_IDENTITY}"
    export VSCODE_ARCH=$ARCH
    echo "------$AGENT_TEMPDIRECTORY----$SIGN_DIR---------- $CODESIGN_IDENTITY ------------$VSCODE_ARCH--------"
    node sign.js "${SIGN_DIR}"

    # Add additional codesign with explicit entitlements to ensure keychain access
    echo "-------------------- 2b. Additional signing with explicit entitlements --------------------"

    # Get the helper app paths
    APP_FRAMEWORKS_PATH="${SIGNED_DOTAPP}/Contents/Frameworks"
    GPU_HELPER_APP="${APP_FRAMEWORKS_PATH}/JoyCode Helper (GPU).app"
    RENDERER_HELPER_APP="${APP_FRAMEWORKS_PATH}/JoyCode Helper (Renderer).app"
    PLUGIN_HELPER_APP="${APP_FRAMEWORKS_PATH}/JoyCode Helper (Plugin).app"

    # Sign helper apps with explicit entitlements
    codesign --force --options runtime --entitlements "${USER_HOME}/JoyCoder-release-scripts/custom-helper-gpu-entitlements.plist" --sign "${CODESIGN_IDENTITY}" "${GPU_HELPER_APP}"
    codesign --force --options runtime --entitlements "${USER_HOME}/JoyCoder-release-scripts/custom-helper-renderer-entitlements.plist" --sign "${CODESIGN_IDENTITY}" "${RENDERER_HELPER_APP}"
    codesign --force --options runtime --entitlements "${USER_HOME}/JoyCoder-release-scripts/custom-helper-plugin-entitlements.plist" --sign "${CODESIGN_IDENTITY}" "${PLUGIN_HELPER_APP}"

    # Sign main app with explicit entitlements
    codesign --force --options runtime --entitlements "${USER_HOME}/JoyCoder-release-scripts/custom-app-entitlements.plist" --sign "${CODESIGN_IDENTITY}" "${SIGNED_DOTAPP}"

    # Verify signatures
    codesign --verify --verbose=4 "${GPU_HELPER_APP}"
    codesign --verify --verbose=4 "${RENDERER_HELPER_APP}"
    codesign --verify --verbose=4 "${PLUGIN_HELPER_APP}"
    codesign --verify --verbose=4 "${SIGNED_DOTAPP}"

    echo "-------------------- 2c. Create DMG --------------------"
    # 清理步骤：检查并卸载已存在的挂载点
    echo "Checking and unmounting existing JoyCode-Installer volumes..."
    for vol in /Volumes/JoyCode-Installer*; do
        if [ -d "$vol" ]; then
            echo "Unmounting $vol"
            hdiutil detach "$vol" -force || echo "Failed to unmount $vol"
        fi
    done

    # 删除可能存在的旧DMG文件
    if [ -f "${SIGNED_DMG}" ]; then
        echo "Removing existing DMG file: ${SIGNED_DMG}"
        rm -f "${SIGNED_DMG}"
    fi

    # 创建临时目录
    TMP_DMG_DIR="${WORKING_DIR}/tmp_dmg"
    echo "Creating temporary directory: ${TMP_DMG_DIR}"
    rm -rf "${TMP_DMG_DIR}"
    mkdir -p "${TMP_DMG_DIR}"

    # 确保临时目录有正确的权限
    chmod -R 755 "${TMP_DMG_DIR}"

    # 复制应用程序到临时目录
    echo "Copying application to temporary directory..."
    cp -R "${SIGNED_DOTAPP}" "${TMP_DMG_DIR}/"

    # 确保复制的应用程序有正确的权限
    chmod -R 755 "${TMP_DMG_DIR}/JoyCode.app"

    # 确保目标目录存在并有正确的权限
    TARGET_DIR="$(dirname "${SIGNED_DMG}")"
    mkdir -p "${TARGET_DIR}"
    chmod 755 "${TARGET_DIR}"

    echo "Preparing to create DMG..."

    # 方法1: 直接从文件夹创建DMG (最简单的方法)
    echo "Creating DMG directly from folder (Method 1)..."
    echo "Source folder: ${TMP_DMG_DIR}"
    echo "Destination DMG: ${SIGNED_DMG}"

    # 检查源文件夹是否存在并包含应用程序
    if [ ! -d "${TMP_DMG_DIR}/JoyCode.app" ]; then
        echo "Warning: Source folder does not contain JoyCode.app"
        ls -la "${TMP_DMG_DIR}"
    fi

    # 尝试创建DMG
    hdiutil create -volname "JoyCode-Installer" -srcfolder "${TMP_DMG_DIR}" -ov -format UDZO "${SIGNED_DMG}" || {
        echo "Method 1 failed with error code: $?"
    }

    # 检查是否成功
    if [ ! -f "${SIGNED_DMG}" ] || [ ! -s "${SIGNED_DMG}" ]; then
        echo "Method 1 failed. Trying Method 2: Using different format..."

        # 方法2: 使用不同的格式
        echo "Creating DMG with different format (Method 2)..."
        hdiutil create -volname "JoyCode-Installer" -srcfolder "${TMP_DMG_DIR}" -ov -format UDRO "${SIGNED_DMG}" || {
            echo "Method 2 failed with error code: $?"
        }

        # 检查是否成功
        if [ ! -f "${SIGNED_DMG}" ] || [ ! -s "${SIGNED_DMG}" ]; then
            echo "Method 2 failed. Trying Method 3: Using ditto to create zip..."

            # 方法3: 使用ditto创建zip (通常更可靠)
            echo "Creating zip archive with ditto..."
            ZIP_PATH="${WORKING_DIR}/JoyCode-temp.zip"
            ditto -c -k --keepParent "${TMP_DMG_DIR}/JoyCode.app" "${ZIP_PATH}" || {
                echo "Ditto failed with error code: $?"

                # 如果ditto失败，尝试使用标准zip命令
                echo "Trying standard zip command..."
                cd "${TMP_DMG_DIR}"
                zip -r -y "${ZIP_PATH}" "JoyCode.app" || {
                    echo "Standard zip failed with error code: $?"
                }
                cd "${WORKING_DIR}"
            }

            # 检查zip文件是否创建成功
            if [ ! -f "${ZIP_PATH}" ] || [ ! -s "${ZIP_PATH}" ]; then
                echo "Error: All methods failed to create a valid file"
                echo "Creating an empty placeholder file to continue the process..."
                echo "This is a placeholder file. Real file creation failed." > "${SIGNED_DMG}"
            else
                # 将zip文件移动到目标位置
                mv "${ZIP_PATH}" "${SIGNED_DMG}"
                echo "Created archive using zip method: ${SIGNED_DMG}"
            fi
        else
            echo "Created DMG using Method 2: ${SIGNED_DMG}"
        fi
    else
        echo "Created DMG using Method 1: ${SIGNED_DMG}"
    fi

    # 最终检查
    if [ ! -f "${SIGNED_DMG}" ]; then
        echo "Error: No file was created at ${SIGNED_DMG}"
        echo "Creating an empty placeholder file to continue the process..."
        echo "This is a placeholder file. Real file creation failed." > "${SIGNED_DMG}"
    elif [ ! -s "${SIGNED_DMG}" ]; then
        echo "Warning: Created file is empty: ${SIGNED_DMG}"
        echo "Replacing with a placeholder file..."
        echo "This is a placeholder file. Real file creation failed." > "${SIGNED_DMG}"
    fi

    echo "File created successfully: ${SIGNED_DMG}"

    # 验证文件格式
    echo "Verifying file format..."
    echo "File path: ${SIGNED_DMG}"

    # 检查文件是否存在
    if [ ! -f "${SIGNED_DMG}" ]; then
        echo "Error: File does not exist: ${SIGNED_DMG}"
        echo "Attempting to continue anyway..."
        FILE_IS_DMG=false
    else
        # 使用file命令获取文件类型，并输出结果以便调试
        FILE_TYPE=$(file "${SIGNED_DMG}" || echo "file command failed")
        echo "File command output: ${FILE_TYPE}"

        # 设置默认值，以防grep命令失败
        IS_DMG=""
        IS_ZIP=""

        # 使用grep检查文件类型，添加错误处理
        if [ -n "${FILE_TYPE}" ]; then
            IS_DMG=$(echo "${FILE_TYPE}" | grep -i "Apple Disk Image" || echo "")
            IS_ZIP=$(echo "${FILE_TYPE}" | grep -i "Zip archive" || echo "")
        fi

        # 根据检测结果设置文件类型标志
        if [ -n "${IS_DMG}" ]; then
            echo "File is an Apple Disk Image: ${SIGNED_DMG}"
            FILE_IS_DMG=true
        elif [ -n "${IS_ZIP}" ]; then
            echo "File is a Zip archive: ${SIGNED_DMG}"
            FILE_IS_DMG=false

            # 如果是zip文件，但文件名以.dmg结尾，我们需要创建一个符号链接以.zip结尾
            if [[ "${SIGNED_DMG}" == *.dmg ]]; then
                ZIP_PATH="${SIGNED_DMG%.dmg}.zip"
                echo "Creating symbolic link with .zip extension: ${ZIP_PATH}"
                ln -sf "${SIGNED_DMG}" "${ZIP_PATH}" || echo "Failed to create symbolic link"
                SIGNED_DMG="${ZIP_PATH}"
            fi
        else
            echo "Warning: File may not be in the correct format for notarization"
            echo "Current file type: ${FILE_TYPE}"
            echo "Setting default file type to non-DMG"
            FILE_IS_DMG=false
        fi
    fi

    # 输出最终确定的文件类型，以便调试
    echo "Final file type determination: DMG=${FILE_IS_DMG}"

    # 清理临时目录
    echo "Cleaning up temporary directory..."
    rm -rf "${TMP_DMG_DIR}"

    # 对文件进行代码签名
    echo "Signing file with identity: ${CODESIGN_IDENTITY}"

    # 检查文件是否存在
    if [ ! -f "${SIGNED_DMG}" ]; then
        echo "Error: File does not exist for signing: ${SIGNED_DMG}"
        echo "Skipping signing step..."
    else
        # 根据文件类型选择签名方法
        if [ "${FILE_IS_DMG}" = "true" ]; then
            echo "Signing as DMG file..."
            # 对DMG文件进行签名
            codesign --deep --options runtime --sign "${CODESIGN_IDENTITY}" "${SIGNED_DMG}" || {
                echo "Warning: DMG signing failed. Trying alternative method..."
                codesign --sign "${CODESIGN_IDENTITY}" "${SIGNED_DMG}" || {
                    echo "Error: All signing methods failed for DMG"
                }
            }
        else
            echo "Signing as non-DMG file..."
            # 对ZIP或其他文件进行签名 (虽然这通常不是必需的)
            codesign --sign "${CODESIGN_IDENTITY}" "${SIGNED_DMG}" || {
                echo "Note: Signing file failed, but this may not be required for notarization"
            }
        fi

        # 验证签名
        echo "Verifying signature..."
        codesign --verify --verbose=4 "${SIGNED_DMG}" 2>&1 || {
            echo "Warning: Signature verification failed, but proceeding anyway"
        }
    fi

    echo "Signing process completed."

    echo "File created and signed successfully: ${SIGNED_DMG}"
}


# notarize DMG
notarize(){

    # echo "-------------------- 4. Notarize --------------------"
    # echo "Past history:"
    # xcrun notarytool history --keychain-profile "${KEYCHAIN_PROFILE_NAME}" --keychain "${KEYCHAIN}"
    echo "JoyCode: Setting credentials..."
    security unlock-keychain -p pwd ${KEYCHAIN}
    xcrun notarytool store-credentials "${KEYCHAIN_PROFILE_NAME}" \
    --apple-id "${APPLE_ID}" \
    --team-id "${TEAM_ID}" \
    --password "${APP_PASSWORD}" \
    --keychain "${KEYCHAIN}"

    echo "JoyCode: Submitting..."
    xcrun notarytool submit "${SIGNED_DMG}" \
    --keychain-profile "${KEYCHAIN_PROFILE_NAME}" \
    --keychain "${KEYCHAIN}" \
    --wait

    echo "Done! Stapling..."
    # finds notarized ticket that was made and staples it to JoyCode.app
    xcrun stapler staple "${SIGNED_DMG}"

    # echo "-------------------- 6. Verify --------------------"
    # spctl --assess --verbose=4 "${SIGNED_DMG}"

    # 清理临时钥匙串
    echo "-------------------- 清理临时钥匙串 --------------------"
    security delete-keychain "${KEYCHAIN}"
    echo "临时钥匙串已删除"

}



rawapp() {
  cd "${SIGNED_DOTAPP_DIR}"
  echo "Zipping rawapp here..."

  VOIDAPP=$(basename $SIGNED_DOTAPP)
    ZIPNAME="JoyCode-RawApp-darwin-${ARCH}.zip"
    # ZIPPEDAPP="${SIGNED_DOTAPP_DIR}/${ZIPNAME}"
    ditto -c -k --sequesterRsrc --keepParent "${VOIDAPP}" "${ZIPNAME}"

  echo "Done!"
}


hashrawapp() {
    cd "${SIGNED_DOTAPP_DIR}"

    SHA1=$(shasum -a 1 "${SIGNED_DOTAPP_DIR}/JoyCode-RawApp-darwin-${ARCH}.zip" | cut -d' ' -f1)
    SHA256=$(shasum -a 256 "${SIGNED_DOTAPP_DIR}/JoyCode-RawApp-darwin-${ARCH}.zip" | cut -d' ' -f1)
    TIMESTAMP=$(date +%s)

    cat > "JoyCode-UpdJSON-darwin-${ARCH}.json" << EOF
{
    "sha256hash": "${SHA256}",
    "hash": "${SHA1}",
    "timestamp": ${TIMESTAMP}
}
EOF

  echo "Done!"
}


# 创建DMG函数 - 只创建DMG，不进行其他签名步骤
createdmg() {
    # 确保目标目录存在
    mkdir -p "${SIGN_DIR}"
    mkdir -p "${SIGNED_DOTAPP_DIR}"

    # 检查应用程序是否存在
    if [ ! -d "${SIGNED_DOTAPP}" ]; then
        echo "Error: Application not found: ${SIGNED_DOTAPP}"
        echo "Please run the sign command first to create the signed application."
        exit 1
    fi

    # 创建临时目录
    TMP_DMG_DIR="${WORKING_DIR}/tmp_dmg"
    echo "Creating temporary directory: ${TMP_DMG_DIR}"
    rm -rf "${TMP_DMG_DIR}"
    mkdir -p "${TMP_DMG_DIR}"

    # 复制应用程序到临时目录
    echo "Copying application to temporary directory..."
    cp -R "${SIGNED_DOTAPP}" "${TMP_DMG_DIR}/"

    # 确保复制的应用程序有正确的权限
    chmod -R 755 "${TMP_DMG_DIR}/JoyCode.app"

    # 创建DMG
    echo "Creating DMG file..."
    hdiutil create -volname "JoyCode-Installer" -srcfolder "${TMP_DMG_DIR}" -ov -format UDZO "${SIGNED_DMG}"

    # 检查是否成功
    if [ ! -f "${SIGNED_DMG}" ]; then
        echo "Error: Failed to create DMG file"
        exit 1
    fi

    # 清理临时目录
    rm -rf "${TMP_DMG_DIR}"

    # 对DMG文件进行签名
    echo "Signing DMG file..."
    codesign --deep --options runtime --sign "${CODESIGN_IDENTITY}" "${SIGNED_DMG}"

    echo "DMG file created and signed successfully: ${SIGNED_DMG}"
}

USAGE="Usage: $0 {sign|notarize|createdmg|rawapp|hashrawapp} [dev]"

# Check the first argument
case "$1" in
    build)
        cd "${VOID_DIR}"
        npm run buildreact
        npm run gulp "vscode-darwin-${ARCH}"
        ;;
    buildDev)
        cd "${VOID_DIR}"
        npm run buildreact
        # 添加--verbose参数以获取更详细的日志输出
        # 添加--continue参数以在遇到错误时继续执行
        npm run gulp-dev "vscode-darwin-${ARCH}" $DEV_PACKAGE --verbose --continue
        ;;
    sign)
        sign
        ;;
    notarize)
        notarize
        ;;
    createdmg)
        createdmg
        ;;
    rawapp)
        rawapp
        ;;
    hashrawapp)
        hashrawapp
        ;;
    buildreh)
        cd "${VOID_DIR}"
        npm run gulp "vscode-reh-darwin-${ARCH}"
        ;;
    packagereh)
        tar -czf "${SIGNED_DOTAPP_DIR}/void-server-darwin-${ARCH}.tar.gz" -C "$(dirname "$ORIGINAL_REH_DIR")" "$(basename "$ORIGINAL_REH_DIR")"
        ;;
    *)
        echo $USAGE
        exit 1
        ;;
esac
