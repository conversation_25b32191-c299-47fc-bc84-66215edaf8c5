#!/bin/bash

# 这个脚本用于向JoyCoder.app的Info.plist文件添加环境变量，以禁用钥匙串访问

# 设置工作目录为项目根目录
cd "$(dirname "$0")/.." || exit 1

# 目标目录
TARGET_DIR_NAME=${1:-"JoyCode-darwin-arm64"}
DESKTOP_DIR="$(dirname $PWD)"
TARGET_BASE_DIR="${DESKTOP_DIR}/${TARGET_DIR_NAME}"
INFO_PLIST_PATH="${TARGET_BASE_DIR}/JoyCode.app/Contents/Info.plist"

echo "路径信息:"
echo "当前项目目录: $PWD"
echo "目标父目录: $DESKTOP_DIR"
echo "目标应用目录: $TARGET_BASE_DIR"
echo "Info.plist路径: $INFO_PLIST_PATH"

# 检查Info.plist文件是否存在
if [ ! -f "$INFO_PLIST_PATH" ]; then
    echo "错误: Info.plist文件不存在: $INFO_PLIST_PATH"
    exit 1
fi

# 备份Info.plist文件
cp "$INFO_PLIST_PATH" "${INFO_PLIST_PATH}.backup"
echo "已备份Info.plist文件到: ${INFO_PLIST_PATH}.backup"

# 检查是否已存在LSEnvironment键
if /usr/libexec/PlistBuddy -c "Print :LSEnvironment" "$INFO_PLIST_PATH" &>/dev/null; then
    echo "LSEnvironment键已存在，将添加或更新环境变量"
else
    echo "LSEnvironment键不存在，将创建新键"
    /usr/libexec/PlistBuddy -c "Add :LSEnvironment dict" "$INFO_PLIST_PATH"
fi

# 添加或更新环境变量
echo "添加环境变量以禁用钥匙串访问..."
/usr/libexec/PlistBuddy -c "Add :LSEnvironment:VSCODE_USE_INMEMORY_SECRETSTORAGE string 1" "$INFO_PLIST_PATH" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :LSEnvironment:VSCODE_USE_INMEMORY_SECRETSTORAGE 1" "$INFO_PLIST_PATH"

# 添加其他环境变量
/usr/libexec/PlistBuddy -c "Add :LSEnvironment:VSCODE_SKIP_RELEASE_NOTES string 1" "$INFO_PLIST_PATH" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :LSEnvironment:VSCODE_SKIP_RELEASE_NOTES 1" "$INFO_PLIST_PATH"

# 禁用屏幕阅读器
/usr/libexec/PlistBuddy -c "Add :LSEnvironment:VSCODE_ACCESSIBILITY_FORCE_DISABLED string 1" "$INFO_PLIST_PATH" 2>/dev/null || \
/usr/libexec/PlistBuddy -c "Set :LSEnvironment:VSCODE_ACCESSIBILITY_FORCE_DISABLED 1" "$INFO_PLIST_PATH"

echo "环境变量已添加到Info.plist文件"

# 使Info.plist文件生效
echo "使Info.plist文件生效..."
touch "$TARGET_BASE_DIR/JoyCode.app"

echo "完成！JoyCoder.app已配置为禁用钥匙串访问和屏幕阅读器功能"
