#!/bin/bash
set -e  # 遇到错误立即退出
set -x  # 打印执行的每条命令

# 构建React部分
echo "开始构建React部分..."
npm run buildreact || { echo "构建React失败"; exit 1; }

echo "上一条命令的退出状态: $?"
# 构建Linux版本
echo "开始构建Linux版本..."
npm run gulp-dev vscode-reh-linux-x64 || { echo "构建Linux版本失败"; exit 1; }

sleep 1

# 更新commit信息（如果需要）
echo "更新commit信息..."
npm run update-commit bf58862d8d60ad8747b8588ad1b8c1ae956ddad6 || { echo "更新commit信息失败"; exit 1; }

sleep 1
echo "上一条命令的退出状态: $?"
# 检查目标目录是否存在
TARGET_DIR="../../vscode-reh-linux-x64"
if [ ! -d "$TARGET_DIR" ]; then
  echo "错误: 目标目录 $TARGET_DIR 不存在"
  exit 1
fi

# 在打包前等待一段时间，让文件系统稳定下来
echo "等待文件系统稳定..."
sleep 5

echo "上一条命令的退出状态: $?"
# 尝试找出并终止可能正在访问目标目录的进程
echo "尝试终止可能正在访问目标目录的进程..."
lsof +D "$TARGET_DIR" 2>/dev/null | grep -v "$$" | awk '{print $2}' | xargs -r kill -TERM 2>/dev/null || true
sleep 2

echo "上一条命令的退出状态: $?"
# 打包文件，使用不同的选项来处理文件变化问题
echo "开始打包文件..."
# 先删除可能存在的旧文件
rm -f joycoder-reh-linux-latest.tar.gz joycoder-reh-linux-latest.tar

# 尝试使用--ignore-failed-read选项
cd "$TARGET_DIR" && tar -czf joycoder-reh-linux-latest.tar.gz . || \
{
  # 如果失败，尝试使用两步法并排除目标文件
  echo "尝试使用备选tar命令..."
  rm -f joycoder-reh-linux-latest.tar joycoder-reh-linux-latest.tar.gz
  tar --exclude='joycoder-reh-linux-latest.tar' -cf joycoder-reh-linux-latest.tar . && \
  gzip -f joycoder-reh-linux-latest.tar || \
  {
    echo "打包文件失败";
    exit 1;
  }
}

echo "构建完成!"
