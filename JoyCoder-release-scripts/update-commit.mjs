import fs from 'fs';
import path from 'path';
import os from 'os';

// Get commit from command line arguments
const commit = process.argv[2];

if (!commit) {
    console.error('Please provide a commit hash: npm run update-commit [commit]');
    process.exit(1);
}

// Determine platform and architecture
const platform = os.platform();
const arch = os.arch();

async function updateProductJson(productPath) {
    try {
        // Read the product.json file
        const productJson = JSON.parse(fs.readFileSync(productPath, 'utf8'));

        // Update the commit
        productJson.commit = commit;

        // Write back to file
        fs.writeFileSync(productPath, JSON.stringify(productJson, null, 2));
        console.log(`Successfully updated commit in ${productPath}`);
    } catch (error) {
        console.error(`Failed to update ${productPath}:`, error.message);
    }
}

// Define paths based on platform
if (platform === 'win32') {
    const winPath = path.join('..', 'VSCode-win32-x64', 'resources', 'app', 'product.json');
    await updateProductJson(winPath);
} else if (platform === 'darwin') {
    if (arch === 'arm64') {
        const armPath = path.join('..', 'VSCode-darwin-arm64', 'JoyCode.app', 'Contents', 'Resources', 'app', 'product.json');
        await updateProductJson(armPath);
    } else {
        const intelPath = path.join('..', 'VSCode-darwin-x64', 'JoyCode.app', 'Contents', 'Resources', 'app', 'product.json');
        await updateProductJson(intelPath);
    }
} else if (platform === 'linux') {
    const intelPath = path.join('..', 'vscode-reh-linux-x64', 'product.json');
    await updateProductJson(intelPath);
} else {
    console.error('Unsupported platform:', platform);
    process.exit(1);
}
