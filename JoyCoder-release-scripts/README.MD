## 自更新参考文档
[自更新issues](https://github.com/voideditor/void/issues/153)
[自更新github文档](https://github.com/voideditor/void-updates-server)
[手撸 Electron 自动更新，再繁琐也要搞懂它](https://juejin.cn/post/7302724955700264999)
[Auto Update](https://www.electron.build/auto-update)
[证书导出文档](https://www.cnblogs.com/qirui/p/8327812.html)
[自更新步骤](https://github.com/jrainlau/wonderland)
[自更新博客](https://github.com/jrainlau/blog-articles/issues/36)


## 接口出参
### 命令执行完成后会生成 sha512、size、url、path为文件名、url务必编码

参考数据
```json
{
  "version": "0.1.0",
  "files": [
    {
      "url": "https%3A%2F%2Fjoycoder.s3.cn-north-1.jdcloud-oss.com%2Fjoycoder-ide%2F0.1.0%2Fdarwin-arm64%2FJoyCoder-RawApp-darwin-arm64_0.1.0.zip",
      "sha512": "9adc979922e2e2b8b172ddf09ebc9e30b7296f6002b5fb7dc630b41d696b2c0170c35ef848b6deb3eb2265512917090c1b19601fc565ca6530a326ee8002a644",
      "size": "150847696"
    }
  ],
  "path": "JoyCode-RawApp-darwin-arm64_0.1.0.zip"
}
```
