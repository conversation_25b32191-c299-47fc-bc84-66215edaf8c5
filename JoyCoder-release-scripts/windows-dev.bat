@echo off
REM Windows批处理文件，执行与windows.sh相同的操作

echo 开始构建JoyCoder Windows版本...

REM 构建React部分
call npm run buildreact
if %errorlevel% neq 0 (
    echo React构建失败！
    exit /b %errorlevel%
)

REM 构建主应用
call npm run gulp-dev vscode-win32-x64-min
if %errorlevel% neq 0 (
    echo 主应用构建失败！
    exit /b %errorlevel%
)

REM 更改图标和构建更新器
call npm run gulp vscode-win32-x64-inno-updater
if %errorlevel% neq 0 (
    echo 更新器构建失败！
    exit /b %errorlevel%
)

REM 修改commit
call npm run update-commit bf58862d8d60ad8747b8588ad1b8c1ae956ddad6
if %errorlevel% neq 0 (
    echo 更新commit失败！
    exit /b %errorlevel%
)

REM 移动扩展
call npm run move:extension -- joycoder-editor- joycoder VSCode-win32-x64 joycoder.joycoder-editor
if %errorlevel% neq 0 (
    echo 移动joycoder-editor扩展失败！
    exit /b %errorlevel%
)

call npm run move:extension -- clouddev- jdcom VSCode-win32-x64 jdcom.clouddev
if %errorlevel% neq 0 (
    echo 移动clouddev扩展失败！
    exit /b %errorlevel%
)

REM 构建系统安装程序（运行两次）
echo 构建系统安装程序（第一次）...
call npm run gulp vscode-win32-x64-system-setup
if %errorlevel% neq 0 (
    echo 系统安装程序构建失败（第一次）！
    exit /b %errorlevel%
)

echo 构建系统安装程序（第二次）...
call npm run gulp vscode-win32-x64-system-setup
if %errorlevel% neq 0 (
    echo 系统安装程序构建失败（第二次）！
    exit /b %errorlevel%
)

echo 构建完成！输出文件位于.build/目录中。
