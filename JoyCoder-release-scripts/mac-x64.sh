# Do not run this unless you know what you're doing.
# Don't run this when JoyCode is open, or <PERSON> will confuse the two versions (run in terminal or VS Code).

set -e

./import_certificates.sh 20241218

# Build, sign and package x64
./mac-sign.sh build x64
npm run update-commit bf58862d8d60ad8747b8588ad1b8c1ae956ddad6
./cp-vsix.sh joycoder-editor- joycoder
./cp-vsix.sh clouddev- jdcom
./move-joycoder-extension.sh VSCode-darwin-x64 joycoder.joycoder-editor
./move-joycoder-extension.sh VSCode-darwin-x64 jdcom.clouddev
# ./mac-sign.sh buildreh x64
./mac-x64-sign.sh sign x64
./mac-x64-sign.sh notarize x64
./mac-x64-sign.sh rawapp x64
# ./mac-sign.sh hashrawapp x64
# ./mac-sign.sh packagereh x64


# TODO: 1. make sure .zip is signed, 2. recursively codesign app
