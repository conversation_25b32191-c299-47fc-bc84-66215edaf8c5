#!/bin/bash
# 开发环境构建脚本，不创建通用应用

set -e

./import_certificates.sh 20241218
# To fix /Volumes/JoyCode errors, DO NOT RUN IN VOID!!!
# To fix permission errors, sudo chmod -r +rwx ~/Desktop/void
# Run in sudo if have errors
# Build, sign and package arm64
./mac-sign.sh build arm64 dev
./cp-vsix.sh joycoder-editor- joycoder
./cp-vsix.sh clouddev- jdcom
./move-joycoder-extension.sh VSCode-darwin-arm64 joycoder.joycoder-editor
./move-joycoder-extension.sh VSCode-darwin-arm64 jdcom.clouddev

# ./mac-sign.sh sign arm64
# ./mac-sign.sh notarize arm64

echo "-------------------- build dev app --------------------"
