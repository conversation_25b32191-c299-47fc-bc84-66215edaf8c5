/*!--------------------------------------------------------
 * Copyright (c) JoyCode. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 *-------------------------------------------------------*/

import * as vscode from 'vscode';
import * as l10n from '@vscode/l10n';

export async function activate(context: vscode.ExtensionContext) {
	console.log('JoyCode L10n extension is now active');

	// 初始化l10n
	await l10n.config({
		fsPath: context.extensionPath
	});

	// 注册命令以更新文本
	const disposable = vscode.commands.registerCommand('joycode-l10n.updateTexts', () => {
		updateTexts();
	});

	context.subscriptions.push(disposable);

	// 监听配置变化
	context.subscriptions.push(
		vscode.workspace.onDidChangeConfiguration(e => {
			if (e.affectsConfiguration('joycode.language')) {
				const config = vscode.workspace.getConfiguration('joycode');
				const language = config.get<string>('language');
				if (language) {
					// 更新文本
					updateTexts();
					// 提示用户需要重启以应用语言更改
					vscode.window.showInformationMessage(
						l10n.t('joycode.settings.language.confirm', language),
						l10n.t('joycode.settings.language.yes')
					).then(selection => {
						if (selection === l10n.t('joycode.settings.language.yes')) {
							vscode.commands.executeCommand('workbench.action.reloadWindow');
						}
					});
				}
			}
		})
	);

	// 初始更新文本
	updateTexts();
}

function updateTexts() {
	// 使用l10n.t()获取翻译文本
	const translations = {
		login: l10n.t('joycode.settings.menu.login'),
		checkUpdate: l10n.t('joycode.settings.menu.checkUpdate'),
		projectSettings: l10n.t('joycode.settings.menu.projectSettings'),
		settings: l10n.t('joycode.settings.menu.settings'),
		joycodeSettings: l10n.t('joycode.settings.menu.joycodeSettings'),
		theme: l10n.t('joycode.settings.menu.theme'),
		language: l10n.t('joycode.settings.menu.language'),
		keybindings: l10n.t('joycode.settings.menu.keybindings'),
		extensions: l10n.t('joycode.settings.menu.extensions'),
		about: l10n.t('joycode.settings.menu.about'),
		releaseNote: l10n.t('joycode.settings.menu.releaseNote'),
		logout: l10n.t('joycode.settings.menu.logout'),
		error: l10n.t('joycode.settings.menu.error') || 'Error'
	};

	console.log('Updating l10n texts:', translations);

	// 使用正确的 context key 设置
	vscode.commands.executeCommand('setContext', 'joycode.l10n.texts', translations);
}

export function deactivate() { }
