{"name": "joycode-l10n", "displayName": "JoyCode L10n", "description": "Internationalization support for JoyCode", "version": "0.0.1", "publisher": "joycoder", "private": true, "engines": {"vscode": "^1.80.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "l10n": "./l10n", "contributes": {"configuration": {"title": "JoyCode L10n", "properties": {"joycode.language": {"type": "string", "default": "en", "enum": ["en", "zh-cn"], "enumDescriptions": ["English", "简体中文"], "description": "Language for JoyCode interface"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts"}, "devDependencies": {"@types/node": "16.x", "@types/vscode": "^1.99.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vscode/l10n": "^0.0.18", "eslint": "^8.45.0", "typescript": "^5.8.3"}}