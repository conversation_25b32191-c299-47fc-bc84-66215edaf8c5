"use strict";
/*!--------------------------------------------------------
 * Copyright (c) JoyCoder. All rights reserved.
 * Licensed under the MIT License. See License.txt in the project root for license information.
 *-------------------------------------------------------*/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const l10n = __importStar(require("@vscode/l10n"));
async function activate(context) {
    console.log('JoyCode L10n extension is now active');
    // 初始化l10n
    await l10n.config({
        fsPath: context.extensionPath
    });
    // 注册命令以更新文本
    const disposable = vscode.commands.registerCommand('joycode-l10n.updateTexts', () => {
        updateTexts();
    });
    context.subscriptions.push(disposable);
    // 监听配置变化
    context.subscriptions.push(vscode.workspace.onDidChangeConfiguration(e => {
        if (e.affectsConfiguration('joycode.language')) {
            const config = vscode.workspace.getConfiguration('joycode');
            const language = config.get('language');
            if (language) {
                // 更新文本
                updateTexts();
                // 提示用户需要重启以应用语言更改
                vscode.window.showInformationMessage(l10n.t('joycode.settings.language.confirm', language), l10n.t('joycode.settings.language.yes')).then(selection => {
                    if (selection === l10n.t('joycode.settings.language.yes')) {
                        vscode.commands.executeCommand('workbench.action.reloadWindow');
                    }
                });
            }
        }
    }));
    // 初始更新文本
    updateTexts();
}
function updateTexts() {
    // 使用l10n.t()获取翻译文本
    const translations = {
        login: l10n.t('joycode.settings.menu.login'),
        checkUpdate: l10n.t('joycode.settings.menu.checkUpdate'),
        projectSettings: l10n.t('joycode.settings.menu.projectSettings'),
        settings: l10n.t('joycode.settings.menu.settings'),
        joycodeSettings: l10n.t('joycode.settings.menu.joycodeSettings'),
        theme: l10n.t('joycode.settings.menu.theme'),
        language: l10n.t('joycode.settings.menu.language'),
        keybindings: l10n.t('joycode.settings.menu.keybindings'),
        extensions: l10n.t('joycode.settings.menu.extensions'),
        about: l10n.t('joycode.settings.menu.about'),
        releaseNote: l10n.t('joycode.settings.menu.releaseNote'),
        logout: l10n.t('joycode.settings.menu.logout')
    };
    // 通知需要更新UI
    vscode.commands.executeCommand('setContext', 'joycode.l10n.texts', translations);
}
function deactivate() { }
//# sourceMappingURL=extension.js.map