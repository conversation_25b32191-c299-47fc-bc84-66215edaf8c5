{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";AAAA;;;2DAG2D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAK3D,4BAwCC;AAuBD,gCAAgC;AAlEhC,+CAAiC;AACjC,mDAAqC;AAE9B,KAAK,UAAU,QAAQ,CAAC,OAAgC;IAC9D,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IAEpD,UAAU;IACV,MAAM,IAAI,CAAC,MAAM,CAAC;QACjB,MAAM,EAAE,OAAO,CAAC,aAAa;KAC7B,CAAC,CAAC;IAEH,YAAY;IACZ,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,0BAA0B,EAAE,GAAG,EAAE;QACnF,WAAW,EAAE,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAEvC,SAAS;IACT,OAAO,CAAC,aAAa,CAAC,IAAI,CACzB,MAAM,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAC,CAAC,EAAE;QAC7C,IAAI,CAAC,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAChD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAS,UAAU,CAAC,CAAC;YAChD,IAAI,QAAQ,EAAE,CAAC;gBACd,OAAO;gBACP,WAAW,EAAE,CAAC;gBACd,kBAAkB;gBAClB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CACnC,IAAI,CAAC,CAAC,CAAC,mCAAmC,EAAE,QAAQ,CAAC,EACrD,IAAI,CAAC,CAAC,CAAC,+BAA+B,CAAC,CACvC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBAClB,IAAI,SAAS,KAAK,IAAI,CAAC,CAAC,CAAC,+BAA+B,CAAC,EAAE,CAAC;wBAC3D,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;oBACjE,CAAC;gBACF,CAAC,CAAC,CAAC;YACJ,CAAC;QACF,CAAC;IACF,CAAC,CAAC,CACF,CAAC;IAEF,SAAS;IACT,WAAW,EAAE,CAAC;AACf,CAAC;AAED,SAAS,WAAW;IACnB,mBAAmB;IACnB,MAAM,YAAY,GAAG;QACpB,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,6BAA6B,CAAC;QAC5C,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,mCAAmC,CAAC;QACxD,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC,uCAAuC,CAAC;QAChE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,gCAAgC,CAAC;QAClD,eAAe,EAAE,IAAI,CAAC,CAAC,CAAC,uCAAuC,CAAC;QAChE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,6BAA6B,CAAC;QAC5C,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,gCAAgC,CAAC;QAClD,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,mCAAmC,CAAC;QACxD,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC,kCAAkC,CAAC;QACtD,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,6BAA6B,CAAC;QAC5C,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,mCAAmC,CAAC;QACxD,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,8BAA8B,CAAC;KAC9C,CAAC;IAEF,WAAW;IACX,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,oBAAoB,EAAE,YAAY,CAAC,CAAC;AAClF,CAAC;AAED,SAAgB,UAAU,KAAK,CAAC"}