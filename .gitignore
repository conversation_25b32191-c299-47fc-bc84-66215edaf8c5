# 操作系统文件
.DS_Store
Thumbs.db

# 项目配置文件
.env
product.overrides.json

# idea 配置文件
.idea
.idea/*
.idea/**/*

# vscode 配置文件
.vscode/extensions/**/out/
.vscode-test

# Node.js 相关文件
npm-debug.log
node_modules/
node_modules/**/*.*
node_modules/**/*
node_modules/.package-lock.json
node_modules/.*.json

# 构建和编译产物
.build/
/out*/
/extensions/**/out/
build/node_modules
coverage/
test_data/
test-results/
test-results.xml
vscode.lsif
vscode.db
.tmp/
JoyCode.dmg
JoyCoderSign/
JoyCodeSign/
extensions/**/dist/

# 特定文件和目录
.profile-oss
/.profile-oss
/cli/target
/cli/openssl
*.snap.actual
.cache
/src/vs/workbench/contrib/JoyCoder/browser/react/out/
/src/vs/workbench/contrib/JoyCoder/browser/react/src2/
/src/vs/workbench/contrib/JoyCoder/browser/react/node_modules/
# /src/vs/workbench/contrib/JoyCoder/browser/react/package-lock.json
# /src/vs/workbench/contrib/JoyCoder/browser/react/tsup.config.js
# /src/vs/workbench/contrib/JoyCoder/browser/react/build.js

# 内部扩展目录
jd-inner-extensions/

