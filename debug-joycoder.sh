#!/bin/bash

# 这个脚本用于启动 JoyCode 并启用调试功能

# 确保脚本在错误时退出
set -e

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# 创建必要的目录
mkdir -p "$SCRIPT_DIR/.profile-oss/crashes"
mkdir -p "$SCRIPT_DIR/.build/telemetry"

# 设置环境变量
export VSCODE_DEV=1
export VSCODE_CLI=1
export VSCODE_EXTHOST_WILL_SEND_SOCKET=
export VSCODE_SKIP_PRELAUNCH=1

# 启动 JoyCode 并启用调试
"$SCRIPT_DIR/scripts/code.sh" \
  --inspect-brk=5875 \
  --remote-debugging-port=9122 \
  --no-cached-data \
  --crash-reporter-directory="$SCRIPT_DIR/.profile-oss/crashes" \
  --disable-features=CalculateNativeWinOcclusion \
  --reuse-window \
  "$@"

echo "JoyCode 已启动，调试端口："
echo "  - 主进程: 5875"
echo "  - 渲染进程: 9122"
echo "现在您可以在 VS Code 中连接到这些端口进行调试。"
